"""
依从性分析服务单元测试

测试ComplianceAnalytics类的各项功能，包括统计分析、
趋势分析和报告生成。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock
from collections import namedtuple

from src.services.compliance_analytics import (
    ComplianceAnalytics,
    ComplianceStatistics,
    ComplianceTrend
)
from src.modules.screening.compliance_model import ComplianceModel, ScreeningEvent
from src.modules.screening.temporal_compliance import TemporalComplianceModel
from src.modules.screening.enums import ScreeningToolType, ScreeningResult
from src.core.individual import Individual
from src.core.enums import Gender


class TestComplianceStatistics:
    """测试ComplianceStatistics数据类"""
    
    def test_compliance_statistics_creation(self):
        """测试依从性统计数据创建"""
        stats = ComplianceStatistics(
            total_individuals=100,
            compliant_individuals=75,
            compliance_rate=0.75,
            mean_interval_days=365.0,
            median_interval_days=360.0,
            std_interval_days=30.0
        )
        
        assert stats.total_individuals == 100
        assert stats.compliant_individuals == 75
        assert stats.compliance_rate == 0.75
        assert stats.mean_interval_days == 365.0
        assert stats.median_interval_days == 360.0
        assert stats.std_interval_days == 30.0


class TestComplianceTrend:
    """测试ComplianceTrend数据类"""
    
    def test_compliance_trend_creation(self):
        """测试依从性趋势数据创建"""
        trend = ComplianceTrend(
            time_period="2023",
            compliance_rate=0.65,
            sample_size=200,
            confidence_interval=(0.60, 0.70),
            trend_direction="increasing"
        )
        
        assert trend.time_period == "2023"
        assert trend.compliance_rate == 0.65
        assert trend.sample_size == 200
        assert trend.confidence_interval == (0.60, 0.70)
        assert trend.trend_direction == "increasing"


class TestComplianceAnalytics:
    """测试ComplianceAnalytics类"""
    
    @pytest.fixture
    def mock_compliance_model(self):
        """提供模拟的依从性模型"""
        return Mock(spec=ComplianceModel)
    
    @pytest.fixture
    def mock_temporal_model(self):
        """提供模拟的时间趋势模型"""
        return Mock(spec=TemporalComplianceModel)
    
    @pytest.fixture
    def analytics_service(self, mock_compliance_model, mock_temporal_model):
        """提供依从性分析服务实例"""
        return ComplianceAnalytics(mock_compliance_model, mock_temporal_model)
    
    @pytest.fixture
    def sample_population(self):
        """提供测试用的人群数据"""
        # 创建一个简单的人群对象模拟
        MockPopulation = namedtuple('Population', ['individuals'])
        
        individuals = [
            Individual(birth_year=1970, gender=Gender.MALE, individual_id="ind_001"),
            Individual(birth_year=1975, gender=Gender.FEMALE, individual_id="ind_002"),
            Individual(birth_year=1965, gender=Gender.MALE, individual_id="ind_003")
        ]
        
        return MockPopulation(individuals=individuals)
    
    @pytest.fixture
    def sample_screening_events(self):
        """提供测试用的筛查事件"""
        base_date = datetime.now() - timedelta(days=365)
        
        return [
            ScreeningEvent(
                date=base_date,
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id="ind_001"
            ),
            ScreeningEvent(
                date=base_date + timedelta(days=180),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.POSITIVE,
                individual_id="ind_001"
            ),
            ScreeningEvent(
                date=base_date + timedelta(days=90),
                tool_type=ScreeningToolType.COLONOSCOPY,
                result=ScreeningResult.NEGATIVE,
                individual_id="ind_002"
            )
        ]
    
    def test_analytics_initialization(self, mock_compliance_model, mock_temporal_model):
        """测试分析服务初始化"""
        analytics = ComplianceAnalytics(mock_compliance_model, mock_temporal_model)
        
        assert analytics.compliance_model == mock_compliance_model
        assert analytics.temporal_model == mock_temporal_model
        assert isinstance(analytics._cache, dict)
    
    def test_generate_compliance_report_no_data(self, analytics_service, sample_population):
        """测试无数据情况下的报告生成"""
        # 模拟无筛查事件
        analytics_service._collect_screening_events = Mock(return_value=[])
        
        report = analytics_service.generate_compliance_report(sample_population)
        
        assert report['status'] == 'no_data'
        assert 'message' in report
        assert report['population_size'] == 3
    
    def test_calculate_overall_statistics_empty(self, analytics_service, sample_population):
        """测试空数据的总体统计计算"""
        stats = analytics_service._calculate_overall_statistics(sample_population, [])
        
        assert stats.total_individuals == 3
        assert stats.compliant_individuals == 0
        assert stats.compliance_rate == 0.0
        assert stats.mean_interval_days == 0.0
        assert stats.median_interval_days == 0.0
        assert stats.std_interval_days == 0.0
    
    def test_calculate_overall_statistics_with_data(self, analytics_service, sample_population, sample_screening_events):
        """测试有数据的总体统计计算"""
        stats = analytics_service._calculate_overall_statistics(sample_population, sample_screening_events)
        
        assert stats.total_individuals == 3
        assert stats.compliant_individuals == 2  # ind_001 和 ind_002 有筛查记录
        assert stats.compliance_rate == 2/3
        assert stats.mean_interval_days > 0  # ind_001 有两次筛查，有间隔
    
    def test_calculate_tool_specific_rates(self, analytics_service, sample_screening_events):
        """测试工具特异性依从性率计算"""
        rates = analytics_service._calculate_tool_specific_rates(sample_screening_events)
        
        # 3个事件中，2个FIT，1个COLONOSCOPY
        assert rates['fecal_immunochemical_test'] == 2/3
        assert rates['colonoscopy'] == 1/3
    
    def test_analyze_by_tool_type(self, analytics_service, sample_screening_events):
        """测试按工具类型分析"""
        analysis = analytics_service._analyze_by_tool_type(sample_screening_events)
        
        # 验证FIT分析
        fit_analysis = analysis['fecal_immunochemical_test']
        assert fit_analysis['unique_individuals'] == 1  # 只有ind_001
        assert fit_analysis['total_events'] == 2
        assert fit_analysis['positive_rate'] == 50.0  # 2个事件中1个阳性
        
        # 验证COLONOSCOPY分析
        colonoscopy_analysis = analysis['colonoscopy']
        assert colonoscopy_analysis['unique_individuals'] == 1  # 只有ind_002
        assert colonoscopy_analysis['total_events'] == 1
        assert colonoscopy_analysis['positive_rate'] == 0.0  # 无阳性结果
    
    def test_calculate_intervals_for_events(self, analytics_service, sample_screening_events):
        """测试事件间隔计算"""
        # 只取ind_001的事件（有两次筛查）
        ind_001_events = [e for e in sample_screening_events if e.individual_id == "ind_001"]
        
        intervals = analytics_service._calculate_intervals_for_events(ind_001_events)
        
        assert len(intervals) == 1  # 两次筛查产生一个间隔
        assert intervals[0] == 180  # 180天间隔
    
    def test_analyze_by_gender(self, analytics_service, sample_population, sample_screening_events):
        """测试按性别分析"""
        individual_map = {ind.individual_id: ind for ind in sample_population.individuals}
        
        gender_analysis = analytics_service._analyze_by_gender(individual_map, sample_screening_events)
        
        # 验证男性分析（ind_001 和 ind_003，但只有ind_001有筛查记录）
        male_analysis = gender_analysis['male']
        assert male_analysis['unique_individuals'] == 1
        assert male_analysis['total_events'] == 2
        
        # 验证女性分析（ind_002）
        female_analysis = gender_analysis['female']
        assert female_analysis['unique_individuals'] == 1
        assert female_analysis['total_events'] == 1
    
    def test_analyze_by_age_group(self, analytics_service, sample_population, sample_screening_events):
        """测试按年龄组分析"""
        individual_map = {ind.individual_id: ind for ind in sample_population.individuals}
        
        age_analysis = analytics_service._analyze_by_age_group(individual_map, sample_screening_events)
        
        # 验证年龄组分析（基于出生年份1970, 1975, 1965）
        # 当前年龄大约为55, 50, 60
        # 但只有ind_001(55岁)和ind_002(50岁)有筛查记录，所以只有50-59年龄组
        assert '50-59' in age_analysis
        # ind_003(60岁)没有筛查记录，所以60-69年龄组不会出现
        assert len(age_analysis) >= 1
    
    def test_analyze_temporal_trends(self, analytics_service, sample_screening_events):
        """测试时间趋势分析"""
        trends = analytics_service._analyze_temporal_trends(sample_screening_events)
        
        assert len(trends) > 0
        for trend in trends:
            assert isinstance(trend, ComplianceTrend)
            assert trend.compliance_rate >= 0
            assert trend.sample_size >= 0
            assert len(trend.confidence_interval) == 2
    
    def test_assess_data_quality_empty(self, analytics_service):
        """测试空数据的数据质量评估"""
        quality = analytics_service._assess_data_quality([])
        
        assert quality['status'] == 'no_data'
        assert quality['completeness'] == 0.0
    
    def test_assess_data_quality_good(self, analytics_service, sample_screening_events):
        """测试良好数据的质量评估"""
        quality = analytics_service._assess_data_quality(sample_screening_events)
        
        assert quality['status'] in ['good', 'fair', 'poor']
        assert 0.0 <= quality['completeness'] <= 1.0
        assert quality['total_events'] == len(sample_screening_events)
        assert quality['unique_individuals'] == 2  # ind_001 和 ind_002
    
    def test_predict_compliance_improvement(self, analytics_service, sample_population):
        """测试依从性改善预测"""
        # 模拟基线报告
        baseline_report = {
            'overall_statistics': {
                'compliance_rate': 0.6
            }
        }
        analytics_service.generate_compliance_report = Mock(return_value=baseline_report)
        
        intervention = {
            'type': 'education',
            'intensity': 'medium',
            'coverage': 0.8
        }
        
        prediction = analytics_service.predict_compliance_improvement(
            sample_population, intervention
        )
        
        assert 'baseline_rate' in prediction
        assert 'predicted_rate' in prediction
        assert 'absolute_improvement' in prediction
        assert 'relative_improvement' in prediction
        assert 'affected_population' in prediction
        
        # 验证改善效果
        assert prediction['predicted_rate'] > prediction['baseline_rate']
        assert prediction['absolute_improvement'] > 0
        assert prediction['affected_population'] == int(3 * 0.8)  # 3个个体 * 80%覆盖率
    
    def test_generate_population_recommendations(self, analytics_service):
        """测试人群建议生成"""
        # 创建模拟统计数据
        low_compliance_stats = ComplianceStatistics(
            total_individuals=100,
            compliant_individuals=40,
            compliance_rate=0.4,  # 低依从性
            mean_interval_days=365.0,
            median_interval_days=360.0,
            std_interval_days=30.0
        )
        
        tool_analysis = {
            'fecal_immunochemical_test': {
                'positive_rate': 12.0  # 高阳性率
            }
        }
        
        demographic_analysis = {
            'gender': {
                'male': {'events_per_individual': 1.0},
                'female': {'events_per_individual': 2.0}  # 性别差异
            }
        }
        
        recommendations = analytics_service._generate_population_recommendations(
            low_compliance_stats, tool_analysis, demographic_analysis
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # 验证包含低依从性的建议
        low_compliance_found = any('依从性率较低' in rec for rec in recommendations)
        assert low_compliance_found
        
        # 验证包含高阳性率的建议
        high_positive_found = any('阳性率较高' in rec for rec in recommendations)
        assert high_positive_found
        
        # 验证包含性别差异的建议
        gender_diff_found = any('性别间依从性存在差异' in rec for rec in recommendations)
        assert gender_diff_found


if __name__ == "__main__":
    pytest.main([__file__])
