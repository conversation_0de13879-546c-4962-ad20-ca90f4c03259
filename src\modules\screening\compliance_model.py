"""
依从性建模系统

实现筛查依从性的建模和计算，包括基础依从性率配置、
时间趋势建模和诊断性肠镜依从性。
"""

import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum

from .enums import ScreeningToolType, ScreeningResult
from ...core.individual import Individual


class ComplianceFactorType(Enum):
    """依从性影响因素类型枚举"""
    
    AGE = "age"
    GENDER = "gender"
    EDUCATION = "education_level"
    INCOME = "income_level"
    URBAN_RURAL = "urban_rural"
    HEALTH_LITERACY = "health_literacy"
    PREVIOUS_EXPERIENCE = "previous_screening_experience"
    PHYSICIAN_RECOMMENDATION = "physician_recommendation"


@dataclass
class ComplianceParameters:
    """依从性参数配置"""
    
    base_compliance_rate: float           # 基础依从性率
    first_time_multiplier: float         # 首次筛查倍数
    repeat_multiplier: float             # 重复筛查倍数
    positive_followup_rate: float        # 阳性后续检查依从性
    time_decay_factor: float             # 时间衰减因子
    factor_weights: Dict[ComplianceFactorType, float] = field(default_factory=dict)  # 影响因素权重
    
    def __post_init__(self):
        """验证参数有效性"""
        if not (0.0 <= self.base_compliance_rate <= 1.0):
            raise ValueError("基础依从性率必须在0-1之间")
        if not (0.0 <= self.positive_followup_rate <= 1.0):
            raise ValueError("阳性后续检查依从性必须在0-1之间")
        if self.first_time_multiplier < 0:
            raise ValueError("首次筛查倍数不能为负数")
        if self.repeat_multiplier < 0:
            raise ValueError("重复筛查倍数不能为负数")
        if not (0.0 <= self.time_decay_factor <= 1.0):
            raise ValueError("时间衰减因子必须在0-1之间")


@dataclass
class ScreeningEvent:
    """筛查事件记录"""
    
    date: datetime
    tool_type: ScreeningToolType
    result: ScreeningResult
    individual_id: str
    additional_data: Dict[str, Any] = field(default_factory=dict)


class ComplianceModel:
    """
    依从性建模类
    
    管理筛查依从性的计算和配置，支持工具特异性依从性率、
    时间趋势建模和个体因素调整。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化依从性模型
        
        Args:
            config: 依从性配置字典
        """
        self.config = config
        self.base_rates = config.get('base_rates', {})
        self.temporal_patterns = config.get('temporal_patterns', {})
        self.followup_compliance = config.get('followup_compliance', {})
        self.factor_weights = config.get('factor_weights', {})
        
        # 验证配置
        self._validate_config()
    
    def _validate_config(self) -> None:
        """验证配置参数的有效性"""
        required_base_rates = ['fit_screening', 'colonoscopy_screening', 'sigmoidoscopy_screening']
        for rate_key in required_base_rates:
            if rate_key not in self.base_rates:
                raise ValueError(f"缺少必需的基础依从性率配置: {rate_key}")
            
            rate_value = self.base_rates[rate_key]
            if not (0.0 <= rate_value <= 1.0):
                raise ValueError(f"依从性率{rate_key}必须在0-1之间，当前值: {rate_value}")
        
        # 验证时间模式参数
        required_temporal = ['first_time_multiplier', 'repeat_multiplier', 'time_decay_rate']
        for param in required_temporal:
            if param not in self.temporal_patterns:
                raise ValueError(f"缺少必需的时间模式参数: {param}")
    
    def calculate_compliance_probability(
        self, 
        individual: Individual, 
        tool_type: ScreeningToolType,
        screening_history: List[ScreeningEvent]
    ) -> float:
        """
        计算个体筛查依从性概率
        
        Args:
            individual: 个体对象
            tool_type: 筛查工具类型
            screening_history: 筛查历史记录
            
        Returns:
            依从性概率 (0-1之间)
        """
        # 获取基础依从性率
        base_rate = self._get_base_rate_for_tool(tool_type)
        
        # 计算时间趋势因子
        temporal_factor = self._calculate_temporal_factor(screening_history)
        
        # 计算个体因素调整（暂时简化，返回1.0）
        individual_factor = 1.0
        
        # 综合依从性概率
        compliance_prob = base_rate * temporal_factor * individual_factor
        
        # 限制在[0,1]范围内
        return min(max(compliance_prob, 0.0), 1.0)
    
    def _get_base_rate_for_tool(self, tool_type: ScreeningToolType) -> float:
        """获取工具特异性基础依从性率"""
        tool_mapping = {
            ScreeningToolType.FIT: 'fit_screening',
            ScreeningToolType.COLONOSCOPY: 'colonoscopy_screening',
            ScreeningToolType.SIGMOIDOSCOPY: 'sigmoidoscopy_screening',
            ScreeningToolType.CTCOLONOGRAPHY: 'colonoscopy_screening',  # 使用结肠镜的依从性
            ScreeningToolType.STOOL_DNA: 'fit_screening',  # 使用FIT的依从性
        }
        
        rate_key = tool_mapping.get(tool_type, 'fit_screening')
        return self.base_rates.get(rate_key, 0.5)  # 默认50%依从性
    
    def _calculate_temporal_factor(self, screening_history: List[ScreeningEvent]) -> float:
        """
        计算时间趋势因子
        
        Args:
            screening_history: 筛查历史记录
            
        Returns:
            时间趋势调整因子
        """
        if not screening_history:
            # 首次筛查
            return self.temporal_patterns.get('first_time_multiplier', 0.8)
        
        # 计算距离上次筛查的时间
        last_screening = max(screening_history, key=lambda x: x.date)
        time_since_last = (datetime.now() - last_screening.date).days / 365.25
        
        # 时间衰减计算
        decay_rate = self.temporal_patterns.get('time_decay_rate', 0.05)
        decay_factor = (1 - decay_rate) ** time_since_last
        
        # 重复筛查基础倍数
        repeat_multiplier = self.temporal_patterns.get('repeat_multiplier', 1.2)
        
        return repeat_multiplier * decay_factor

    def _get_diagnostic_base_rate(self, tool_type: ScreeningToolType) -> float:
        """
        根据筛查工具类型获取诊断依从性基础率

        Args:
            tool_type: 筛查工具类型

        Returns:
            诊断依从性基础率
        """
        tool_mapping = {
            ScreeningToolType.FIT: 'positive_fit_to_colonoscopy',
            ScreeningToolType.STOOL_DNA: 'positive_stool_dna_to_colonoscopy',
            ScreeningToolType.SIGMOIDOSCOPY: 'positive_fit_to_colonoscopy',  # 使用FIT的配置
            ScreeningToolType.CTCOLONOGRAPHY: 'positive_fit_to_colonoscopy',  # 使用FIT的配置
        }

        rate_key = tool_mapping.get(tool_type, 'positive_fit_to_colonoscopy')
        return self.followup_compliance.get(rate_key, 0.75)  # 默认75%

    def _calculate_waiting_time_impact(self, waiting_time_weeks: float) -> float:
        """
        计算等待时间对依从性的影响

        Args:
            waiting_time_weeks: 等待时间（周）

        Returns:
            等待时间影响值
        """
        if waiting_time_weeks <= 0:
            return 0.0

        # 获取等待时间影响系数
        impact_per_week = self.followup_compliance.get('waiting_time_impact', -0.02)
        max_waiting_weeks = self.followup_compliance.get('max_waiting_weeks', 12)

        # 限制最大等待时间
        effective_waiting_time = min(waiting_time_weeks, max_waiting_weeks)

        return impact_per_week * effective_waiting_time

    def _calculate_multiple_positive_factor(
        self,
        individual: Individual,
        screening_history: List[ScreeningEvent]
    ) -> float:
        """
        计算多次阳性结果对依从性的影响

        Args:
            individual: 个体对象
            screening_history: 筛查历史记录

        Returns:
            多次阳性影响因子
        """
        # 计算历史阳性结果数量
        positive_count = sum(
            1 for event in screening_history
            if event.individual_id == individual.individual_id
            and event.result == ScreeningResult.POSITIVE
        )

        if positive_count <= 1:
            return 1.0  # 首次阳性或无阳性历史

        # 多次阳性的衰减因子
        decay_factor = self.followup_compliance.get('multiple_positive_decay', 0.9)

        # 计算衰减：每次额外的阳性结果都会降低依从性
        return decay_factor ** (positive_count - 1)
    
    def calculate_diagnostic_compliance(
        self,
        individual: Individual,
        positive_result: ScreeningEvent,
        waiting_time_weeks: float = 0.0,
        screening_history: Optional[List[ScreeningEvent]] = None
    ) -> float:
        """
        计算阳性结果后诊断依从性

        Args:
            individual: 个体对象
            positive_result: 阳性筛查结果事件
            waiting_time_weeks: 等待时间（周）
            screening_history: 筛查历史记录（用于计算多次阳性影响）

        Returns:
            诊断依从性概率 (0-1之间)
        """
        # 根据筛查工具类型获取基础诊断依从性率
        base_rate = self._get_diagnostic_base_rate(positive_result.tool_type)

        # 等待时间影响
        waiting_impact = self._calculate_waiting_time_impact(waiting_time_weeks)

        # 多次阳性结果的影响
        multiple_positive_factor = self._calculate_multiple_positive_factor(
            individual, screening_history or []
        )

        # 个体因素调整（暂时简化，返回1.0）
        individual_factor = 1.0

        # 计算最终诊断依从性
        diagnostic_compliance = (
            base_rate + waiting_impact
        ) * multiple_positive_factor * individual_factor

        # 限制在[0,1]范围内
        return min(max(diagnostic_compliance, 0.0), 1.0)
    
    def simulate_compliance_decision(
        self, 
        individual: Individual, 
        tool_type: ScreeningToolType,
        screening_history: List[ScreeningEvent]
    ) -> bool:
        """
        模拟依从性决策
        
        Args:
            individual: 个体对象
            tool_type: 筛查工具类型
            screening_history: 筛查历史记录
            
        Returns:
            是否依从筛查 (True/False)
        """
        compliance_prob = self.calculate_compliance_probability(
            individual, tool_type, screening_history
        )
        
        # 随机抽样决定是否依从
        return random.random() < compliance_prob
    
    def simulate_diagnostic_compliance_decision(
        self,
        individual: Individual,
        positive_result: ScreeningEvent,
        waiting_time_weeks: float = 0.0,
        screening_history: Optional[List[ScreeningEvent]] = None
    ) -> bool:
        """
        模拟诊断依从性决策

        Args:
            individual: 个体对象
            positive_result: 阳性筛查结果事件
            waiting_time_weeks: 等待时间（周）
            screening_history: 筛查历史记录

        Returns:
            是否依从诊断检查 (True/False)
        """
        diagnostic_compliance = self.calculate_diagnostic_compliance(
            individual, positive_result, waiting_time_weeks, screening_history
        )

        # 随机抽样决定是否依从
        return random.random() < diagnostic_compliance

    def calculate_diagnostic_failure_consequences(
        self,
        individual: Individual,
        missed_diagnostic_event: ScreeningEvent,
        delay_months: int = 6
    ) -> Dict[str, Any]:
        """
        计算诊断依从性失败的后果

        Args:
            individual: 个体对象
            missed_diagnostic_event: 错过的诊断事件
            delay_months: 延迟诊断的月数

        Returns:
            后果分析字典
        """
        consequences = {
            'individual_id': individual.individual_id,
            'missed_event_date': missed_diagnostic_event.date,
            'delay_months': delay_months,
            'risk_factors': {
                'disease_progression_risk': self._calculate_progression_risk(delay_months),
                'future_compliance_impact': self._calculate_future_compliance_impact(),
                'cost_implications': self._calculate_cost_implications(delay_months)
            },
            'recommended_actions': self._get_failure_recommendations(delay_months)
        }

        return consequences

    def _calculate_progression_risk(self, delay_months: int) -> float:
        """计算疾病进展风险增加"""
        # 简化模型：每月延迟增加1%的进展风险
        base_risk_increase = 0.01 * delay_months
        return min(base_risk_increase, 0.5)  # 最大50%风险增加

    def _calculate_future_compliance_impact(self) -> float:
        """计算对未来依从性的影响"""
        # 错过诊断会降低未来依从性
        return -0.1  # 降低10%

    def _calculate_cost_implications(self, delay_months: int) -> Dict[str, float]:
        """计算成本影响"""
        return {
            'additional_screening_cost': delay_months * 100,  # 简化计算
            'potential_treatment_cost_increase': delay_months * 500,
            'productivity_loss': delay_months * 200
        }

    def _get_failure_recommendations(self, delay_months: int) -> List[str]:
        """获取失败后的建议措施"""
        recommendations = [
            "安排紧急诊断性肠镜检查",
            "加强患者教育和沟通"
        ]

        if delay_months > 6:
            recommendations.extend([
                "考虑更频繁的随访",
                "评估替代诊断方法"
            ])

        return recommendations
    
    def get_compliance_parameters(self, tool_type: ScreeningToolType) -> ComplianceParameters:
        """
        获取工具特异性依从性参数
        
        Args:
            tool_type: 筛查工具类型
            
        Returns:
            依从性参数对象
        """
        base_rate = self._get_base_rate_for_tool(tool_type)
        
        return ComplianceParameters(
            base_compliance_rate=base_rate,
            first_time_multiplier=self.temporal_patterns.get('first_time_multiplier', 0.8),
            repeat_multiplier=self.temporal_patterns.get('repeat_multiplier', 1.2),
            positive_followup_rate=self.followup_compliance.get('positive_fit_to_colonoscopy', 0.75),
            time_decay_factor=self.temporal_patterns.get('time_decay_rate', 0.05),
            factor_weights={}  # 暂时为空，后续扩展
        )
    
    def validate_compliance_rates(self) -> Dict[str, bool]:
        """
        验证依从性率配置的合理性
        
        Returns:
            验证结果字典
        """
        validation_results = {}
        
        # 验证基础依从性率范围
        for tool, rate in self.base_rates.items():
            validation_results[f"base_rate_{tool}"] = 0.0 <= rate <= 1.0
        
        # 验证时间模式参数
        temporal_params = self.temporal_patterns
        validation_results["first_time_multiplier"] = temporal_params.get('first_time_multiplier', 0) >= 0
        validation_results["repeat_multiplier"] = temporal_params.get('repeat_multiplier', 0) >= 0
        validation_results["time_decay_rate"] = 0.0 <= temporal_params.get('time_decay_rate', 0) <= 1.0
        
        # 验证后续检查依从性
        followup_rate = self.followup_compliance.get('positive_fit_to_colonoscopy', 0)
        validation_results["positive_followup_rate"] = 0.0 <= followup_rate <= 1.0
        
        return validation_results
