"""
筛查结果处理器单元测试

测试筛查结果判定逻辑、假阳性/假阴性处理和质量控制功能。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.core.individual import Individual
from src.core.enums import DiseaseState, Gender
from src.modules.screening import (
    ScreeningResultProcessor, ScreeningResultType, FollowupAction,
    DetailedScreeningResult, ScreeningToolType, ScreeningTool,
    FalseResultType, FalseResultImpact
)


class TestScreeningResultProcessor:
    """筛查结果处理器测试类"""

    @pytest.fixture
    def tool_config(self):
        """筛查工具配置"""
        return {
            'sensitivity_by_state': {
                'normal': 0.0,
                'low_risk_adenoma': 0.3,
                'high_risk_adenoma': 0.7,
                'small_serrated': 0.4,
                'large_serrated': 0.8,
                'preclinical_cancer': 0.9,
                'clinical_cancer': 0.95
            },
            'specificity': 0.9,
            'indeterminate_rate': 0.02,
            'inadequate_rate': 0.01,
            'quality_threshold': 0.8,
            'confidence_threshold': 0.7
        }

    @pytest.fixture
    def processor(self, tool_config):
        """创建筛查结果处理器"""
        return ScreeningResultProcessor(tool_config)

    @pytest.fixture
    def normal_individual(self):
        """正常个体"""
        return Individual(
            birth_year=1970,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.NORMAL
        )

    @pytest.fixture
    def adenoma_individual(self):
        """腺瘤个体"""
        return Individual(
            birth_year=1965,
            gender=Gender.FEMALE,
            initial_disease_state=DiseaseState.HIGH_RISK_ADENOMA
        )

    @pytest.fixture
    def cancer_individual(self):
        """癌症个体"""
        return Individual(
            birth_year=1960,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.CLINICAL_CANCER
        )

    @pytest.fixture
    def mock_tool(self):
        """模拟筛查工具"""
        tool = Mock(spec=ScreeningTool)
        tool.tool_type = ScreeningToolType.FIT
        tool.cost_model = None
        return tool

    def test_processor_initialization(self, tool_config):
        """测试处理器初始化"""
        processor = ScreeningResultProcessor(tool_config)
        
        assert processor.specificity == 0.9
        assert processor.indeterminate_rate == 0.02
        assert processor.inadequate_rate == 0.01
        assert processor.quality_threshold == 0.8
        assert processor.confidence_threshold == 0.7

    def test_detection_probability_calculation(self, processor, adenoma_individual, mock_tool):
        """测试检测概率计算"""
        detection_prob = processor._calculate_detection_probability(
            adenoma_individual, mock_tool
        )
        
        # 高风险腺瘤的基础敏感性是0.7
        assert 0.5 < detection_prob < 1.0  # 考虑个体调整因子

    def test_individual_adjustment_factor(self, processor, mock_tool):
        """测试个体特征调整因子"""
        # 年轻个体
        young_individual = Individual(
            birth_year=2000,
            gender=Gender.FEMALE,
            initial_disease_state=DiseaseState.NORMAL
        )
        
        # 老年个体
        old_individual = Individual(
            birth_year=1940,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.NORMAL
        )
        
        young_adjustment = processor._calculate_individual_adjustment(
            young_individual, mock_tool
        )
        old_adjustment = processor._calculate_individual_adjustment(
            old_individual, mock_tool
        )
        
        # 年轻人调整因子应该略低
        assert young_adjustment < 1.0
        # 老年人调整因子应该略低
        assert old_adjustment < 1.0
        # 调整因子应该在合理范围内
        assert 0.8 <= young_adjustment <= 1.2
        assert 0.8 <= old_adjustment <= 1.2

    @patch('random.random')
    def test_result_type_determination_normal_individual(
        self, mock_random, processor, normal_individual
    ):
        """测试正常个体的结果类型判定"""
        # 测试真阴性（特异性内）
        mock_random.side_effect = [0.05, 0.05, 0.05]  # 不足率、不确定率、特异性测试
        result_type = processor._determine_result_type(
            DiseaseState.NORMAL, 0.0
        )
        assert result_type == ScreeningResultType.NEGATIVE

        # 测试假阳性（特异性外）
        mock_random.side_effect = [0.05, 0.05, 0.95]  # 不足率、不确定率、特异性测试
        result_type = processor._determine_result_type(
            DiseaseState.NORMAL, 0.0
        )
        assert result_type == ScreeningResultType.POSITIVE

    @patch('random.random')
    def test_result_type_determination_disease_individual(
        self, mock_random, processor
    ):
        """测试疾病个体的结果类型判定"""
        # 测试真阳性（敏感性内）
        mock_random.side_effect = [0.05, 0.05, 0.3]  # 不足率、不确定率、敏感性测试
        result_type = processor._determine_result_type(
            DiseaseState.HIGH_RISK_ADENOMA, 0.7
        )
        assert result_type == ScreeningResultType.POSITIVE

        # 测试假阴性（敏感性外）
        mock_random.side_effect = [0.05, 0.05, 0.8]  # 不足率、不确定率、敏感性测试
        result_type = processor._determine_result_type(
            DiseaseState.HIGH_RISK_ADENOMA, 0.7
        )
        assert result_type == ScreeningResultType.NEGATIVE

    @patch('random.random')
    def test_inadequate_result(self, mock_random, processor):
        """测试样本不足结果"""
        mock_random.return_value = 0.005  # 小于inadequate_rate (0.01)
        result_type = processor._determine_result_type(
            DiseaseState.NORMAL, 0.0
        )
        assert result_type == ScreeningResultType.INADEQUATE

    @patch('random.random')
    def test_indeterminate_result(self, mock_random, processor):
        """测试不确定结果"""
        mock_random.side_effect = [0.05, 0.01]  # 不足率通过，不确定率触发
        result_type = processor._determine_result_type(
            DiseaseState.NORMAL, 0.0
        )
        assert result_type == ScreeningResultType.INDETERMINATE

    def test_confidence_score_calculation(self, processor):
        """测试置信度分数计算"""
        # 样本不足
        confidence = processor._calculate_confidence_score(
            0.7, ScreeningResultType.INADEQUATE
        )
        assert confidence == 0.0

        # 不确定结果
        confidence = processor._calculate_confidence_score(
            0.7, ScreeningResultType.INDETERMINATE
        )
        assert confidence == 0.5

        # 阳性结果
        confidence = processor._calculate_confidence_score(
            0.7, ScreeningResultType.POSITIVE
        )
        assert confidence == 0.7

        # 阴性结果
        confidence = processor._calculate_confidence_score(
            0.7, ScreeningResultType.NEGATIVE
        )
        assert abs(confidence - 0.3) < 1e-10

    def test_true_positive_identification(self, processor):
        """测试真阳性识别"""
        # 真阳性
        is_tp = processor._is_true_positive(
            DiseaseState.HIGH_RISK_ADENOMA, ScreeningResultType.POSITIVE
        )
        assert is_tp is True

        # 假阳性
        is_tp = processor._is_true_positive(
            DiseaseState.NORMAL, ScreeningResultType.POSITIVE
        )
        assert is_tp is False

        # 阴性结果
        is_tp = processor._is_true_positive(
            DiseaseState.HIGH_RISK_ADENOMA, ScreeningResultType.NEGATIVE
        )
        assert is_tp is None

    def test_true_negative_identification(self, processor):
        """测试真阴性识别"""
        # 真阴性
        is_tn = processor._is_true_negative(
            DiseaseState.NORMAL, ScreeningResultType.NEGATIVE
        )
        assert is_tn is True

        # 假阴性
        is_tn = processor._is_true_negative(
            DiseaseState.HIGH_RISK_ADENOMA, ScreeningResultType.NEGATIVE
        )
        assert is_tn is False

        # 阳性结果
        is_tn = processor._is_true_negative(
            DiseaseState.NORMAL, ScreeningResultType.POSITIVE
        )
        assert is_tn is None

    def test_followup_action_determination(self, processor):
        """测试后续行动确定"""
        # 阳性结果 - FIT工具
        result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.8
        )
        action = processor._determine_followup_action(result)
        assert action == FollowupAction.DIAGNOSTIC_COLONOSCOPY

        # 阳性结果 - 结肠镜
        result.tool_type = ScreeningToolType.COLONOSCOPY
        action = processor._determine_followup_action(result)
        assert action == FollowupAction.IMMEDIATE_TREATMENT

        # 不确定结果
        result.result_type = ScreeningResultType.INDETERMINATE
        action = processor._determine_followup_action(result)
        assert action == FollowupAction.REPEAT_SCREENING

        # 样本不足
        result.result_type = ScreeningResultType.INADEQUATE
        action = processor._determine_followup_action(result)
        assert action == FollowupAction.REPEAT_SCREENING

        # 阴性结果
        result.result_type = ScreeningResultType.NEGATIVE
        action = processor._determine_followup_action(result)
        assert action == FollowupAction.NONE

    def test_processing_time_calculation(self, processor, mock_tool):
        """测试处理时间计算"""
        processing_time = processor._calculate_processing_time(mock_tool)
        
        # FIT工具的基础时间是5分钟，有随机变异
        assert 4.0 <= processing_time <= 6.0

    def test_screening_cost_calculation(self, processor, mock_tool):
        """测试筛查成本计算"""
        cost = processor._calculate_screening_cost(mock_tool)
        
        # FIT工具的默认成本是50元
        assert cost == 50.0

    def test_quality_score_calculation(self, processor, normal_individual, mock_tool):
        """测试质量分数计算"""
        quality_score = processor._calculate_quality_score(
            normal_individual, mock_tool
        )
        
        # 质量分数应该在合理范围内
        assert 0.0 <= quality_score <= 1.0

    def test_result_quality_validation(self, processor):
        """测试结果质量验证"""
        # 高质量结果
        good_result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.9,
            quality_score=0.95
        )
        assert processor.validate_result_quality(good_result) is True

        # 低质量结果
        poor_result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.5,
            quality_score=0.6
        )
        assert processor.validate_result_quality(poor_result) is False

    def test_complete_screening_process(
        self, processor, adenoma_individual, mock_tool
    ):
        """测试完整筛查处理流程"""
        result = processor.process_screening(
            adenoma_individual, mock_tool
        )
        
        # 验证结果对象
        assert isinstance(result, DetailedScreeningResult)
        assert result.individual_id == adenoma_individual.individual_id
        assert result.tool_type == ScreeningToolType.FIT
        assert result.result_type in [
            ScreeningResultType.POSITIVE, 
            ScreeningResultType.NEGATIVE,
            ScreeningResultType.INDETERMINATE,
            ScreeningResultType.INADEQUATE
        ]
        assert 0.0 <= result.confidence_score <= 1.0
        assert 0.0 <= result.quality_score <= 1.0
        assert result.processing_time > 0
        assert result.cost > 0
        assert result.followup_action in FollowupAction
        
        # 验证额外数据
        assert 'true_disease_state' in result.additional_data
        assert 'detection_probability' in result.additional_data
        assert 'tool_sensitivity' in result.additional_data
        assert 'tool_specificity' in result.additional_data

    def test_screening_result_validation(self):
        """测试筛查结果数据验证"""
        # 有效结果
        valid_result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.8,
            quality_score=0.9,
            processing_time=5.0,
            cost=50.0
        )
        # 应该不抛出异常
        
        # 无效置信度分数
        with pytest.raises(ValueError, match="置信度分数必须在0-1之间"):
            DetailedScreeningResult(
                individual_id="test",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=1.5  # 无效值
            )

        # 无效质量分数
        with pytest.raises(ValueError, match="质量分数必须在0-1之间"):
            DetailedScreeningResult(
                individual_id="test",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8,
                quality_score=-0.1  # 无效值
            )

        # 无效处理时间
        with pytest.raises(ValueError, match="处理时间不能为负数"):
            DetailedScreeningResult(
                individual_id="test",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8,
                processing_time=-1.0  # 无效值
            )

        # 无效成本
        with pytest.raises(ValueError, match="成本不能为负数"):
            DetailedScreeningResult(
                individual_id="test",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8,
                cost=-10.0  # 无效值
            )

    def test_false_result_type_determination(self, processor):
        """测试假结果类型判定"""
        # 假阳性
        false_type = processor._determine_false_result_type(
            DetailedScreeningResult(
                individual_id="test",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8
            ),
            DiseaseState.NORMAL
        )
        assert false_type == FalseResultType.FALSE_POSITIVE

        # 假阴性
        false_type = processor._determine_false_result_type(
            DetailedScreeningResult(
                individual_id="test",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.NEGATIVE,
                test_date=datetime.now(),
                confidence_score=0.8
            ),
            DiseaseState.HIGH_RISK_ADENOMA
        )
        assert false_type == FalseResultType.FALSE_NEGATIVE

        # 真阳性
        false_type = processor._determine_false_result_type(
            DetailedScreeningResult(
                individual_id="test",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8
            ),
            DiseaseState.HIGH_RISK_ADENOMA
        )
        assert false_type == FalseResultType.TRUE_RESULT

    def test_false_positive_impact_calculation(self, processor):
        """测试假阳性影响计算"""
        result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.8
        )

        impact = processor._calculate_false_positive_impact(result)

        assert impact.result_type == FalseResultType.FALSE_POSITIVE
        assert 0.4 <= impact.psychological_impact_score <= 0.7
        assert impact.additional_cost > 0
        assert impact.unnecessary_procedures >= 1
        assert 7 <= impact.anxiety_duration_days <= 30

    def test_false_negative_impact_calculation(self, processor):
        """测试假阴性影响计算"""
        result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.NEGATIVE,
            test_date=datetime.now(),
            confidence_score=0.8
        )

        impact = processor._calculate_false_negative_impact(
            result, DiseaseState.HIGH_RISK_ADENOMA
        )

        assert impact.result_type == FalseResultType.FALSE_NEGATIVE
        assert 0.6 <= impact.psychological_impact_score <= 0.9
        assert impact.additional_cost >= 0
        assert impact.delay_in_detection_days > 0

    def test_false_positive_cost_calculation(self, processor):
        """测试假阳性成本计算"""
        # FIT工具假阳性成本
        cost = processor._calculate_false_positive_cost(ScreeningToolType.FIT)
        assert cost == 1200.0

        # CT工具假阳性成本
        cost = processor._calculate_false_positive_cost(ScreeningToolType.CTCOLONOGRAPHY)
        assert cost == 1500.0

    def test_false_negative_cost_calculation(self, processor):
        """测试假阴性成本计算"""
        # 腺瘤假阴性成本
        cost = processor._calculate_false_negative_cost(
            DiseaseState.HIGH_RISK_ADENOMA, 100
        )
        assert cost == 200.0  # 100天 * 2元/天

        # 癌症假阴性成本
        cost = processor._calculate_false_negative_cost(
            DiseaseState.CLINICAL_CANCER, 50
        )
        assert cost == 1000.0  # 50天 * 20元/天

    def test_detection_delay_calculation(self, processor):
        """测试检测延迟计算"""
        # 低风险腺瘤延迟
        delay = processor._calculate_detection_delay(
            DiseaseState.LOW_RISK_ADENOMA, ScreeningToolType.FIT
        )
        assert 292 <= delay <= 365  # 80%-100% of 365 days

        # 临床癌症延迟
        delay = processor._calculate_detection_delay(
            DiseaseState.CLINICAL_CANCER, ScreeningToolType.FIT
        )
        assert 36 <= delay <= 109  # 10%-30% of 365 days

    def test_false_result_followup_processing(self, processor, normal_individual):
        """测试假结果后续处理"""
        # 假阳性结果
        fp_result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.8,
            false_result_impact=FalseResultImpact(
                result_type=FalseResultType.FALSE_POSITIVE,
                psychological_impact_score=0.6,
                additional_cost=1200.0,
                unnecessary_procedures=1,
                anxiety_duration_days=14
            )
        )

        followup = processor.process_false_result_followup(fp_result, normal_individual)
        assert followup["action"] == "false_positive_management"
        assert "recommended_actions" in followup
        assert followup["psychological_support_needed"] is True

        # 假阴性结果
        fn_result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.NEGATIVE,
            test_date=datetime.now(),
            confidence_score=0.8,
            false_result_impact=FalseResultImpact(
                result_type=FalseResultType.FALSE_NEGATIVE,
                psychological_impact_score=0.8,
                additional_cost=500.0,
                delay_in_detection_days=180
            )
        )

        followup = processor.process_false_result_followup(fn_result, normal_individual)
        assert followup["action"] == "false_negative_management"
        assert followup["urgent_followup_needed"] is True

    def test_false_result_statistics_calculation(self, processor):
        """测试假结果统计计算"""
        # 创建测试结果列表
        results = [
            # 假阳性结果
            DetailedScreeningResult(
                individual_id="fp1",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8,
                false_result_impact=FalseResultImpact(
                    result_type=FalseResultType.FALSE_POSITIVE,
                    psychological_impact_score=0.6,
                    additional_cost=1200.0,
                    unnecessary_procedures=1,
                    anxiety_duration_days=14
                )
            ),
            # 假阴性结果
            DetailedScreeningResult(
                individual_id="fn1",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.NEGATIVE,
                test_date=datetime.now(),
                confidence_score=0.8,
                false_result_impact=FalseResultImpact(
                    result_type=FalseResultType.FALSE_NEGATIVE,
                    psychological_impact_score=0.8,
                    additional_cost=500.0,
                    delay_in_detection_days=180
                )
            ),
            # 真结果（无假结果影响）
            DetailedScreeningResult(
                individual_id="true1",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.NEGATIVE,
                test_date=datetime.now(),
                confidence_score=0.8
            )
        ]

        stats = processor.calculate_false_result_statistics(results)

        assert stats["total_results"] == 3
        assert stats["false_positive"]["count"] == 1
        assert stats["false_positive"]["rate"] == 1/3
        assert stats["false_negative"]["count"] == 1
        assert stats["false_negative"]["rate"] == 1/3
        assert stats["overall_false_result_rate"] == 2/3

    def test_complete_screening_with_false_results(
        self, processor, normal_individual, adenoma_individual, mock_tool
    ):
        """测试包含假结果处理的完整筛查流程"""
        # 正常个体筛查（可能产生假阳性）
        normal_result = processor.process_screening(normal_individual, mock_tool)

        # 验证假结果影响被正确计算
        if normal_result.result_type == ScreeningResultType.POSITIVE:
            # 假阳性
            assert normal_result.false_result_impact is not None
            assert normal_result.false_result_impact.result_type == FalseResultType.FALSE_POSITIVE
        else:
            # 真阴性
            assert normal_result.false_result_impact is None

        # 腺瘤个体筛查（可能产生假阴性）
        adenoma_result = processor.process_screening(adenoma_individual, mock_tool)

        if adenoma_result.result_type == ScreeningResultType.NEGATIVE:
            # 假阴性
            assert adenoma_result.false_result_impact is not None
            assert adenoma_result.false_result_impact.result_type == FalseResultType.FALSE_NEGATIVE
        else:
            # 真阳性
            assert adenoma_result.false_result_impact is None

    def test_false_result_impact_validation(self):
        """测试假结果影响数据验证"""
        # 有效假结果影响
        valid_impact = FalseResultImpact(
            result_type=FalseResultType.FALSE_POSITIVE,
            psychological_impact_score=0.6,
            additional_cost=1200.0,
            unnecessary_procedures=1,
            anxiety_duration_days=14
        )
        # 应该不抛出异常

        # 无效心理影响分数
        with pytest.raises(ValueError, match="心理影响分数必须在0-1之间"):
            FalseResultImpact(
                result_type=FalseResultType.FALSE_POSITIVE,
                psychological_impact_score=1.5,  # 无效值
                additional_cost=1200.0
            )

        # 无效额外成本
        with pytest.raises(ValueError, match="额外成本不能为负数"):
            FalseResultImpact(
                result_type=FalseResultType.FALSE_POSITIVE,
                psychological_impact_score=0.6,
                additional_cost=-100.0  # 无效值
            )

    def test_individual_screening_history_update(
        self, processor, adenoma_individual, mock_tool
    ):
        """测试个体筛查历史更新"""
        # 设置个体为腺瘤状态
        adenoma_individual.transition_to_state(DiseaseState.HIGH_RISK_ADENOMA)

        # 进行筛查
        result = processor.process_screening(adenoma_individual, mock_tool)

        # 验证筛查历史被更新
        assert len(adenoma_individual.screening_history) == 1

        screening_record = adenoma_individual.screening_history[0]
        assert screening_record['tool_type'] == mock_tool.tool_type.value
        assert screening_record['result_type'] == result.result_type.value
        assert screening_record['confidence_score'] == result.confidence_score
        assert 'test_date' in screening_record

    def test_screening_behavior_impact_calculation(self, normal_individual):
        """测试筛查行为影响计算"""
        # 添加一些筛查历史
        normal_individual.add_screening_record({
            'test_date': datetime.now(),
            'tool_type': 'fecal_immunochemical_test',
            'result_type': 'positive',
            'false_result_type': 'false_positive'
        })

        normal_individual.add_screening_record({
            'test_date': datetime.now(),
            'tool_type': 'fecal_immunochemical_test',
            'result_type': 'negative',
            'false_result_type': None
        })

        # 计算行为影响
        impact = normal_individual.get_screening_behavior_impact()

        assert 'compliance_modifier' in impact
        assert 'anxiety_level' in impact
        assert 'confidence_in_screening' in impact

        # 假阳性应该降低依从性和信心
        assert impact['compliance_modifier'] < 1.0
        assert impact['confidence_in_screening'] < 1.0
        assert impact['anxiety_level'] > 0.0

    def test_screening_compliance_score(self, normal_individual):
        """测试筛查依从性分数计算"""
        # 年轻个体（未到筛查年龄）
        young_individual = Individual(
            birth_year=2000,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.NORMAL
        )

        compliance = young_individual.get_screening_compliance_score()
        assert compliance == 1.0  # 未到筛查年龄，依从性为1

        # 老年个体（应该有筛查历史）
        old_individual = Individual(
            birth_year=1960,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.NORMAL
        )

        # 无筛查历史
        compliance = old_individual.get_screening_compliance_score()
        assert compliance == 0.0

        # 添加一些筛查记录
        for i in range(5):
            old_individual.add_screening_record({
                'test_date': datetime.now(),
                'tool_type': 'fecal_immunochemical_test',
                'result_type': 'negative'
            })

        compliance = old_individual.get_screening_compliance_score()
        assert 0.0 < compliance <= 1.0

    def test_positive_screening_history_check(self, normal_individual):
        """测试阳性筛查历史检查"""
        # 无筛查历史
        assert normal_individual.has_positive_screening_history() is False

        # 添加阴性筛查
        normal_individual.add_screening_record({
            'test_date': datetime.now(),
            'tool_type': 'fecal_immunochemical_test',
            'result_type': 'negative'
        })

        assert normal_individual.has_positive_screening_history() is False

        # 添加阳性筛查
        normal_individual.add_screening_record({
            'test_date': datetime.now(),
            'tool_type': 'fecal_immunochemical_test',
            'result_type': 'positive'
        })

        assert normal_individual.has_positive_screening_history() is True

    def test_last_screening_retrieval(self, normal_individual):
        """测试最近筛查记录获取"""
        # 无筛查历史
        assert normal_individual.get_last_screening() is None
        assert normal_individual.get_last_screening('fecal_immunochemical_test') is None

        # 添加筛查记录
        earlier_date = datetime.now() - timedelta(days=365)
        later_date = datetime.now()

        normal_individual.add_screening_record({
            'test_date': earlier_date,
            'tool_type': 'fecal_immunochemical_test',
            'result_type': 'negative'
        })

        normal_individual.add_screening_record({
            'test_date': later_date,
            'tool_type': 'colonoscopy',
            'result_type': 'negative'
        })

        # 获取最近的任何筛查
        last_screening = normal_individual.get_last_screening()
        assert last_screening['tool_type'] == 'colonoscopy'

        # 获取特定工具的最近筛查
        last_fit = normal_individual.get_last_screening('fecal_immunochemical_test')
        assert last_fit['tool_type'] == 'fecal_immunochemical_test'

        # 获取不存在的工具类型
        last_ct = normal_individual.get_last_screening('ct_colonography')
        assert last_ct is None

    def test_screening_detected_lesion_removal(
        self, processor, adenoma_individual, mock_tool
    ):
        """测试筛查发现病变摘除"""
        # 设置个体为腺瘤状态
        adenoma_individual.transition_to_state(DiseaseState.HIGH_RISK_ADENOMA)
        original_state = adenoma_individual.current_disease_state

        # 模拟筛查发现病变摘除
        result = DetailedScreeningResult(
            individual_id=adenoma_individual.individual_id,
            tool_type=mock_tool.tool_type,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.8,
            is_true_positive=True
        )

        processor._simulate_screening_detected_lesion_removal(
            adenoma_individual, result
        )

        # 验证状态重置为正常
        assert adenoma_individual.current_disease_state == DiseaseState.NORMAL

        # 验证摘除记录
        assert 'lesion_removal' in result.additional_data
        removal_records = result.additional_data['lesion_removal']
        assert len(removal_records) == 1
        assert removal_records[0]['original_state'] == original_state.value
        assert removal_records[0]['detection_method'] == 'screening'

    def test_screening_survival_impact_calculation(
        self, processor, adenoma_individual
    ):
        """测试筛查生存影响计算"""
        # 创建一些筛查结果
        results = [
            # 真阳性结果（腺瘤发现）
            DetailedScreeningResult(
                individual_id=adenoma_individual.individual_id,
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8,
                is_true_positive=True,
                additional_data={'true_disease_state': 'high_risk_adenoma'}
            ),
            # 假阳性结果
            DetailedScreeningResult(
                individual_id=adenoma_individual.individual_id,
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.6,
                is_true_positive=False,
                false_result_impact=FalseResultImpact(
                    result_type=FalseResultType.FALSE_POSITIVE,
                    psychological_impact_score=0.5,
                    additional_cost=1200.0
                )
            )
        ]

        # 计算生存影响
        impact = processor.calculate_screening_impact_on_survival(
            adenoma_individual, results
        )

        assert 'life_years_gained' in impact
        assert 'quality_adjusted_life_years' in impact
        assert 'early_detection_benefit' in impact
        assert 'false_result_harm' in impact

        # 应该有正的生存获益
        assert impact['life_years_gained'] > 0
        assert impact['quality_adjusted_life_years'] > 0

        # 应该有假结果危害
        assert impact['false_result_harm'] > 0
