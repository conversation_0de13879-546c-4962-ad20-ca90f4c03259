"""
筛查效果评估指标单元测试

测试筛查效果评估指标的计算，包括检出率、发病率降低、死亡率降低、
生存获益评估和成本效果比计算。
"""

import pytest
from datetime import datetime
from unittest.mock import Mock

from src.core.individual import Individual
from src.core.enums import DiseaseState, Gender
from src.core.population import Population
from src.modules.screening import (
    EffectivenessMetrics, DetectionMetrics, IncidenceReductionMetrics,
    MortalityReductionMetrics, SurvivalBenefitMetrics, CostEffectivenessMetrics,
    DetailedScreeningResult, ScreeningResultType, ScreeningToolType
)


class TestEffectivenessMetrics:
    """筛查效果评估指标测试类"""

    @pytest.fixture
    def sample_population(self):
        """创建样本人群"""
        individuals = []
        for i in range(1000):
            individual = Individual(
                birth_year=1970 - (i % 25),  # 50-74岁
                gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
                initial_disease_state=DiseaseState.NORMAL
            )
            individuals.append(individual)
        
        return Population(initial_individuals=individuals)

    @pytest.fixture
    def effectiveness_calculator(self, sample_population):
        """创建效果评估计算器"""
        return EffectivenessMetrics(sample_population)

    @pytest.fixture
    def sample_screening_results(self):
        """创建样本筛查结果"""
        results = []
        base_date = datetime.now()
        
        # 创建100个筛查结果
        for i in range(100):
            # 10个真阳性结果，不同疾病状态
            if i < 10:
                result_type = ScreeningResultType.POSITIVE
                is_true_positive = True
                
                # 不同的疾病状态
                if i < 4:
                    true_state = 'high_risk_adenoma'
                elif i < 6:
                    true_state = 'large_serrated'
                elif i < 8:
                    true_state = 'preclinical_cancer'
                else:
                    true_state = 'clinical_cancer_stage_i'
            else:
                result_type = ScreeningResultType.NEGATIVE
                is_true_positive = False
                true_state = 'normal'
            
            result = DetailedScreeningResult(
                individual_id=f"individual_{i}",
                tool_type=ScreeningToolType.FIT,
                result_type=result_type,
                test_date=base_date,
                confidence_score=0.8,
                is_true_positive=is_true_positive,
                cost=50.0,
                additional_data={'true_disease_state': true_state}
            )
            
            results.append(result)
        
        return results

    def test_effectiveness_calculator_initialization(self, sample_population):
        """测试效果评估计算器初始化"""
        calculator = EffectivenessMetrics(sample_population)
        
        assert calculator.population == sample_population
        assert len(calculator.screening_results) == 0
        assert calculator.baseline_rates is not None
        assert 'cancer_incidence_per_100k' in calculator.baseline_rates

    def test_baseline_rates_loading(self, effectiveness_calculator):
        """测试基线率数据加载"""
        rates = effectiveness_calculator.baseline_rates
        
        assert 'cancer_incidence_per_100k' in rates
        assert 'cancer_mortality_per_100k' in rates
        assert 'overall_mortality_per_100k' in rates
        assert 'adenoma_prevalence' in rates
        assert 'cancer_5year_survival' in rates
        
        # 验证数值合理性
        assert rates['cancer_incidence_per_100k'] > 0
        assert rates['cancer_mortality_per_100k'] > 0
        assert 0 < rates['cancer_5year_survival'] < 1

    def test_add_screening_results(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试添加筛查结果"""
        initial_count = len(effectiveness_calculator.screening_results)
        
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        assert len(effectiveness_calculator.screening_results) == initial_count + len(sample_screening_results)

    def test_detection_metrics_calculation(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试检出指标计算"""
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        metrics = effectiveness_calculator.calculate_detection_metrics()
        
        assert isinstance(metrics, DetectionMetrics)
        assert metrics.total_screenings == 100
        assert metrics.total_adenomas_detected == 6  # 4个高风险腺瘤 + 2个锯齿状腺瘤
        assert metrics.total_cancers_detected == 4   # 2个临床前癌症 + 2个临床癌症
        assert metrics.early_stage_cancers_detected == 4  # 所有癌症都是早期
        
        # 验证检出率计算
        assert metrics.adenoma_detection_rate == 60.0  # (6/100) * 1000
        assert metrics.preclinical_cancer_detection_rate == 20.0  # (2/100) * 1000
        assert metrics.clinical_cancer_detection_rate == 20.0  # (2/100) * 1000
        assert metrics.early_stage_cancer_rate == 1.0  # 4/4

    def test_incidence_reduction_calculation(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试发病率降低指标计算"""
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        metrics = effectiveness_calculator.calculate_incidence_reduction()
        
        assert isinstance(metrics, IncidenceReductionMetrics)
        assert metrics.baseline_incidence_rate > 0
        assert metrics.screening_incidence_rate >= 0
        assert metrics.cancer_incidence_reduction >= 0
        assert metrics.prevented_cancer_cases >= 0

    def test_mortality_reduction_calculation(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试死亡率降低指标计算"""
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        metrics = effectiveness_calculator.calculate_mortality_reduction()
        
        assert isinstance(metrics, MortalityReductionMetrics)
        assert metrics.baseline_mortality_rate > 0
        assert metrics.screening_mortality_rate >= 0
        assert metrics.cancer_mortality_reduction >= 0
        assert metrics.overall_mortality_reduction >= 0
        assert metrics.prevented_deaths >= 0

    def test_survival_benefit_calculation(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试生存获益指标计算"""
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        metrics = effectiveness_calculator.calculate_survival_benefit()
        
        assert isinstance(metrics, SurvivalBenefitMetrics)
        assert metrics.life_years_gained > 0
        assert metrics.quality_adjusted_life_years > 0
        assert metrics.disability_adjusted_life_years > 0
        assert metrics.average_life_extension >= 0
        assert metrics.survival_rate_improvement >= 0
        
        # 验证生存获益计算逻辑
        # 4个腺瘤 * 0.5-0.7 + 2个临床前癌症 * 5.0 + 2个临床癌症 * 3.0
        expected_min_life_years = 4 * 0.5 + 2 * 5.0 + 2 * 3.0  # 18年
        assert metrics.life_years_gained >= expected_min_life_years

    def test_cost_effectiveness_calculation(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试成本效果指标计算"""
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        # 先计算其他指标
        survival_metrics = effectiveness_calculator.calculate_survival_benefit()
        incidence_metrics = effectiveness_calculator.calculate_incidence_reduction()
        mortality_metrics = effectiveness_calculator.calculate_mortality_reduction()
        
        # 计算成本效果指标
        cost_metrics = effectiveness_calculator.calculate_cost_effectiveness(
            survival_metrics, incidence_metrics, mortality_metrics
        )
        
        assert isinstance(cost_metrics, CostEffectivenessMetrics)
        assert cost_metrics.total_screening_costs > 0
        assert cost_metrics.cost_per_life_year_gained > 0
        assert cost_metrics.cost_per_qaly > 0
        assert cost_metrics.total_treatment_cost_savings >= 0
        
        # 验证成本计算
        expected_screening_costs = 100 * 50.0  # 100个筛查 * 50元
        assert cost_metrics.total_screening_costs == expected_screening_costs

    def test_treatment_cost_savings_calculation(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试治疗成本节省计算"""
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        cost_savings = effectiveness_calculator._calculate_treatment_cost_savings()
        
        # 验证成本节省计算
        # 4个腺瘤 * 5000 + 2个临床前癌症 * 50000 + 2个临床癌症 * 30000
        expected_savings = 4 * 5000 + 2 * 50000 + 2 * 30000  # 180000
        assert cost_savings == expected_savings

    def test_cancer_counting_after_screening(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试筛查后癌症统计"""
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        cancers_after = effectiveness_calculator._count_cancers_after_screening()
        deaths_after = effectiveness_calculator._count_cancer_deaths_after_screening()
        
        assert cancers_after >= 0
        assert deaths_after >= 0
        assert deaths_after <= cancers_after  # 死亡数不应超过癌症数

    def test_effectiveness_report_generation(
        self, effectiveness_calculator, sample_screening_results
    ):
        """测试效果评估报告生成"""
        effectiveness_calculator.add_screening_results(sample_screening_results)
        
        report = effectiveness_calculator.generate_effectiveness_report()
        
        # 验证报告结构
        assert 'report_metadata' in report
        assert 'detection_metrics' in report
        assert 'incidence_reduction' in report
        assert 'mortality_reduction' in report
        assert 'survival_benefit' in report
        assert 'cost_effectiveness' in report
        
        # 验证报告元数据
        metadata = report['report_metadata']
        assert metadata['population_size'] == 1000
        assert metadata['total_screenings'] == 100
        assert 'generation_time' in metadata
        
        # 验证检出指标
        detection = report['detection_metrics']
        assert detection['adenoma_detection_rate_per_1000'] == 60.0
        assert detection['total_adenomas_detected'] == 6
        assert detection['total_cancers_detected'] == 4
        
        # 验证成本效果指标
        cost_eff = report['cost_effectiveness']
        assert cost_eff['total_screening_costs'] == 5000.0
        assert cost_eff['treatment_cost_savings'] == 180000.0

    def test_empty_screening_results_handling(self, effectiveness_calculator):
        """测试空筛查结果处理"""
        # 不添加任何筛查结果
        
        detection_metrics = effectiveness_calculator.calculate_detection_metrics()
        assert detection_metrics.total_screenings == 0
        assert detection_metrics.total_adenomas_detected == 0
        
        survival_metrics = effectiveness_calculator.calculate_survival_benefit()
        assert survival_metrics.life_years_gained == 0
        assert survival_metrics.quality_adjusted_life_years == 0

    def test_detection_metrics_data_structure(self):
        """测试检出指标数据结构"""
        metrics = DetectionMetrics(
            adenoma_detection_rate=40.0,
            preclinical_cancer_detection_rate=20.0,
            clinical_cancer_detection_rate=15.0,
            early_stage_cancer_rate=0.8,
            total_screenings=100,
            total_adenomas_detected=4,
            total_cancers_detected=3,
            early_stage_cancers_detected=2
        )
        
        assert metrics.adenoma_detection_rate == 40.0
        assert metrics.total_screenings == 100
        assert metrics.early_stage_cancer_rate == 0.8

    def test_survival_benefit_metrics_data_structure(self):
        """测试生存获益指标数据结构"""
        metrics = SurvivalBenefitMetrics(
            life_years_gained=100.0,
            quality_adjusted_life_years=80.0,
            disability_adjusted_life_years=75.0,
            average_life_extension=0.1,
            survival_rate_improvement=0.05
        )
        
        assert metrics.life_years_gained == 100.0
        assert metrics.quality_adjusted_life_years == 80.0
        assert metrics.average_life_extension == 0.1

    def test_cost_effectiveness_metrics_data_structure(self):
        """测试成本效果指标数据结构"""
        metrics = CostEffectivenessMetrics(
            cost_per_life_year_gained=5000.0,
            cost_per_qaly=6000.0,
            cost_per_cancer_prevented=50000.0,
            cost_per_death_prevented=100000.0,
            incremental_cost_effectiveness_ratio=6000.0,
            total_screening_costs=500000.0,
            total_treatment_cost_savings=200000.0
        )
        
        assert metrics.cost_per_life_year_gained == 5000.0
        assert metrics.cost_per_qaly == 6000.0
        assert metrics.incremental_cost_effectiveness_ratio == 6000.0

    def test_different_disease_states_survival_benefit(
        self, sample_population
    ):
        """测试不同疾病状态的生存获益计算"""
        # 创建新的计算器，避免之前测试的影响
        effectiveness_calculator = EffectivenessMetrics(sample_population)

        # 创建包含不同疾病状态的筛查结果
        results = []
        disease_states = [
            'low_risk_adenoma',
            'high_risk_adenoma', 
            'small_serrated',
            'large_serrated',
            'preclinical_cancer',
            'clinical_cancer_stage_i',
            'clinical_cancer_stage_ii',
            'clinical_cancer_stage_iii'
        ]
        
        for i, state in enumerate(disease_states):
            result = DetailedScreeningResult(
                individual_id=f"individual_{i}",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8,
                is_true_positive=True,
                cost=50.0,
                additional_data={'true_disease_state': state}
            )
            results.append(result)
        
        effectiveness_calculator.add_screening_results(results)
        survival_metrics = effectiveness_calculator.calculate_survival_benefit()

        # 验证不同疾病状态产生不同的生存获益
        assert survival_metrics.life_years_gained > 0
        assert survival_metrics.quality_adjusted_life_years > 0

        # 验证生存获益计算
        # 预期: 0.5 + 0.5 + 0.7 + 0.7 + 5.0 + 3.0 + 2.0 + 1.0 = 13.4年
        expected_life_years = 0.5 + 0.5 + 0.7 + 0.7 + 5.0 + 3.0 + 2.0 + 1.0

        # 验证生存获益在合理范围内
        assert survival_metrics.life_years_gained >= expected_life_years
        assert survival_metrics.life_years_gained <= expected_life_years + 5.0  # 允许一些额外的获益
