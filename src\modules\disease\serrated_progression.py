"""
锯齿状腺瘤进展模型

实现锯齿状腺瘤通路的疾病进展参数和时间分布建模。
"""

import random
import numpy as np
from typing import Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

from src.core.enums import DiseaseState, PathwayType, AnatomicalLocation


@dataclass
class ProgressionTimeDistribution:
    """进展时间分布参数"""
    mean: float          # 平均时间（年）
    std: float           # 标准差（年）
    min_time: float      # 最小时间（年）
    max_time: float      # 最大时间（年）
    distribution_type: str = "normal"  # 分布类型
    
    def __post_init__(self):
        """验证参数"""
        if self.mean <= 0:
            raise ValueError("平均时间必须大于0")
        if self.std <= 0:
            raise ValueError("标准差必须大于0")
        if self.min_time < 0:
            raise ValueError("最小时间不能为负数")
        if self.max_time <= self.min_time:
            raise ValueError("最大时间必须大于最小时间")
    
    def sample_time(self, random_seed: Optional[int] = None) -> float:
        """从分布中采样时间"""
        if random_seed is not None:
            np.random.seed(random_seed)
        
        if self.distribution_type == "normal":
            # 正态分布采样，截断到指定范围
            time = np.random.normal(self.mean, self.std)
            return max(self.min_time, min(self.max_time, time))
        elif self.distribution_type == "lognormal":
            # 对数正态分布
            time = np.random.lognormal(np.log(self.mean), self.std)
            return max(self.min_time, min(self.max_time, time))
        else:
            raise ValueError(f"不支持的分布类型: {self.distribution_type}")


class SerratedProgressionModel:
    """锯齿状腺瘤进展模型"""
    
    def __init__(self, random_seed: Optional[int] = None):
        """
        初始化锯齿状腺瘤进展模型
        
        Args:
            random_seed: 随机种子
        """
        self.random_seed = random_seed
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
        
        # 锯齿状腺瘤进展时间分布参数
        self.progression_times = {
            "normal_to_small_serrated": ProgressionTimeDistribution(
                mean=3.0,      # 平均3年（比传统腺瘤快）
                std=1.5,       # 标准差1.5年
                min_time=0.5,  # 最小6个月
                max_time=8.0   # 最大8年
            ),
            "small_serrated_to_large_serrated": ProgressionTimeDistribution(
                mean=4.0,      # 平均4年
                std=2.0,       # 标准差2年
                min_time=1.0,  # 最小1年
                max_time=10.0  # 最大10年
            ),
            "large_serrated_to_preclinical_cancer": ProgressionTimeDistribution(
                mean=6.0,      # 平均6年（比传统腺瘤慢）
                std=2.5,       # 标准差2.5年
                min_time=2.0,  # 最小2年
                max_time=15.0  # 最大15年
            )
        }
        
        # 进展概率（每年）
        self.progression_probabilities = {
            "normal_to_small_serrated": 0.02,              # 2%年进展率
            "small_serrated_to_large_serrated": 0.15,      # 15%年进展率
            "large_serrated_to_preclinical_cancer": 0.08,  # 8%年进展率
        }
        
        # 回退概率（治疗或自然回退）
        self.regression_probabilities = {
            "small_serrated_to_normal": 0.05,      # 5%年回退率
            "large_serrated_to_normal": 0.03,      # 3%年回退率
        }
    
    def get_progression_time(self, from_state: str, to_state: str) -> float:
        """
        获取状态转换的进展时间
        
        Args:
            from_state: 起始状态
            to_state: 目标状态
            
        Returns:
            进展时间（年）
        """
        transition_key = f"{from_state}_to_{to_state}"
        
        if transition_key in self.progression_times:
            return self.progression_times[transition_key].sample_time(self.random_seed)
        else:
            raise ValueError(f"未定义的状态转换: {from_state} -> {to_state}")
    
    def get_progression_probability(self, from_state: str, to_state: str, time_step: float = 1.0) -> float:
        """
        获取在给定时间步长内的进展概率
        
        Args:
            from_state: 起始状态
            to_state: 目标状态
            time_step: 时间步长（年）
            
        Returns:
            进展概率
        """
        transition_key = f"{from_state}_to_{to_state}"
        
        if transition_key in self.progression_probabilities:
            annual_prob = self.progression_probabilities[transition_key]
            # 转换为时间步长概率
            return 1 - (1 - annual_prob) ** time_step
        elif transition_key in self.regression_probabilities:
            annual_prob = self.regression_probabilities[transition_key]
            return 1 - (1 - annual_prob) ** time_step
        else:
            return 0.0
    
    def should_progress(self, from_state: str, to_state: str, time_step: float = 1.0) -> bool:
        """
        判断是否应该进展到下一状态
        
        Args:
            from_state: 起始状态
            to_state: 目标状态
            time_step: 时间步长（年）
            
        Returns:
            是否进展
        """
        probability = self.get_progression_probability(from_state, to_state, time_step)
        return random.random() < probability
    
    def get_next_state(self, current_state: DiseaseState, time_step: float = 1.0) -> DiseaseState:
        """
        根据当前状态和时间步长确定下一状态
        
        Args:
            current_state: 当前疾病状态
            time_step: 时间步长（年）
            
        Returns:
            下一个疾病状态
        """
        state_str = current_state.value
        
        # 定义锯齿状通路的状态转换逻辑
        if state_str == "normal":
            if self.should_progress("normal", "small_serrated", time_step):
                return DiseaseState.SMALL_SERRATED
        elif state_str == "small_serrated":
            # 检查进展到大锯齿状腺瘤
            if self.should_progress("small_serrated", "large_serrated", time_step):
                return DiseaseState.LARGE_SERRATED
            # 检查回退到正常
            elif self.should_progress("small_serrated", "normal", time_step):
                return DiseaseState.NORMAL
        elif state_str == "large_serrated":
            # 检查进展到临床前癌症
            if self.should_progress("large_serrated", "preclinical_cancer", time_step):
                return DiseaseState.PRECLINICAL_CANCER
            # 检查回退到正常
            elif self.should_progress("large_serrated", "normal", time_step):
                return DiseaseState.NORMAL
        
        # 如果没有状态变化，返回当前状态
        return current_state
    
    def simulate_progression_timeline(self, start_state: DiseaseState, 
                                    max_time: float = 50.0) -> Dict[str, Any]:
        """
        模拟完整的进展时间线
        
        Args:
            start_state: 起始状态
            max_time: 最大模拟时间（年）
            
        Returns:
            进展时间线字典
        """
        timeline = []
        current_state = start_state
        current_time = 0.0
        
        while current_time < max_time:
            # 记录当前状态
            timeline.append({
                "time": current_time,
                "state": current_state.value,
                "pathway": "serrated_adenoma"
            })
            
            # 如果到达终态，停止模拟
            if current_state in [DiseaseState.DEATH_CANCER, DiseaseState.DEATH_OTHER]:
                break
            
            # 计算下一次状态变化的时间
            next_state = self.get_next_state(current_state, time_step=0.1)  # 0.1年步长
            
            if next_state != current_state:
                # 状态发生变化，计算精确的转换时间
                transition_time = self.get_progression_time(current_state.value, next_state.value)
                new_time = current_time + transition_time

                # 确保不超过最大时间
                if new_time > max_time:
                    break

                current_time = new_time
                current_state = next_state
            else:
                # 状态未变化，增加时间步长
                current_time += 0.1
        
        return {
            "timeline": timeline,
            "final_state": current_state.value,
            "total_time": current_time
        }
    
    def get_progression_statistics(self) -> Dict[str, Any]:
        """获取进展参数统计信息"""
        stats = {
            "progression_times": {},
            "progression_probabilities": self.progression_probabilities.copy(),
            "regression_probabilities": self.regression_probabilities.copy()
        }
        
        # 计算进展时间统计
        for transition, distribution in self.progression_times.items():
            stats["progression_times"][transition] = {
                "mean": distribution.mean,
                "std": distribution.std,
                "min": distribution.min_time,
                "max": distribution.max_time,
                "distribution_type": distribution.distribution_type
            }
        
        return stats
    
    def update_progression_parameters(self, parameter_updates: Dict[str, Any]) -> None:
        """
        更新进展参数
        
        Args:
            parameter_updates: 参数更新字典
        """
        if "progression_times" in parameter_updates:
            for transition, params in parameter_updates["progression_times"].items():
                if transition in self.progression_times:
                    current_dist = self.progression_times[transition]
                    # 更新分布参数
                    if "mean" in params:
                        current_dist.mean = params["mean"]
                    if "std" in params:
                        current_dist.std = params["std"]
                    if "min_time" in params:
                        current_dist.min_time = params["min_time"]
                    if "max_time" in params:
                        current_dist.max_time = params["max_time"]
        
        if "progression_probabilities" in parameter_updates:
            self.progression_probabilities.update(parameter_updates["progression_probabilities"])
        
        if "regression_probabilities" in parameter_updates:
            self.regression_probabilities.update(parameter_updates["regression_probabilities"])
    
    def validate_parameters(self) -> bool:
        """验证模型参数的合理性"""
        try:
            # 验证进展时间分布
            for transition, distribution in self.progression_times.items():
                distribution.__post_init__()  # 触发验证
            
            # 验证概率范围
            for prob_dict in [self.progression_probabilities, self.regression_probabilities]:
                for transition, prob in prob_dict.items():
                    if not 0 <= prob <= 1:
                        raise ValueError(f"概率 {transition} 超出有效范围: {prob}")
            
            return True
        except Exception:
            return False


@dataclass
class SerratedAdenomaCharacteristics:
    """锯齿状腺瘤特异性特征"""
    size_mm: float                      # 腺瘤大小（毫米）
    anatomical_location: AnatomicalLocation  # 解剖位置
    detection_difficulty: float         # 检测难度系数（0-1）
    malignant_potential: float          # 恶变潜能评分（0-1）
    morphology_score: float             # 形态学评分（0-1）
    treatment_response: float           # 治疗反应评分（0-1）

    def __post_init__(self):
        """验证特征值"""
        if self.size_mm < 0:
            raise ValueError("腺瘤大小不能为负数")
        if not 0 <= self.detection_difficulty <= 1:
            raise ValueError("检测难度系数必须在0-1范围内")
        if not 0 <= self.malignant_potential <= 1:
            raise ValueError("恶变潜能评分必须在0-1范围内")
        if not 0 <= self.morphology_score <= 1:
            raise ValueError("形态学评分必须在0-1范围内")
        if not 0 <= self.treatment_response <= 1:
            raise ValueError("治疗反应评分必须在0-1范围内")


class SerratedAdenomaFeatureModel:
    """锯齿状腺瘤特征模型"""

    def __init__(self):
        """初始化特征模型"""
        # 锯齿状腺瘤解剖位置偏好
        self.location_probabilities = {
            AnatomicalLocation.PROXIMAL_COLON: 0.65,  # 65%近端结肠（vs传统40%）
            AnatomicalLocation.DISTAL_COLON: 0.25,   # 25%远端结肠（vs传统35%）
            AnatomicalLocation.RECTUM: 0.10          # 10%直肠（vs传统25%）
        }

        # 锯齿状腺瘤筛查检测特征
        self.screening_characteristics = {
            "colonoscopy_sensitivity": 0.75,    # 结肠镜敏感性（vs传统0.95）
            "fit_sensitivity": 0.45,           # FIT敏感性（vs传统0.65）
            "sigmoidoscopy_sensitivity": 0.30,  # 乙状结肠镜敏感性（vs传统0.85）
            "detection_skill_dependency": 1.5   # 检测技能依赖性系数
        }

        # 大小分布参数
        self.size_distribution = {
            "small_serrated": {
                "mean_mm": 6.0,
                "std_mm": 2.0,
                "min_mm": 2.0,
                "max_mm": 9.9
            },
            "large_serrated": {
                "mean_mm": 15.0,
                "std_mm": 5.0,
                "min_mm": 10.0,
                "max_mm": 30.0
            }
        }

        # 恶变风险评估参数
        self.malignancy_risk_factors = {
            "size_threshold_mm": 10.0,          # 大小阈值
            "proximal_location_multiplier": 1.3, # 近端位置恶变风险倍数
            "age_risk_factor": 0.02,            # 年龄风险因子（每年）
            "baseline_malignancy_risk": 0.05    # 基础恶变风险
        }

    def generate_characteristics(self, state: DiseaseState,
                               individual_age: Optional[int] = None,
                               random_seed: Optional[int] = None) -> SerratedAdenomaCharacteristics:
        """
        生成锯齿状腺瘤特征

        Args:
            state: 疾病状态
            individual_age: 个体年龄
            random_seed: 随机种子

        Returns:
            锯齿状腺瘤特征
        """
        if random_seed is not None:
            np.random.seed(random_seed)

        # 确定解剖位置
        location = self._sample_anatomical_location()

        # 确定大小
        size_mm = self._sample_size(state)

        # 计算检测难度
        detection_difficulty = self._calculate_detection_difficulty(size_mm, location)

        # 计算恶变潜能
        malignant_potential = self._calculate_malignant_potential(size_mm, location, individual_age)

        # 生成形态学评分
        morphology_score = np.random.beta(2, 3)  # 偏向较低评分

        # 生成治疗反应评分
        treatment_response = self._calculate_treatment_response(size_mm, location)

        return SerratedAdenomaCharacteristics(
            size_mm=size_mm,
            anatomical_location=location,
            detection_difficulty=detection_difficulty,
            malignant_potential=malignant_potential,
            morphology_score=morphology_score,
            treatment_response=treatment_response
        )

    def _sample_anatomical_location(self) -> AnatomicalLocation:
        """采样解剖位置"""
        locations = list(self.location_probabilities.keys())
        probabilities = list(self.location_probabilities.values())

        choice = np.random.choice(len(locations), p=probabilities)
        return locations[choice]

    def _sample_size(self, state: DiseaseState) -> float:
        """采样腺瘤大小"""
        if state == DiseaseState.SMALL_SERRATED:
            params = self.size_distribution["small_serrated"]
        elif state == DiseaseState.LARGE_SERRATED:
            params = self.size_distribution["large_serrated"]
        else:
            raise ValueError(f"无效的锯齿状腺瘤状态: {state}")

        size = np.random.normal(params["mean_mm"], params["std_mm"])
        return max(params["min_mm"], min(params["max_mm"], size))

    def _calculate_detection_difficulty(self, size_mm: float,
                                      location: AnatomicalLocation) -> float:
        """计算检测难度"""
        # 基础检测难度（锯齿状腺瘤本身较难检测）
        base_difficulty = 0.6

        # 大小影响（越小越难检测）
        size_factor = max(0.1, 1.0 - (size_mm / 20.0))

        # 位置影响（近端结肠更难检测）
        location_factors = {
            AnatomicalLocation.PROXIMAL_COLON: 1.2,
            AnatomicalLocation.DISTAL_COLON: 1.0,
            AnatomicalLocation.RECTUM: 0.8
        }
        location_factor = location_factors.get(location, 1.0)

        difficulty = base_difficulty * size_factor * location_factor
        return max(0.0, min(1.0, difficulty))

    def _calculate_malignant_potential(self, size_mm: float,
                                     location: AnatomicalLocation,
                                     age: Optional[int] = None) -> float:
        """计算恶变潜能"""
        # 基础恶变风险
        base_risk = self.malignancy_risk_factors["baseline_malignancy_risk"]

        # 大小影响
        if size_mm >= self.malignancy_risk_factors["size_threshold_mm"]:
            size_multiplier = 1.0 + (size_mm - 10.0) / 20.0
        else:
            size_multiplier = size_mm / 10.0

        # 位置影响
        if location == AnatomicalLocation.PROXIMAL_COLON:
            location_multiplier = self.malignancy_risk_factors["proximal_location_multiplier"]
        else:
            location_multiplier = 1.0

        # 年龄影响
        age_multiplier = 1.0
        if age is not None:
            age_factor = self.malignancy_risk_factors["age_risk_factor"]
            age_multiplier = 1.0 + max(0, age - 50) * age_factor

        potential = base_risk * size_multiplier * location_multiplier * age_multiplier
        return max(0.0, min(1.0, potential))

    def _calculate_treatment_response(self, size_mm: float,
                                    location: AnatomicalLocation) -> float:
        """计算治疗反应评分"""
        # 基础治疗反应（锯齿状腺瘤治疗反应较好）
        base_response = 0.8

        # 大小影响（越大治疗越困难）
        size_factor = max(0.5, 1.0 - (size_mm - 5.0) / 25.0)

        # 位置影响（近端位置治疗稍困难）
        location_factors = {
            AnatomicalLocation.PROXIMAL_COLON: 0.9,
            AnatomicalLocation.DISTAL_COLON: 1.0,
            AnatomicalLocation.RECTUM: 1.1
        }
        location_factor = location_factors.get(location, 1.0)

        response = base_response * size_factor * location_factor
        return max(0.0, min(1.0, response))

    def get_screening_sensitivity(self, screening_method: str,
                                characteristics: SerratedAdenomaCharacteristics) -> float:
        """
        获取特定筛查方法对锯齿状腺瘤的敏感性

        Args:
            screening_method: 筛查方法名称
            characteristics: 锯齿状腺瘤特征

        Returns:
            调整后的敏感性
        """
        base_sensitivity = self.screening_characteristics.get(f"{screening_method}_sensitivity", 0.5)

        # 根据检测难度调整敏感性
        difficulty_adjustment = 1.0 - characteristics.detection_difficulty * 0.3

        # 根据大小调整敏感性
        size_adjustment = min(1.0, characteristics.size_mm / 10.0)

        adjusted_sensitivity = base_sensitivity * difficulty_adjustment * size_adjustment
        return max(0.0, min(1.0, adjusted_sensitivity))

    def get_location_distribution(self) -> Dict[AnatomicalLocation, float]:
        """获取锯齿状腺瘤解剖位置分布"""
        return self.location_probabilities.copy()

    def get_screening_characteristics(self) -> Dict[str, float]:
        """获取筛查特征"""
        return self.screening_characteristics.copy()
