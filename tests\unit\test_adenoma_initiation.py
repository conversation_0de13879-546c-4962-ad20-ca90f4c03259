"""
测试腺瘤产生建模引擎

测试基于年龄、性别和风险因素的乙状函数腺瘤产生概率计算。
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch

from src.core.enums import Gender
from src.modules.disease.adenoma_initiation import (
    AdenomaInitiationModel,
    SigmoidParameters,
    GenderMultipliers
)
from src.modules.disease.risk_factors import RiskFactorProfile, RiskFactor, RiskFactorType
from src.modules.disease.risk_calculator import RiskScore


class TestSigmoidParameters:
    """测试乙状函数参数"""

    def test_sigmoid_parameters_creation(self):
        """测试乙状函数参数创建"""
        params = SigmoidParameters(
            inflection_point=50.0,
            steepness=10.0,
            max_probability=0.001,
            baseline_rate=0.00001
        )
        
        assert params.inflection_point == 50.0
        assert params.steepness == 10.0
        assert params.max_probability == 0.001
        assert params.baseline_rate == 0.00001


class TestGenderMultipliers:
    """测试性别倍数"""

    def test_gender_multipliers_creation(self):
        """测试性别倍数创建"""
        multipliers = GenderMultipliers(
            male_multiplier=1.2,
            female_multiplier=1.0
        )
        
        assert multipliers.male_multiplier == 1.2
        assert multipliers.female_multiplier == 1.0


class TestAdenomaInitiationModel:
    """测试腺瘤产生建模类"""

    def setup_method(self):
        """设置测试环境"""
        self.model = AdenomaInitiationModel()
        
        # 创建测试用的风险因素档案
        self.risk_profile = RiskFactorProfile("test_individual_001")
        self.risk_profile.add_risk_factor(RiskFactor(RiskFactorType.FAMILY_HISTORY, True))
        self.risk_profile.add_risk_factor(RiskFactor(RiskFactorType.BMI, 25.0))

    def test_model_initialization(self):
        """测试模型初始化"""
        assert self.model.sigmoid_params is not None
        assert self.model.gender_multipliers is not None
        assert self.model.risk_calculator is not None

    def test_default_sigmoid_parameters(self):
        """测试默认乙状函数参数"""
        params = self.model.sigmoid_params
        
        assert params.inflection_point == 50.0
        assert params.steepness == 10.0
        assert params.max_probability == 0.001
        assert params.baseline_rate == 0.00001

    def test_default_gender_multipliers(self):
        """测试默认性别倍数"""
        multipliers = self.model.gender_multipliers
        
        assert multipliers.male_multiplier == 1.2
        assert multipliers.female_multiplier == 1.0

    def test_custom_parameters_initialization(self):
        """测试自定义参数初始化"""
        custom_sigmoid = SigmoidParameters(
            inflection_point=55.0,
            steepness=8.0,
            max_probability=0.002,
            baseline_rate=0.00002
        )
        
        custom_gender = GenderMultipliers(
            male_multiplier=1.5,
            female_multiplier=0.8
        )
        
        model = AdenomaInitiationModel(
            sigmoid_params=custom_sigmoid,
            gender_multipliers=custom_gender
        )
        
        assert model.sigmoid_params.inflection_point == 55.0
        assert model.gender_multipliers.male_multiplier == 1.5

    def test_base_sigmoid_probability_calculation(self):
        """测试基础乙状函数概率计算"""
        # 测试不同年龄的概率
        prob_young = self.model.calculate_base_sigmoid_probability(30.0)
        prob_middle = self.model.calculate_base_sigmoid_probability(50.0)
        prob_old = self.model.calculate_base_sigmoid_probability(70.0)
        
        # 概率应该随年龄增加
        assert prob_young < prob_middle < prob_old
        
        # 概率应该在合理范围内
        assert 0 <= prob_young <= 1
        assert 0 <= prob_middle <= 1
        assert 0 <= prob_old <= 1

    def test_sigmoid_function_properties(self):
        """测试乙状函数特性"""
        # 在拐点年龄，概率应该接近中点
        inflection_prob = self.model.calculate_base_sigmoid_probability(50.0)
        expected_midpoint = (
            self.model.sigmoid_params.baseline_rate + 
            self.model.sigmoid_params.max_probability
        ) / 2
        
        # 允许一定误差
        assert abs(inflection_prob - expected_midpoint) < 0.0001

    def test_gender_adjustment(self):
        """测试性别调整"""
        base_prob = 0.001
        
        male_prob = self.model.apply_gender_adjustment(base_prob, Gender.MALE)
        female_prob = self.model.apply_gender_adjustment(base_prob, Gender.FEMALE)
        
        # 男性概率应该更高（默认倍数1.2 vs 1.0）
        assert male_prob > female_prob
        assert male_prob == base_prob * 1.2
        assert female_prob == base_prob * 1.0

    @patch('src.modules.disease.adenoma_initiation.RiskCalculator')
    def test_risk_factor_adjustment(self, mock_risk_calculator_class):
        """测试风险因素调整"""
        # 模拟风险计算器
        mock_calculator = Mock()
        mock_risk_score = Mock()
        mock_risk_score.final_score = 1.5
        mock_calculator.calculate_risk_score.return_value = mock_risk_score
        
        model = AdenomaInitiationModel(risk_calculator=mock_calculator)
        
        base_prob = 0.001
        adjusted_prob = model.apply_risk_factor_adjustment(
            base_prob, self.risk_profile, 55.0, Gender.MALE
        )

        assert adjusted_prob == base_prob * 1.5
        mock_calculator.calculate_risk_score.assert_called_once_with(
            individual_id=self.risk_profile.individual_id,
            risk_profile=self.risk_profile,
            age=55.0,
            gender=Gender.MALE
        )

    def test_risk_factor_adjustment_upper_limit(self):
        """测试风险因素调整上限"""
        # 创建一个返回高风险评分的模拟计算器
        mock_calculator = Mock()
        mock_risk_score = Mock()
        mock_risk_score.final_score = 100.0  # 极高风险评分
        mock_calculator.calculate_risk_score.return_value = mock_risk_score
        
        model = AdenomaInitiationModel(risk_calculator=mock_calculator)
        
        base_prob = 0.001
        adjusted_prob = model.apply_risk_factor_adjustment(
            base_prob, self.risk_profile, 55.0, Gender.MALE
        )
        
        # 应该被限制在月概率上限1%
        assert adjusted_prob <= 0.01

    def test_comprehensive_probability_calculation(self):
        """测试综合概率计算"""
        prob = self.model.calculate_adenoma_initiation_probability(
            age=55.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile
        )
        
        # 概率应该在合理范围内
        assert 0 <= prob <= 1
        assert isinstance(prob, float)

    def test_annual_probability_conversion(self):
        """测试年概率转换"""
        annual_prob = self.model.calculate_annual_probability(
            age=55.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile
        )
        
        monthly_prob = self.model.calculate_adenoma_initiation_probability(
            age=55.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile
        )
        
        # 年概率应该大于月概率
        assert annual_prob > monthly_prob
        
        # 验证转换公式：1 - (1 - monthly_prob)^12
        expected_annual = 1 - (1 - monthly_prob) ** 12
        assert abs(annual_prob - expected_annual) < 1e-10

    def test_age_progression_in_probability(self):
        """测试概率随年龄的变化"""
        ages = [30, 40, 50, 60, 70]
        probabilities = []
        
        for age in ages:
            prob = self.model.calculate_adenoma_initiation_probability(
                age=age,
                gender=Gender.MALE,
                risk_profile=self.risk_profile
            )
            probabilities.append(prob)
        
        # 概率应该随年龄单调递增
        for i in range(1, len(probabilities)):
            assert probabilities[i] >= probabilities[i-1]

    def test_gender_difference_in_probability(self):
        """测试性别差异"""
        male_prob = self.model.calculate_adenoma_initiation_probability(
            age=55.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile
        )
        
        female_prob = self.model.calculate_adenoma_initiation_probability(
            age=55.0,
            gender=Gender.FEMALE,
            risk_profile=self.risk_profile
        )
        
        # 男性概率应该更高
        assert male_prob > female_prob

    @patch('numpy.random.random')
    def test_adenoma_initiation_sampling_success(self, mock_random):
        """测试腺瘤产生时间抽样（成功情况）"""
        # 模拟随机数，第一次就命中
        mock_random.return_value = 0.0001  # 很小的随机数，确保命中
        
        initiation_age = self.model.sample_adenoma_initiation_time(
            age=50.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile,
            max_age=80.0
        )
        
        assert initiation_age is not None
        assert 50.0 <= initiation_age <= 80.0

    @patch('numpy.random.random')
    def test_adenoma_initiation_sampling_failure(self, mock_random):
        """测试腺瘤产生时间抽样（失败情况）"""
        # 模拟随机数，永远不命中
        mock_random.return_value = 0.999  # 很大的随机数，永远不命中
        
        initiation_age = self.model.sample_adenoma_initiation_time(
            age=50.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile,
            max_age=52.0  # 很短的时间窗口
        )
        
        assert initiation_age is None

    def test_probability_curve_generation(self):
        """测试概率曲线生成"""
        curve = self.model.get_probability_curve(
            gender=Gender.MALE,
            risk_profile=self.risk_profile,
            age_range=(40.0, 60.0),
            age_step=5.0
        )
        
        # 检查返回的数据结构
        assert isinstance(curve, dict)
        assert len(curve) == 5  # 40, 45, 50, 55, 60
        
        # 检查年龄键
        expected_ages = [40.0, 45.0, 50.0, 55.0, 60.0]
        for age in expected_ages:
            assert age in curve
            assert 0 <= curve[age] <= 1

    def test_parameter_updates(self):
        """测试参数更新"""
        new_sigmoid = SigmoidParameters(
            inflection_point=45.0,
            steepness=12.0,
            max_probability=0.0015,
            baseline_rate=0.000015
        )
        
        new_gender = GenderMultipliers(
            male_multiplier=1.3,
            female_multiplier=0.9
        )
        
        self.model.update_sigmoid_parameters(new_sigmoid)
        self.model.update_gender_multipliers(new_gender)
        
        assert self.model.sigmoid_params.inflection_point == 45.0
        assert self.model.gender_multipliers.male_multiplier == 1.3

    def test_get_model_parameters(self):
        """测试获取模型参数"""
        params = self.model.get_model_parameters()
        
        assert "sigmoid_params" in params
        assert "gender_multipliers" in params
        
        sigmoid_params = params["sigmoid_params"]
        assert "inflection_point" in sigmoid_params
        assert "steepness" in sigmoid_params
        assert "max_probability" in sigmoid_params
        assert "baseline_rate" in sigmoid_params
        
        gender_params = params["gender_multipliers"]
        assert "male_multiplier" in gender_params
        assert "female_multiplier" in gender_params

    def test_probability_consistency(self):
        """测试概率计算一致性"""
        # 多次计算相同参数应该得到相同结果
        prob1 = self.model.calculate_adenoma_initiation_probability(
            age=55.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile
        )
        
        prob2 = self.model.calculate_adenoma_initiation_probability(
            age=55.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile
        )
        
        assert prob1 == prob2

    def test_edge_cases(self):
        """测试边界情况"""
        # 极低年龄
        prob_young = self.model.calculate_adenoma_initiation_probability(
            age=0.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile
        )
        assert prob_young >= 0
        
        # 极高年龄
        prob_old = self.model.calculate_adenoma_initiation_probability(
            age=120.0,
            gender=Gender.MALE,
            risk_profile=self.risk_profile
        )
        assert prob_old <= 1
