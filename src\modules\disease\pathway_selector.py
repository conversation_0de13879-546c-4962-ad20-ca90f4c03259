"""
通路选择和分配机制

实现疾病进展通路的选择逻辑，包括15%锯齿状腺瘤通路分配和基于风险因素的通路选择偏好。
"""

import random
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

from src.core.enums import PathwayType, DiseaseState
from src.modules.disease.risk_factors import RiskFactorType, RiskFactorProfile


@dataclass
class PathwaySelectionStatistics:
    """通路选择统计信息"""
    total_selections: int = 0
    adenoma_carcinoma_count: int = 0
    serrated_adenoma_count: int = 0
    
    @property
    def adenoma_carcinoma_percentage(self) -> float:
        """腺瘤-癌变通路百分比"""
        if self.total_selections == 0:
            return 0.0
        return (self.adenoma_carcinoma_count / self.total_selections) * 100
    
    @property
    def serrated_adenoma_percentage(self) -> float:
        """锯齿状腺瘤通路百分比"""
        if self.total_selections == 0:
            return 0.0
        return (self.serrated_adenoma_count / self.total_selections) * 100
    
    def reset(self) -> None:
        """重置统计信息"""
        self.total_selections = 0
        self.adenoma_carcinoma_count = 0
        self.serrated_adenoma_count = 0


class PathwaySelector:
    """疾病进展通路选择器"""
    
    def __init__(self, base_serrated_probability: float = 0.15, random_seed: Optional[int] = None):
        """
        初始化通路选择器
        
        Args:
            base_serrated_probability: 基础锯齿状腺瘤通路概率（默认15%）
            random_seed: 随机种子，用于可重现的结果
        """
        self.base_serrated_probability = base_serrated_probability
        self.statistics = PathwaySelectionStatistics()
        
        # 风险因素对锯齿状腺瘤通路的影响系数
        self.serrated_modifiers = {
            RiskFactorType.SMOKING: 1.3,           # 吸烟增加锯齿状风险
            RiskFactorType.ALCOHOL_CONSUMPTION: 1.2,  # 酒精增加锯齿状风险
            RiskFactorType.FAMILY_HISTORY: 0.8,    # 家族史降低锯齿状风险
            RiskFactorType.IBD: 0.9,               # 炎症性肠病略降低锯齿状风险
            RiskFactorType.DIABETES: 1.1,          # 糖尿病略增加锯齿状风险
        }
        
        # 性别对锯齿状腺瘤通路的影响
        self.gender_modifiers = {
            "female": 1.1,  # 女性略高锯齿状风险
            "male": 1.0     # 男性基准
        }
        
        if random_seed is not None:
            random.seed(random_seed)
    
    def select_pathway(self, individual, risk_factors: Optional[RiskFactorProfile] = None) -> PathwayType:
        """
        选择疾病进展通路
        
        Args:
            individual: 个体对象（需要有gender属性）
            risk_factors: 风险因素配置文件
            
        Returns:
            选择的通路类型
        """
        adjusted_probability = self._calculate_adjusted_probability(individual, risk_factors)
        
        # 随机选择通路
        if random.random() < adjusted_probability:
            selected_pathway = PathwayType.SERRATED_ADENOMA
            self.statistics.serrated_adenoma_count += 1
        else:
            selected_pathway = PathwayType.ADENOMA_CARCINOMA
            self.statistics.adenoma_carcinoma_count += 1
        
        self.statistics.total_selections += 1
        return selected_pathway
    
    def _calculate_adjusted_probability(self, individual, risk_factors: Optional[RiskFactorProfile]) -> float:
        """
        计算调整后的锯齿状腺瘤通路概率
        
        Args:
            individual: 个体对象
            risk_factors: 风险因素配置文件
            
        Returns:
            调整后的概率
        """
        adjusted_probability = self.base_serrated_probability
        
        # 应用风险因素调整
        if risk_factors:
            for factor_type, risk_factor in risk_factors.risk_factors.items():
                if factor_type in self.serrated_modifiers and risk_factor.value:
                    modifier = self.serrated_modifiers[factor_type]

                    # 对于连续型风险因素，根据值的大小调整影响
                    if factor_type in [RiskFactorType.ALCOHOL_CONSUMPTION]:
                        # 酒精消费：值越高影响越大
                        if isinstance(risk_factor.value, (int, float)):
                            modifier = 1.0 + (modifier - 1.0) * min(risk_factor.value / 10.0, 1.0)

                    adjusted_probability *= modifier
        
        # 应用性别调整
        if hasattr(individual, 'gender') and individual.gender:
            gender_modifier = self.gender_modifiers.get(individual.gender.lower(), 1.0)
            adjusted_probability *= gender_modifier
        
        # 确保概率在有效范围内
        adjusted_probability = max(0.0, min(1.0, adjusted_probability))
        
        return adjusted_probability
    
    def batch_select_pathways(self, individuals: List, risk_factors_list: Optional[List[RiskFactorProfile]] = None) -> List[PathwayType]:
        """
        批量选择通路
        
        Args:
            individuals: 个体列表
            risk_factors_list: 风险因素列表（可选，长度应与individuals相同）
            
        Returns:
            通路类型列表
        """
        if risk_factors_list and len(risk_factors_list) != len(individuals):
            raise ValueError("风险因素列表长度必须与个体列表长度相同")
        
        pathways = []
        for i, individual in enumerate(individuals):
            risk_factors = risk_factors_list[i] if risk_factors_list else None
            pathway = self.select_pathway(individual, risk_factors)
            pathways.append(pathway)
        
        return pathways
    
    def get_statistics(self) -> PathwaySelectionStatistics:
        """获取通路选择统计信息"""
        return self.statistics
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.statistics.reset()
    
    def validate_distribution(self, tolerance: float = 0.02) -> bool:
        """
        验证通路分配是否符合预期分布
        
        Args:
            tolerance: 允许的偏差范围（默认2%）
            
        Returns:
            是否符合预期分布
        """
        if self.statistics.total_selections == 0:
            return True
        
        expected_serrated_percentage = self.base_serrated_probability * 100
        actual_serrated_percentage = self.statistics.serrated_adenoma_percentage
        
        deviation = abs(actual_serrated_percentage - expected_serrated_percentage)
        return deviation <= (tolerance * 100)
    
    def get_pathway_preferences(self, individual, risk_factors: Optional[RiskFactorProfile] = None) -> Dict[str, float]:
        """
        获取个体的通路偏好概率
        
        Args:
            individual: 个体对象
            risk_factors: 风险因素配置文件
            
        Returns:
            通路偏好字典
        """
        serrated_prob = self._calculate_adjusted_probability(individual, risk_factors)
        adenoma_prob = 1.0 - serrated_prob
        
        return {
            "serrated_adenoma": serrated_prob,
            "adenoma_carcinoma": adenoma_prob
        }
    
    def simulate_population_distribution(self, population_size: int, 
                                       individuals: Optional[List] = None,
                                       risk_factors_list: Optional[List[RiskFactorProfile]] = None) -> Dict[str, float]:
        """
        模拟人群通路分布
        
        Args:
            population_size: 人群大小
            individuals: 个体列表（可选）
            risk_factors_list: 风险因素列表（可选）
            
        Returns:
            通路分布统计
        """
        # 重置统计
        self.reset_statistics()
        
        # 如果没有提供个体列表，创建模拟个体
        if individuals is None:
            individuals = [MockIndividual() for _ in range(population_size)]
        
        # 批量选择通路
        self.batch_select_pathways(individuals, risk_factors_list)
        
        return {
            "total_population": population_size,
            "serrated_adenoma_count": self.statistics.serrated_adenoma_count,
            "adenoma_carcinoma_count": self.statistics.adenoma_carcinoma_count,
            "serrated_adenoma_percentage": self.statistics.serrated_adenoma_percentage,
            "adenoma_carcinoma_percentage": self.statistics.adenoma_carcinoma_percentage
        }


class MockIndividual:
    """模拟个体类，用于测试"""
    
    def __init__(self, gender: str = "male"):
        self.gender = gender
