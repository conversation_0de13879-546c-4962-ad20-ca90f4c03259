"""
筛查工具成本模型

实现筛查工具的成本配置和管理系统，包括直接成本、间接成本、
地区调整和时间调整等功能。
"""

from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List
from enum import Enum
import logging

from src.core.individual import Individual
from .enums import ScreeningToolType


logger = logging.getLogger(__name__)


class CostCategory(Enum):
    """成本类别枚举"""
    DIRECT_MEDICAL = "direct_medical"           # 直接医疗成本
    DIRECT_NON_MEDICAL = "direct_non_medical"   # 直接非医疗成本
    INDIRECT = "indirect"                       # 间接成本
    INTANGIBLE = "intangible"                   # 无形成本


class CostComponent(Enum):
    """成本组成部分枚举"""
    # 直接医疗成本
    PROCEDURE_FEE = "procedure_fee"             # 检查费用
    MATERIAL_COST = "material_cost"             # 材料费用
    EQUIPMENT_COST = "equipment_cost"           # 设备费用
    STAFF_COST = "staff_cost"                   # 人员费用
    FACILITY_COST = "facility_cost"             # 设施费用
    ANESTHESIA_COST = "anesthesia_cost"         # 麻醉费用
    
    # 直接非医疗成本
    TRANSPORTATION = "transportation"           # 交通费用
    ACCOMMODATION = "accommodation"             # 住宿费用
    CAREGIVER_COST = "caregiver_cost"          # 陪护费用
    
    # 间接成本
    TIME_COST = "time_cost"                     # 时间成本
    PRODUCTIVITY_LOSS = "productivity_loss"     # 生产力损失
    WORK_ABSENCE = "work_absence"               # 工作缺勤
    
    # 无形成本
    PAIN_DISCOMFORT = "pain_discomfort"         # 疼痛不适
    ANXIETY_STRESS = "anxiety_stress"           # 焦虑压力


@dataclass
class CostItem:
    """成本项目配置"""
    
    component: CostComponent
    category: CostCategory
    base_cost: float                            # 基础成本（元）
    currency: str = "CNY"                       # 货币单位
    
    # 成本调整因子
    age_adjustment: Optional[Dict[str, float]] = None
    gender_adjustment: Optional[Dict[str, float]] = None
    region_adjustment: Optional[Dict[str, float]] = None
    time_adjustment: Optional[Dict[str, float]] = None
    
    # 成本范围和不确定性
    min_cost: Optional[float] = None
    max_cost: Optional[float] = None
    uncertainty_std: float = 0.0                # 标准差（用于不确定性分析）
    
    # 成本发生概率
    occurrence_probability: float = 1.0         # 成本发生概率
    
    # 描述信息
    description: str = ""
    data_source: str = ""                       # 数据来源

    def __post_init__(self):
        """验证成本项目配置"""
        if self.base_cost < 0:
            raise ValueError("基础成本不能为负数")
        
        if not 0.0 <= self.occurrence_probability <= 1.0:
            raise ValueError("发生概率必须在0-1之间")
        
        if self.min_cost is not None and self.min_cost > self.base_cost:
            raise ValueError("最小成本不能大于基础成本")
        
        if self.max_cost is not None and self.max_cost < self.base_cost:
            raise ValueError("最大成本不能小于基础成本")


@dataclass
class RegionalCostProfile:
    """地区成本档案"""
    
    region_name: str
    cost_level_index: float = 1.0               # 成本水平指数（相对于基准地区）
    
    # 分类成本调整因子
    medical_cost_multiplier: float = 1.0        # 医疗成本倍数
    labor_cost_multiplier: float = 1.0          # 人工成本倍数
    facility_cost_multiplier: float = 1.0       # 设施成本倍数
    transportation_cost_multiplier: float = 1.0 # 交通成本倍数
    
    # 特殊调整
    urban_rural_factor: float = 1.0             # 城乡差异因子
    hospital_level_factor: float = 1.0          # 医院等级因子


class ScreeningCostModel:
    """筛查工具成本模型"""
    
    def __init__(
        self,
        tool_type: ScreeningToolType,
        cost_items: List[CostItem],
        regional_profile: Optional[RegionalCostProfile] = None,
        reference_year: int = 2023
    ):
        """
        初始化成本模型
        
        Args:
            tool_type: 筛查工具类型
            cost_items: 成本项目列表
            regional_profile: 地区成本档案
            reference_year: 参考年份
        """
        self.tool_type = tool_type
        self.cost_items = {item.component: item for item in cost_items}
        self.regional_profile = regional_profile or RegionalCostProfile("默认地区")
        self.reference_year = reference_year
        
        # 验证成本配置
        self._validate_cost_configuration()
    
    def _validate_cost_configuration(self) -> None:
        """验证成本配置的完整性"""
        required_components = self._get_required_components()
        missing_components = []
        
        for component in required_components:
            if component not in self.cost_items:
                missing_components.append(component)
        
        if missing_components:
            logger.warning(
                f"筛查工具 {self.tool_type} 缺少以下成本组件: {missing_components}"
            )
    
    def _get_required_components(self) -> List[CostComponent]:
        """获取当前工具类型的必需成本组件"""
        # 基础必需组件
        required = [CostComponent.PROCEDURE_FEE, CostComponent.TIME_COST]
        
        # 根据工具类型添加特定组件
        if self.tool_type in [ScreeningToolType.COLONOSCOPY, ScreeningToolType.SIGMOIDOSCOPY]:
            required.extend([
                CostComponent.EQUIPMENT_COST,
                CostComponent.STAFF_COST,
                CostComponent.FACILITY_COST
            ])
        
        if self.tool_type == ScreeningToolType.COLONOSCOPY:
            required.append(CostComponent.ANESTHESIA_COST)
        
        if self.tool_type == ScreeningToolType.FIT:
            required.extend([
                CostComponent.MATERIAL_COST,
                CostComponent.TRANSPORTATION  # 样本运输
            ])
        
        return required
    
    def calculate_total_cost(
        self,
        individual: Individual,
        procedure_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, float]:
        """
        计算个体筛查总成本
        
        Args:
            individual: 个体对象
            procedure_context: 检查上下文信息
            
        Returns:
            Dict[str, float]: 成本分解字典
        """
        context = procedure_context or {}
        cost_breakdown = {}
        total_cost = 0.0
        
        for component, cost_item in self.cost_items.items():
            # 计算单项成本
            item_cost = self._calculate_item_cost(
                cost_item, individual, context
            )
            
            cost_breakdown[component.value] = item_cost
            total_cost += item_cost
        
        # 添加总成本
        cost_breakdown["total_cost"] = total_cost
        
        # 按类别汇总
        category_totals = self._calculate_category_totals(cost_breakdown)
        cost_breakdown.update(category_totals)
        
        return cost_breakdown
    
    def _calculate_item_cost(
        self,
        cost_item: CostItem,
        individual: Individual,
        context: Dict[str, Any]
    ) -> float:
        """计算单项成本"""
        # 基础成本
        base_cost = cost_item.base_cost
        
        # 应用年龄调整
        age_factor = self._calculate_age_adjustment(cost_item, individual.get_current_age())
        
        # 应用性别调整
        gender_factor = self._calculate_gender_adjustment(cost_item, individual.gender)
        
        # 应用地区调整
        region_factor = self._calculate_region_adjustment(cost_item)
        
        # 应用时间调整
        time_factor = self._calculate_time_adjustment(cost_item, context.get("year"))
        
        # 应用检查特异性调整
        procedure_factor = self._calculate_procedure_adjustment(cost_item, context)
        
        # 计算调整后成本
        adjusted_cost = (
            base_cost * 
            age_factor * 
            gender_factor * 
            region_factor * 
            time_factor * 
            procedure_factor
        )
        
        # 应用发生概率
        if cost_item.occurrence_probability < 1.0:
            import random
            if random.random() > cost_item.occurrence_probability:
                adjusted_cost = 0.0
        
        # 应用成本边界
        if cost_item.min_cost is not None:
            adjusted_cost = max(adjusted_cost, cost_item.min_cost)
        if cost_item.max_cost is not None:
            adjusted_cost = min(adjusted_cost, cost_item.max_cost)
        
        return max(0.0, adjusted_cost)
    
    def _calculate_age_adjustment(self, cost_item: CostItem, age: float) -> float:
        """计算年龄调整因子"""
        if not cost_item.age_adjustment:
            return 1.0
        
        # 简化的年龄调整模型
        if age < 65:
            return cost_item.age_adjustment.get("young", 1.0)
        elif age < 75:
            return cost_item.age_adjustment.get("middle", 1.0)
        else:
            return cost_item.age_adjustment.get("elderly", 1.0)
    
    def _calculate_gender_adjustment(self, cost_item: CostItem, gender) -> float:
        """计算性别调整因子"""
        if not cost_item.gender_adjustment:
            return 1.0
        
        gender_str = gender.value if hasattr(gender, 'value') else str(gender)
        return cost_item.gender_adjustment.get(gender_str, 1.0)
    
    def _calculate_region_adjustment(self, cost_item: CostItem) -> float:
        """计算地区调整因子"""
        if not self.regional_profile:
            return 1.0
        
        # 根据成本类别应用不同的地区调整
        if cost_item.category == CostCategory.DIRECT_MEDICAL:
            return self.regional_profile.medical_cost_multiplier
        elif cost_item.component == CostComponent.STAFF_COST:
            return self.regional_profile.labor_cost_multiplier
        elif cost_item.component == CostComponent.FACILITY_COST:
            return self.regional_profile.facility_cost_multiplier
        elif cost_item.component == CostComponent.TRANSPORTATION:
            return self.regional_profile.transportation_cost_multiplier
        else:
            return self.regional_profile.cost_level_index
    
    def _calculate_time_adjustment(self, cost_item: CostItem, year: Optional[int]) -> float:
        """计算时间调整因子（通胀等）"""
        if not year or not cost_item.time_adjustment:
            return 1.0
        
        # 简化的通胀调整模型
        years_diff = year - self.reference_year
        annual_inflation = cost_item.time_adjustment.get("annual_inflation", 0.03)
        
        return (1 + annual_inflation) ** years_diff
    
    def _calculate_procedure_adjustment(
        self, 
        cost_item: CostItem, 
        context: Dict[str, Any]
    ) -> float:
        """计算检查特异性调整因子"""
        factor = 1.0
        
        # 检查复杂度调整
        complexity = context.get("procedure_complexity", "standard")
        if complexity == "complex":
            factor *= 1.3
        elif complexity == "simple":
            factor *= 0.8
        
        # 检查时长调整
        duration_minutes = context.get("procedure_duration_minutes")
        if duration_minutes and cost_item.component == CostComponent.STAFF_COST:
            standard_duration = 30  # 标准检查时长
            factor *= duration_minutes / standard_duration
        
        return factor
    
    def _calculate_category_totals(self, cost_breakdown: Dict[str, float]) -> Dict[str, float]:
        """计算各类别成本总计"""
        category_totals = {}
        
        for component, cost_item in self.cost_items.items():
            category_key = f"{cost_item.category.value}_total"
            if category_key not in category_totals:
                category_totals[category_key] = 0.0
            
            category_totals[category_key] += cost_breakdown.get(component.value, 0.0)
        
        return category_totals
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """获取成本模型摘要"""
        return {
            "tool_type": self.tool_type.value,
            "total_cost_items": len(self.cost_items),
            "regional_profile": self.regional_profile.region_name,
            "reference_year": self.reference_year,
            "cost_components": [item.component.value for item in self.cost_items.values()],
            "base_total_cost": sum(item.base_cost for item in self.cost_items.values())
        }
