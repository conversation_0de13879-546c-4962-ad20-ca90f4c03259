# Story 1.9: 主界面完善和项目管理

## Status

Draft

## Story

**As a** 研究人员和项目管理者，
**I want** 拥有完善的主界面和项目管理功能来组织、跟踪和管理多个模拟项目，
**so that** 能够高效地管理项目生命周期、监控系统状态并快速访问项目相关功能。

## Acceptance Criteria

1. 实现项目管理面板（项目列表树形结构、状态指示器、项目信息预览）
2. 创建右侧属性面板（详细信息显示、快速操作、元数据编辑）
3. 实现状态监控区域（系统资源使用率、后台任务列表、实时通知）
4. 完善左侧导航面板（功能模块导航、快速访问、收藏夹）
5. 添加工具栏快速操作（新建、打开、保存、导入导出、设置）
6. 实现项目模板系统（预定义配置、快速创建、模板管理）
7. 创建最近项目和历史记录管理
8. 添加项目搜索和筛选功能
9. 实现项目备份和恢复功能
10. 确保符合ai-frontend-prompts.md的主界面开发标准

## Tasks / Subtasks

- [ ] 任务1：实现项目管理面板 (AC: 1)
  - [ ] 扩展src/interfaces/desktop/widgets/project_management_panel.py
  - [ ] 实现ProjectManagementPanel项目管理面板
  - [ ] 创建项目列表树形结构（QTreeWidget，支持分组）
  - [ ] 添加项目状态指示器（进行中/已完成/草稿/错误）
  - [ ] 实现项目信息预览（模拟人数、运行时间、最后更新）
  - [ ] 创建右键上下文菜单（打开、复制、删除、导出、属性）
  - [ ] 添加双击打开项目功能
  - [ ] 实现项目拖拽排序和分组

- [ ] 任务2：创建右侧属性面板 (AC: 2)
  - [ ] 创建src/interfaces/desktop/widgets/project_properties_panel.py
  - [ ] 实现ProjectPropertiesPanel属性面板
  - [ ] 添加项目详细信息显示（创建时间、修改时间、大小、描述）
  - [ ] 创建快速操作按钮（运行、编辑、复制、删除）
  - [ ] 实现项目元数据编辑（标题、描述、标签、分类）
  - [ ] 添加项目统计信息（文件数量、数据大小、运行历史）
  - [ ] 创建项目依赖关系显示
  - [ ] 实现属性实时更新

- [ ] 任务3：实现状态监控区域 (AC: 3)
  - [ ] 创建src/interfaces/desktop/widgets/system_status_monitor.py
  - [ ] 实现SystemStatusMonitor状态监控组件
  - [ ] 添加系统资源使用率显示（CPU、内存、磁盘使用率）
  - [ ] 创建后台任务列表（当前运行的模拟、导入导出任务）
  - [ ] 实现实时通知系统（成功、警告、错误消息）
  - [ ] 添加任务进度监控（进度条、预估完成时间）
  - [ ] 创建系统健康状态指示器
  - [ ] 实现通知历史记录和清理

- [ ] 任务4：完善左侧导航面板 (AC: 4)
  - [ ] 扩展src/interfaces/desktop/widgets/navigation_panel.py
  - [ ] 实现NavigationPanel导航面板增强
  - [ ] 添加功能模块导航（人群设置、疾病进展、筛查策略等）
  - [ ] 创建快速访问工具栏（常用功能、最近使用）
  - [ ] 实现收藏夹功能（用户自定义快捷方式）
  - [ ] 添加导航历史记录（前进、后退按钮）
  - [ ] 创建可折叠的导航分组
  - [ ] 实现导航面板自定义布局

- [ ] 任务5：添加工具栏快速操作 (AC: 5)
  - [ ] 扩展src/interfaces/desktop/windows/main_window.py工具栏
  - [ ] 实现增强的工具栏快速操作
  - [ ] 添加新建项目按钮（主要按钮，深蓝色背景）
  - [ ] 创建打开项目功能（文件对话框，最近项目列表）
  - [ ] 实现保存项目（Ctrl+S快捷键，自动保存）
  - [ ] 添加导入数据文件（支持拖拽到窗口）
  - [ ] 创建导出结果（多格式选择：Excel、PDF、CSV）
  - [ ] 实现应用设置快速访问

- [ ] 任务6：实现项目模板系统 (AC: 6)
  - [ ] 创建src/interfaces/desktop/services/project_template_service.py
  - [ ] 实现ProjectTemplateService模板服务
  - [ ] 添加预定义项目模板（基础筛查、高级分析、经济评估）
  - [ ] 创建模板快速创建向导
  - [ ] 实现自定义模板保存和管理
  - [ ] 添加模板预览和描述
  - [ ] 创建模板导入导出功能
  - [ ] 实现模板版本管理

- [ ] 任务7：创建最近项目和历史记录 (AC: 7)
  - [ ] 创建src/interfaces/desktop/services/project_history_service.py
  - [ ] 实现ProjectHistoryService历史服务
  - [ ] 添加最近项目列表（最多20个）
  - [ ] 创建项目访问历史记录
  - [ ] 实现历史记录搜索和筛选
  - [ ] 添加收藏项目功能
  - [ ] 创建历史记录清理和管理
  - [ ] 实现快速访问面板

- [ ] 任务8：添加项目搜索和筛选 (AC: 8)
  - [ ] 创建src/interfaces/desktop/widgets/project_search_widget.py
  - [ ] 实现ProjectSearchWidget搜索组件
  - [ ] 添加全文搜索功能（项目名称、描述、标签）
  - [ ] 创建高级筛选（状态、创建时间、大小、类型）
  - [ ] 实现搜索结果高亮显示
  - [ ] 添加保存搜索条件功能
  - [ ] 创建搜索历史记录
  - [ ] 实现实时搜索建议

- [ ] 任务9：实现项目备份和恢复 (AC: 9)
  - [ ] 创建src/interfaces/desktop/services/project_backup_service.py
  - [ ] 实现ProjectBackupService备份服务
  - [ ] 添加自动备份功能（定时、版本控制）
  - [ ] 创建手动备份和恢复界面
  - [ ] 实现增量备份和压缩
  - [ ] 添加备份验证和完整性检查
  - [ ] 创建备份历史管理
  - [ ] 实现云端备份集成（可选）

## Dev Notes

### 架构上下文 [Source: architecture/frontend-architecture.md]

**主界面组件位置**：
- 主窗口：src/interfaces/desktop/windows/main_window.py
- 项目管理：src/interfaces/desktop/widgets/project_management_panel.py
- 导航面板：src/interfaces/desktop/widgets/navigation_panel.py
- 属性面板：src/interfaces/desktop/widgets/project_properties_panel.py
- 状态监控：src/interfaces/desktop/widgets/system_status_monitor.py

**主窗口布局结构** [Source: architecture/frontend-architecture.md]：
```python
# 主窗口布局
layout = QHBoxLayout(self.central_widget)
self.nav_panel = NavigationPanel()          # 左侧导航
self.content_stack = QStackedWidget()       # 中央内容区域
self.properties_panel = PropertiesPanel()   # 右侧属性面板
```

### 技术栈要求 [Source: architecture/tech-stack.md]

**核心技术**：
- **桌面框架**：PyQt6 6.5+（原生性能，丰富UI组件）
- **数据库**：SQLite 3.40+（本地数据存储）
- **ORM**：SQLAlchemy 2.0+（数据库抽象层）
- **配置管理**：PyYAML 6.0+（配置文件处理）

### 项目数据模型 [Source: architecture/data-models.md]

**项目相关实体**：
- **Project**：项目基本信息
- **ProjectTemplate**：项目模板
- **ProjectHistory**：项目历史记录
- **ProjectBackup**：项目备份记录
- **UserPreferences**：用户偏好设置

### 主界面设计要求 [Source: docs/ai-frontend-prompts.md]

**布局结构**：
- 顶部菜单栏：文件、编辑、视图、工具、帮助（原生菜单栏）
- 工具栏：新建、打开、保存、导出、设置等快捷按钮
- 左侧导航面板：项目管理、人群设置、疾病进展、筛查策略等
- 中央内容区域：当前功能的主要工作区域
- 右侧属性面板：当前选中项目的详细信息和快速操作
- 底部状态栏：进度信息、系统状态、内存使用等

**关键组件要求**：
- 项目列表（树形结构）
- 项目状态指示器（进行中/已完成/草稿）
- 项目信息预览（模拟人数、运行时间、最后更新）
- 右键上下文菜单：打开、复制、删除、导出
- 双击打开项目

### 性能和用户体验

**响应性要求**：
- 原生桌面应用外观和感觉
- 清晰的视觉层次，合理的间距
- 可调整的面板大小（分割器）
- 加载状态指示器和空状态处理
- 实时数据更新指示器

**技术实现**：
- 使用PyQt6的布局管理器
- QListWidget/QTreeWidget实现虚拟滚动
- QThread用于后台任务和实时更新
- QSettings保存用户偏好设置
- SQLite数据库缓存

### 项目结构对齐 [Source: architecture/unified-project-structure.md]

**文件位置**：
- 主窗口：src/interfaces/desktop/windows/main_window.py
- 项目组件：src/interfaces/desktop/widgets/project_*
- 项目服务：src/interfaces/desktop/services/project_*
- 数据模型：src/database/models.py
- 工具函数：src/interfaces/desktop/utils/

## Testing

### 测试文件位置 [Source: architecture/unified-project-structure.md]

- `tests/unit/widgets/test_project_management_panel.py`
- `tests/unit/widgets/test_project_properties_panel.py`
- `tests/unit/widgets/test_system_status_monitor.py`
- `tests/unit/widgets/test_navigation_panel.py`
- `tests/unit/services/test_project_template_service.py`
- `tests/unit/services/test_project_history_service.py`
- `tests/unit/services/test_project_backup_service.py`
- `tests/integration/test_main_window_integration.py`

### 测试标准

- 项目CRUD操作测试（创建、读取、更新、删除）
- 项目模板系统测试
- 搜索和筛选功能测试
- 备份和恢复功能测试
- 用户界面响应性测试
- 多项目并发管理测试
- 系统资源监控准确性测试

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-31 | 1.0 | 初始故事创建，基于ai-frontend-prompts.md分析 | Bob (SM) |

## Dev Agent Record

*此部分将由开发代理在实施过程中填充*

### Agent Model Used

*待填充*

### Debug Log References

*待填充*

### Completion Notes List

*待填充*

### File List

*待填充*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填充*
