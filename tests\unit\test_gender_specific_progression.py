"""
性别特异性进展倍数单元测试

测试性别特异性进展率、风险因素权重和统计跟踪功能。
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch

from src.core.enums import Gender, DiseaseState
from src.modules.disease.adenoma_progression import (
    AdenomaProgressionModel,
    GenderSpecificProgressionRates,
    GenderRiskFactorWeights
)
from src.modules.disease.dwell_time_model import (
    DwellTimeModel,
    GenderSpecificDwellTimeDistribution
)
from src.modules.disease.cancer_incidence import CancerIncidenceModel
from src.modules.disease.gender_statistics_tracker import GenderStatisticsTracker


class TestGenderSpecificProgressionRates:
    """测试性别特异性进展率"""

    def test_default_gender_specific_rates(self):
        """测试默认性别特异性进展率"""
        model = AdenomaProgressionModel()
        
        # 验证男性进展率
        male_rates = model.gender_specific_rates["male"]
        assert male_rates.adenoma_initiation_rate == 1.2
        assert male_rates.low_to_high_progression_rate == 1.1
        assert male_rates.high_to_preclinical_rate == 1.15
        assert male_rates.preclinical_to_clinical_rate == 1.0
        assert male_rates.cancer_stage_progression_rate == 1.05
        
        # 验证女性进展率
        female_rates = model.gender_specific_rates["female"]
        assert female_rates.adenoma_initiation_rate == 1.0
        assert female_rates.low_to_high_progression_rate == 1.0
        assert female_rates.high_to_preclinical_rate == 1.0
        assert female_rates.preclinical_to_clinical_rate == 1.0
        assert female_rates.cancer_stage_progression_rate == 1.0

    def test_gender_specific_progression_rate_calculation(self):
        """测试性别特异性进展率计算"""
        model = AdenomaProgressionModel()
        
        # 测试男性进展率
        male_rate = model.get_gender_specific_progression_rate(
            "low_to_high_adenoma", Gender.MALE
        )
        assert male_rate == 1.1
        
        # 测试女性进展率
        female_rate = model.get_gender_specific_progression_rate(
            "low_to_high_adenoma", Gender.FEMALE
        )
        assert female_rate == 1.0
        
        # 测试未知转换类型
        unknown_rate = model.get_gender_specific_progression_rate(
            "unknown_transition", Gender.MALE
        )
        assert unknown_rate == 1.0

    def test_gender_adjusted_progression_probability(self):
        """测试性别调整的进展概率"""
        model = AdenomaProgressionModel()
        
        # 测试男性进展概率
        male_prob = model.calculate_gender_adjusted_progression_probability(
            "low_to_high_adenoma", 60.0, 3.0, Gender.MALE
        )
        
        # 测试女性进展概率
        female_prob = model.calculate_gender_adjusted_progression_probability(
            "low_to_high_adenoma", 60.0, 3.0, Gender.FEMALE
        )
        
        # 男性进展概率应该高于女性
        assert male_prob > female_prob
        
        # 概率应该在有效范围内
        assert 0.0 <= male_prob <= 1.0
        assert 0.0 <= female_prob <= 1.0

    def test_risk_factor_adjustment(self):
        """测试风险因素调整"""
        model = AdenomaProgressionModel()
        
        risk_factors = {
            "smoking": 0.8,
            "alcohol": 0.6,
            "obesity": 0.4
        }
        
        # 测试男性风险因素调整
        male_adjustment = model._calculate_risk_factor_adjustment(risk_factors, "male")
        
        # 测试女性风险因素调整
        female_adjustment = model._calculate_risk_factor_adjustment(risk_factors, "female")
        
        # 调整因子应该大于1（增加风险）
        assert male_adjustment > 1.0
        assert female_adjustment > 1.0
        
        # 男性吸烟和饮酒的风险调整应该更高
        assert male_adjustment != female_adjustment


class TestGenderRiskFactorWeights:
    """测试性别特异性风险因素权重"""

    def test_default_gender_risk_weights(self):
        """测试默认性别风险因素权重"""
        model = AdenomaProgressionModel()
        
        # 验证男性风险权重
        male_weights = model.gender_risk_weights["male"]
        assert male_weights.smoking_weight == 1.3
        assert male_weights.alcohol_weight == 1.2
        assert male_weights.obesity_weight == 1.1
        assert male_weights.family_history_weight == 1.0
        assert male_weights.inflammatory_bowel_disease_weight == 1.0
        
        # 验证女性风险权重
        female_weights = model.gender_risk_weights["female"]
        assert female_weights.smoking_weight == 1.0
        assert female_weights.alcohol_weight == 1.0
        assert female_weights.obesity_weight == 1.2
        assert female_weights.family_history_weight == 1.0
        assert female_weights.inflammatory_bowel_disease_weight == 1.1

    def test_update_gender_risk_weights(self):
        """测试更新性别风险因素权重"""
        model = AdenomaProgressionModel()
        
        new_weights = GenderRiskFactorWeights(
            smoking_weight=1.5,
            alcohol_weight=1.4,
            obesity_weight=1.3,
            family_history_weight=1.1,
            inflammatory_bowel_disease_weight=1.2
        )
        
        model.update_gender_risk_weights("male", new_weights)
        
        # 验证更新后的权重
        updated_weights = model.gender_risk_weights["male"]
        assert updated_weights.smoking_weight == 1.5
        assert updated_weights.alcohol_weight == 1.4
        assert updated_weights.obesity_weight == 1.3


class TestGenderSpecificDwellTime:
    """测试性别特异性停留时间"""

    def test_gender_specific_dwell_time_sampling(self):
        """测试性别特异性停留时间抽样"""
        model = DwellTimeModel()
        
        # 测试男性停留时间
        male_times = []
        for _ in range(100):
            time = model.sample_dwell_time("low_risk_adenoma", age=60.0, gender="male")
            male_times.append(time)
        
        # 测试女性停留时间
        female_times = []
        for _ in range(100):
            time = model.sample_dwell_time("low_risk_adenoma", age=60.0, gender="female")
            female_times.append(time)
        
        # 女性平均停留时间应该长于男性
        assert np.mean(female_times) > np.mean(male_times)
        
        # 所有时间都应该为正数
        assert all(t > 0 for t in male_times)
        assert all(t > 0 for t in female_times)

    def test_gender_specific_distribution_parameters(self):
        """测试性别特异性分布参数"""
        model = DwellTimeModel()
        
        # 验证男性分布参数
        male_dist = model.gender_specific_distributions["male"]["low_risk_adenoma"]
        assert male_dist.mean == 4.5
        assert male_dist.std == 1.8
        
        # 验证女性分布参数
        female_dist = model.gender_specific_distributions["female"]["low_risk_adenoma"]
        assert female_dist.mean == 5.5
        assert female_dist.std == 2.2
        
        # 女性平均停留时间应该长于男性
        assert female_dist.mean > male_dist.mean


class TestGenderAdjustedCancerIncidence:
    """测试性别调整的癌症发病率"""

    def test_gender_adjusted_cancer_incidence(self):
        """测试性别调整的癌症发病概率"""
        model = CancerIncidenceModel()
        
        # 测试男性癌症发病概率
        male_prob = model.calculate_gender_adjusted_cancer_incidence_probability(
            age=65.0,
            gender="male",
            anatomical_location="rectum"
        )
        
        # 测试女性癌症发病概率
        female_prob = model.calculate_gender_adjusted_cancer_incidence_probability(
            age=65.0,
            gender="female",
            anatomical_location="rectum"
        )
        
        # 男性直肠癌发病概率应该高于女性
        assert male_prob > female_prob
        
        # 概率应该在有效范围内
        assert 0.0 <= male_prob <= 1.0
        assert 0.0 <= female_prob <= 1.0

    def test_gender_specific_adjustment_factors(self):
        """测试性别特异性调整因子"""
        model = CancerIncidenceModel()
        
        # 测试男性调整因子
        male_adjustment = model._get_gender_specific_adjustment("male", "rectum", 65.0)
        
        # 测试女性调整因子
        female_adjustment = model._get_gender_specific_adjustment("female", "rectum", 65.0)
        
        # 男性直肠癌调整因子应该高于女性
        assert male_adjustment > female_adjustment
        
        # 调整因子应该在合理范围内
        assert 0.5 <= male_adjustment <= 2.0
        assert 0.5 <= female_adjustment <= 2.0

    def test_risk_factor_adjustment_cancer(self):
        """测试癌症发病的风险因素调整"""
        model = CancerIncidenceModel()
        
        risk_factors = {
            "smoking": 0.8,
            "alcohol": 0.6,
            "obesity": 0.4
        }
        
        # 测试男性风险因素调整
        male_adjustment = model._calculate_risk_factor_adjustment(risk_factors, "male")
        
        # 测试女性风险因素调整
        female_adjustment = model._calculate_risk_factor_adjustment(risk_factors, "female")
        
        # 调整因子应该大于1
        assert male_adjustment > 1.0
        assert female_adjustment > 1.0
        
        # 男性和女性的调整应该不同
        assert male_adjustment != female_adjustment


class TestGenderStatisticsTracker:
    """测试性别差异统计跟踪器"""

    def test_statistics_tracker_initialization(self):
        """测试统计跟踪器初始化"""
        tracker = GenderStatisticsTracker()
        
        # 验证转换统计初始化
        assert "adenoma_initiation" in tracker.transition_statistics
        assert "low_to_high_adenoma" in tracker.transition_statistics
        
        # 验证解剖位置统计初始化
        assert "proximal_colon" in tracker.anatomical_location_statistics
        assert "distal_colon" in tracker.anatomical_location_statistics
        assert "rectum" in tracker.anatomical_location_statistics

    def test_record_adenoma_initiation(self):
        """测试记录腺瘤产生事件"""
        tracker = GenderStatisticsTracker()
        
        # 记录男性腺瘤产生
        tracker.record_adenoma_initiation(55.0, Gender.MALE)
        
        # 记录女性腺瘤产生
        tracker.record_adenoma_initiation(58.0, Gender.FEMALE)
        
        # 验证统计信息
        male_stats = tracker.transition_statistics["adenoma_initiation"].male_stats
        female_stats = tracker.transition_statistics["adenoma_initiation"].female_stats
        
        assert male_stats.sample_count == 1
        assert female_stats.sample_count == 1
        assert male_stats.mean_age_at_onset == 55.0
        assert female_stats.mean_age_at_onset == 58.0

    def test_record_progression_event(self):
        """测试记录进展事件"""
        tracker = GenderStatisticsTracker()
        
        # 记录男性进展事件
        tracker.record_progression_event(
            "low_to_high_adenoma", 60.0, Gender.MALE, 3.5, "proximal_colon"
        )
        
        # 记录女性进展事件
        tracker.record_progression_event(
            "low_to_high_adenoma", 62.0, Gender.FEMALE, 4.2, "proximal_colon"
        )
        
        # 验证统计信息
        transition_stats = tracker.transition_statistics["low_to_high_adenoma"]
        assert transition_stats.male_stats.sample_count == 1
        assert transition_stats.female_stats.sample_count == 1
        
        # 验证解剖位置统计
        location_stats = tracker.anatomical_location_statistics["proximal_colon"]
        assert location_stats["male"].sample_count == 1
        assert location_stats["female"].sample_count == 1

    def test_gender_differences_summary(self):
        """测试性别差异摘要"""
        tracker = GenderStatisticsTracker()
        
        # 添加一些测试数据
        for i in range(10):
            tracker.record_adenoma_initiation(50 + i, Gender.MALE)
            tracker.record_adenoma_initiation(52 + i, Gender.FEMALE)
        
        # 获取摘要
        summary = tracker.get_gender_differences_summary()
        
        # 验证摘要结构
        assert "transition_differences" in summary
        assert "anatomical_location_differences" in summary
        assert "age_group_differences" in summary
        
        # 验证转换差异
        if "adenoma_initiation" in summary["transition_differences"]:
            adenoma_diff = summary["transition_differences"]["adenoma_initiation"]
            assert "male_female_ratio" in adenoma_diff
            assert "time_difference" in adenoma_diff

    def test_statistical_tests(self):
        """测试统计显著性检验"""
        tracker = GenderStatisticsTracker()
        
        # 添加足够的测试数据
        np.random.seed(42)
        for i in range(30):
            # 男性进展时间稍短
            male_time = np.random.normal(3.0, 1.0)
            female_time = np.random.normal(3.5, 1.0)
            
            tracker.record_progression_event(
                "low_to_high_adenoma", 60.0, Gender.MALE, male_time
            )
            tracker.record_progression_event(
                "low_to_high_adenoma", 60.0, Gender.FEMALE, female_time
            )
        
        # 执行统计检验
        test_results = tracker.perform_statistical_tests()
        
        # 验证检验结果
        assert "low_to_high_adenoma_time_difference" in test_results
        test_result = test_results["low_to_high_adenoma_time_difference"]
        assert "t_statistic" in test_result
        assert "p_value" in test_result
        assert "significant" in test_result


class TestGenderDifferencesIntegration:
    """测试性别差异的集成功能"""

    def test_gender_differences_consistency(self):
        """测试性别差异的一致性"""
        progression_model = AdenomaProgressionModel()
        dwell_time_model = DwellTimeModel()
        cancer_model = CancerIncidenceModel()
        
        # 验证男性进展更快的一致性
        # 1. 进展概率更高
        male_prog_prob = progression_model.calculate_gender_adjusted_progression_probability(
            "low_to_high_adenoma", 60.0, 3.0, Gender.MALE
        )
        female_prog_prob = progression_model.calculate_gender_adjusted_progression_probability(
            "low_to_high_adenoma", 60.0, 3.0, Gender.FEMALE
        )
        assert male_prog_prob > female_prog_prob
        
        # 2. 停留时间更短
        male_dwell_times = [
            dwell_time_model.sample_dwell_time("low_risk_adenoma", 60.0, "male")
            for _ in range(50)
        ]
        female_dwell_times = [
            dwell_time_model.sample_dwell_time("low_risk_adenoma", 60.0, "female")
            for _ in range(50)
        ]
        assert np.mean(male_dwell_times) < np.mean(female_dwell_times)
        
        # 3. 癌症发病率更高（某些位置）
        male_cancer_prob = cancer_model.calculate_gender_adjusted_cancer_incidence_probability(
            65.0, "male", "rectum"
        )
        female_cancer_prob = cancer_model.calculate_gender_adjusted_cancer_incidence_probability(
            65.0, "female", "rectum"
        )
        assert male_cancer_prob > female_cancer_prob

    def test_model_parameter_updates(self):
        """测试模型参数更新功能"""
        model = AdenomaProgressionModel()
        
        # 更新性别特异性进展率
        new_rates = GenderSpecificProgressionRates(
            adenoma_initiation_rate=1.3,
            low_to_high_progression_rate=1.2,
            high_to_preclinical_rate=1.25,
            preclinical_to_clinical_rate=1.1,
            cancer_stage_progression_rate=1.15
        )
        
        model.update_gender_specific_rates("male", new_rates)
        
        # 验证更新
        updated_rates = model.gender_specific_rates["male"]
        assert updated_rates.adenoma_initiation_rate == 1.3
        assert updated_rates.low_to_high_progression_rate == 1.2
        
        # 验证参数导出
        params = model.get_model_parameters()
        assert "gender_specific_rates" in params
        assert "gender_risk_weights" in params


if __name__ == "__main__":
    pytest.main([__file__])
