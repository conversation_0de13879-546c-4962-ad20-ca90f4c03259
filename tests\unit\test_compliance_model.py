"""
依从性模型单元测试

测试ComplianceModel类的各项功能，包括依从性概率计算、
诊断依从性建模和配置验证。
"""

import pytest
import yaml
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.modules.screening.compliance_model import (
    ComplianceModel, 
    ComplianceParameters, 
    ScreeningEvent,
    ComplianceFactorType
)
from src.modules.screening.enums import ScreeningToolType, ScreeningResult
from src.core.individual import Individual
from src.core.enums import Gender, DiseaseState


class TestComplianceParameters:
    """测试ComplianceParameters数据类"""
    
    def test_valid_parameters(self):
        """测试有效参数创建"""
        params = ComplianceParameters(
            base_compliance_rate=0.65,
            first_time_multiplier=0.8,
            repeat_multiplier=1.2,
            positive_followup_rate=0.75,
            time_decay_factor=0.05
        )
        
        assert params.base_compliance_rate == 0.65
        assert params.first_time_multiplier == 0.8
        assert params.repeat_multiplier == 1.2
        assert params.positive_followup_rate == 0.75
        assert params.time_decay_factor == 0.05
    
    def test_invalid_base_compliance_rate(self):
        """测试无效的基础依从性率"""
        with pytest.raises(ValueError, match="基础依从性率必须在0-1之间"):
            ComplianceParameters(
                base_compliance_rate=1.5,  # 无效值
                first_time_multiplier=0.8,
                repeat_multiplier=1.2,
                positive_followup_rate=0.75,
                time_decay_factor=0.05
            )
    
    def test_invalid_followup_rate(self):
        """测试无效的后续检查依从性率"""
        with pytest.raises(ValueError, match="阳性后续检查依从性必须在0-1之间"):
            ComplianceParameters(
                base_compliance_rate=0.65,
                first_time_multiplier=0.8,
                repeat_multiplier=1.2,
                positive_followup_rate=-0.1,  # 无效值
                time_decay_factor=0.05
            )
    
    def test_negative_multipliers(self):
        """测试负数倍数"""
        with pytest.raises(ValueError, match="首次筛查倍数不能为负数"):
            ComplianceParameters(
                base_compliance_rate=0.65,
                first_time_multiplier=-0.1,  # 无效值
                repeat_multiplier=1.2,
                positive_followup_rate=0.75,
                time_decay_factor=0.05
            )


class TestComplianceModel:
    """测试ComplianceModel类"""
    
    @pytest.fixture
    def sample_config(self):
        """提供测试用的配置"""
        return {
            'base_rates': {
                'fit_screening': 0.65,
                'colonoscopy_screening': 0.45,
                'sigmoidoscopy_screening': 0.55
            },
            'temporal_patterns': {
                'first_time_multiplier': 0.8,
                'repeat_multiplier': 1.2,
                'time_decay_rate': 0.05
            },
            'followup_compliance': {
                'positive_fit_to_colonoscopy': 0.75,
                'waiting_time_impact': -0.02,
                'multiple_positive_decay': 0.9
            },
            'factor_weights': {}
        }
    
    @pytest.fixture
    def sample_individual(self):
        """提供测试用的个体"""
        return Individual(
            birth_year=1970,
            gender=Gender.MALE,
            individual_id="test_001"
        )
    
    def test_model_initialization(self, sample_config):
        """测试模型初始化"""
        model = ComplianceModel(sample_config)
        
        assert model.base_rates == sample_config['base_rates']
        assert model.temporal_patterns == sample_config['temporal_patterns']
        assert model.followup_compliance == sample_config['followup_compliance']
    
    def test_invalid_config_missing_base_rates(self):
        """测试缺少基础依从性率的配置"""
        invalid_config = {
            'base_rates': {
                'fit_screening': 0.65
                # 缺少其他必需的依从性率
            },
            'temporal_patterns': {
                'first_time_multiplier': 0.8,
                'repeat_multiplier': 1.2,
                'time_decay_rate': 0.05
            }
        }
        
        with pytest.raises(ValueError, match="缺少必需的基础依从性率配置"):
            ComplianceModel(invalid_config)
    
    def test_invalid_compliance_rate_range(self):
        """测试依从性率超出范围"""
        invalid_config = {
            'base_rates': {
                'fit_screening': 1.5,  # 无效值
                'colonoscopy_screening': 0.45,
                'sigmoidoscopy_screening': 0.55
            },
            'temporal_patterns': {
                'first_time_multiplier': 0.8,
                'repeat_multiplier': 1.2,
                'time_decay_rate': 0.05
            }
        }
        
        with pytest.raises(ValueError, match="依从性率.*必须在0-1之间"):
            ComplianceModel(invalid_config)
    
    def test_calculate_compliance_probability_first_time(self, sample_config, sample_individual):
        """测试首次筛查依从性概率计算"""
        model = ComplianceModel(sample_config)
        
        # 空的筛查历史表示首次筛查
        screening_history = []
        
        prob = model.calculate_compliance_probability(
            sample_individual, 
            ScreeningToolType.FIT, 
            screening_history
        )
        
        # 预期值：基础率(0.65) * 首次倍数(0.8) * 个体因素(1.0) = 0.52
        expected = 0.65 * 0.8 * 1.0
        assert abs(prob - expected) < 0.001
    
    def test_calculate_compliance_probability_repeat(self, sample_config, sample_individual):
        """测试重复筛查依从性概率计算"""
        model = ComplianceModel(sample_config)
        
        # 创建筛查历史（1年前的筛查）
        past_screening = ScreeningEvent(
            date=datetime.now() - timedelta(days=365),
            tool_type=ScreeningToolType.FIT,
            result=ScreeningResult.NEGATIVE,
            individual_id=sample_individual.individual_id
        )
        screening_history = [past_screening]
        
        prob = model.calculate_compliance_probability(
            sample_individual, 
            ScreeningToolType.FIT, 
            screening_history
        )
        
        # 验证概率在合理范围内
        assert 0.0 <= prob <= 1.0
        
        # 验证重复筛查的概率计算包含时间衰减
        # 基础率(0.65) * 重复倍数(1.2) * 衰减因子((1-0.05)^1) ≈ 0.741
        expected_base = 0.65 * 1.2 * (0.95 ** 1)
        assert abs(prob - expected_base) < 0.01
    
    def test_get_base_rate_for_tool(self, sample_config):
        """测试获取工具特异性基础依从性率"""
        model = ComplianceModel(sample_config)
        
        # 测试已配置的工具
        assert model._get_base_rate_for_tool(ScreeningToolType.FIT) == 0.65
        assert model._get_base_rate_for_tool(ScreeningToolType.COLONOSCOPY) == 0.45
        assert model._get_base_rate_for_tool(ScreeningToolType.SIGMOIDOSCOPY) == 0.55
        
        # 测试未配置的工具（RISK_QUESTIONNAIRE映射到FIT的依从性率）
        assert model._get_base_rate_for_tool(ScreeningToolType.RISK_QUESTIONNAIRE) == 0.65
    
    def test_calculate_diagnostic_compliance(self, sample_config, sample_individual):
        """测试诊断依从性计算"""
        model = ComplianceModel(sample_config)
        
        positive_result = ScreeningEvent(
            date=datetime.now(),
            tool_type=ScreeningToolType.FIT,
            result=ScreeningResult.POSITIVE,
            individual_id=sample_individual.individual_id
        )
        
        # 测试无等待时间的诊断依从性
        diagnostic_compliance = model.calculate_diagnostic_compliance(
            sample_individual, 
            positive_result, 
            waiting_time_weeks=0.0
        )
        
        # 预期值：基础率(0.75) + 等待影响(0) * 个体因素(1.0) = 0.75
        assert abs(diagnostic_compliance - 0.75) < 0.001
        
        # 测试有等待时间的诊断依从性
        diagnostic_compliance_with_wait = model.calculate_diagnostic_compliance(
            sample_individual, 
            positive_result, 
            waiting_time_weeks=4.0
        )
        
        # 预期值：基础率(0.75) + 等待影响(-0.02*4) * 个体因素(1.0) = 0.67
        expected = (0.75 + (-0.02 * 4)) * 1.0
        assert abs(diagnostic_compliance_with_wait - expected) < 0.001

    def test_calculate_diagnostic_compliance_multiple_positives(self, sample_config, sample_individual):
        """测试多次阳性结果对诊断依从性的影响"""
        model = ComplianceModel(sample_config)

        # 创建多次阳性结果的历史
        screening_history = [
            ScreeningEvent(
                date=datetime.now() - timedelta(days=365),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.POSITIVE,
                individual_id=sample_individual.individual_id
            ),
            ScreeningEvent(
                date=datetime.now() - timedelta(days=180),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.POSITIVE,
                individual_id=sample_individual.individual_id
            )
        ]

        current_positive = ScreeningEvent(
            date=datetime.now(),
            tool_type=ScreeningToolType.FIT,
            result=ScreeningResult.POSITIVE,
            individual_id=sample_individual.individual_id
        )

        diagnostic_compliance = model.calculate_diagnostic_compliance(
            sample_individual,
            current_positive,
            waiting_time_weeks=0.0,
            screening_history=screening_history
        )

        # 验证多次阳性会降低依从性
        # 基础率(0.75) * 衰减因子(0.9^(2-1)) = 0.75 * 0.9 = 0.675
        expected = 0.75 * (0.9 ** (2 - 1))
        assert abs(diagnostic_compliance - expected) < 0.01

    def test_get_diagnostic_base_rate(self, sample_config):
        """测试获取诊断依从性基础率"""
        model = ComplianceModel(sample_config)

        # 测试FIT工具
        assert model._get_diagnostic_base_rate(ScreeningToolType.FIT) == 0.75

        # 测试未配置的工具（应返回默认值）
        assert model._get_diagnostic_base_rate(ScreeningToolType.COLONOSCOPY) == 0.75

    def test_calculate_waiting_time_impact(self, sample_config):
        """测试等待时间影响计算"""
        model = ComplianceModel(sample_config)

        # 测试无等待时间
        assert model._calculate_waiting_time_impact(0.0) == 0.0

        # 测试正常等待时间
        impact = model._calculate_waiting_time_impact(4.0)
        expected = -0.02 * 4.0
        assert abs(impact - expected) < 0.001

        # 测试超过最大等待时间（假设最大12周）
        impact_max = model._calculate_waiting_time_impact(20.0)
        expected_max = -0.02 * 12.0  # 应该被限制在12周
        assert abs(impact_max - expected_max) < 0.001

    def test_calculate_multiple_positive_factor(self, sample_config, sample_individual):
        """测试多次阳性因子计算"""
        model = ComplianceModel(sample_config)

        # 测试无阳性历史
        empty_history = []
        factor = model._calculate_multiple_positive_factor(sample_individual, empty_history)
        assert factor == 1.0

        # 测试单次阳性历史
        single_positive_history = [
            ScreeningEvent(
                date=datetime.now() - timedelta(days=365),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.POSITIVE,
                individual_id=sample_individual.individual_id
            )
        ]
        factor_single = model._calculate_multiple_positive_factor(sample_individual, single_positive_history)
        assert factor_single == 1.0

        # 测试多次阳性历史
        multiple_positive_history = [
            ScreeningEvent(
                date=datetime.now() - timedelta(days=365),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.POSITIVE,
                individual_id=sample_individual.individual_id
            ),
            ScreeningEvent(
                date=datetime.now() - timedelta(days=180),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.POSITIVE,
                individual_id=sample_individual.individual_id
            )
        ]
        factor_multiple = model._calculate_multiple_positive_factor(sample_individual, multiple_positive_history)
        expected = 0.9 ** (2 - 1)  # 2次阳性，衰减因子0.9
        assert abs(factor_multiple - expected) < 0.001
    
    def test_simulate_compliance_decision(self, sample_config, sample_individual):
        """测试依从性决策模拟"""
        model = ComplianceModel(sample_config)
        
        # 使用固定随机种子确保可重复性
        with patch('random.random', return_value=0.3):
            # 依从性概率 > 0.3，应该返回True
            decision = model.simulate_compliance_decision(
                sample_individual, 
                ScreeningToolType.FIT, 
                []
            )
            # 首次FIT筛查概率 = 0.65 * 0.8 = 0.52 > 0.3
            assert decision is True
        
        with patch('random.random', return_value=0.8):
            # 依从性概率 < 0.8，应该返回False
            decision = model.simulate_compliance_decision(
                sample_individual, 
                ScreeningToolType.FIT, 
                []
            )
            # 首次FIT筛查概率 = 0.52 < 0.8
            assert decision is False
    
    def test_get_compliance_parameters(self, sample_config):
        """测试获取依从性参数"""
        model = ComplianceModel(sample_config)
        
        params = model.get_compliance_parameters(ScreeningToolType.FIT)
        
        assert isinstance(params, ComplianceParameters)
        assert params.base_compliance_rate == 0.65
        assert params.first_time_multiplier == 0.8
        assert params.repeat_multiplier == 1.2
        assert params.positive_followup_rate == 0.75
        assert params.time_decay_factor == 0.05
    
    def test_validate_compliance_rates(self, sample_config):
        """测试依从性率验证"""
        model = ComplianceModel(sample_config)
        
        validation_results = model.validate_compliance_rates()
        
        # 所有验证项都应该通过
        assert all(validation_results.values())
        
        # 检查特定验证项
        assert validation_results["base_rate_fit_screening"] is True
        assert validation_results["first_time_multiplier"] is True
        assert validation_results["time_decay_rate"] is True
        assert validation_results["positive_followup_rate"] is True


class TestScreeningEvent:
    """测试ScreeningEvent数据类"""
    
    def test_screening_event_creation(self):
        """测试筛查事件创建"""
        event = ScreeningEvent(
            date=datetime.now(),
            tool_type=ScreeningToolType.FIT,
            result=ScreeningResult.POSITIVE,
            individual_id="test_001"
        )
        
        assert event.tool_type == ScreeningToolType.FIT
        assert event.result == ScreeningResult.POSITIVE
        assert event.individual_id == "test_001"
        assert isinstance(event.additional_data, dict)


if __name__ == "__main__":
    pytest.main([__file__])
