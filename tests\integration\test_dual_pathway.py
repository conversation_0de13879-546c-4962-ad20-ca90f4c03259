"""
双通路集成测试

测试腺瘤-癌变通路和锯齿状腺瘤通路的集成功能，验证双通路协调工作。
"""

import pytest
import numpy as np
from typing import List, Dict

from src.core.enums import DiseaseState, PathwayType, AnatomicalLocation
from src.modules.disease.disease_model import DiseaseProgressionEngine
from src.modules.disease.pathway_selector import PathwaySelector
from src.modules.disease.serrated_progression import SerratedProgressionModel, SerratedAdenomaFeatureModel
from src.modules.disease.risk_factors import RiskFactorProfile, RiskFactor, RiskFactorType


class MockIndividual:
    """模拟个体类"""
    
    def __init__(self, individual_id: str = "test-001", 
                 disease_state: DiseaseState = DiseaseState.NORMAL,
                 pathway_type: PathwayType = PathwayType.ADENOMA_CARCINOMA,
                 gender: str = "male", age: int = 50):
        self.individual_id = individual_id
        self.disease_state = disease_state
        self.pathway_type = pathway_type
        self.gender = gender
        self.age = age


class TestDualPathwayIntegration:
    """测试双通路集成功能"""
    
    def test_pathway_assignment_distribution(self):
        """测试通路分配分布"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        # 创建大量个体进行统计测试
        individuals = [MockIndividual(f"individual-{i}") for i in range(1000)]
        
        # 分配通路
        pathway_assignments = []
        for individual in individuals:
            pathway = engine.assign_pathway(individual)
            pathway_assignments.append(pathway)
        
        # 统计分布
        serrated_count = sum(1 for p in pathway_assignments if p == PathwayType.SERRATED_ADENOMA)
        adenoma_count = sum(1 for p in pathway_assignments if p == PathwayType.ADENOMA_CARCINOMA)
        
        # 验证分布接近预期（15% vs 85%，允许5%偏差）
        serrated_percentage = serrated_count / 1000 * 100
        adenoma_percentage = adenoma_count / 1000 * 100
        
        assert 10 <= serrated_percentage <= 20, f"锯齿状通路比例: {serrated_percentage}%"
        assert 80 <= adenoma_percentage <= 90, f"腺瘤通路比例: {adenoma_percentage}%"
    
    def test_pathway_specific_progression(self):
        """测试通路特异性进展"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        # 测试锯齿状通路进展
        serrated_individual = MockIndividual(
            individual_id="serrated-001",
            disease_state=DiseaseState.NORMAL,
            pathway_type=PathwayType.SERRATED_ADENOMA
        )
        
        serrated_result = engine.progress_individual(serrated_individual, time_step=1.0)
        
        # 验证锯齿状通路只能进展到锯齿状状态
        if serrated_result.transition_occurred:
            assert serrated_result.new_state in [
                DiseaseState.NORMAL,
                DiseaseState.SMALL_SERRATED,
                DiseaseState.LARGE_SERRATED,
                DiseaseState.PRECLINICAL_CANCER
            ]
        
        # 测试腺瘤通路进展
        adenoma_individual = MockIndividual(
            individual_id="adenoma-001",
            disease_state=DiseaseState.NORMAL,
            pathway_type=PathwayType.ADENOMA_CARCINOMA
        )
        
        adenoma_result = engine.progress_individual(adenoma_individual, time_step=1.0)
        
        # 验证腺瘤通路只能进展到腺瘤状态
        if adenoma_result.transition_occurred:
            assert adenoma_result.new_state in [
                DiseaseState.NORMAL,
                DiseaseState.LOW_RISK_ADENOMA,
                DiseaseState.HIGH_RISK_ADENOMA,
                DiseaseState.PRECLINICAL_CANCER
            ]
    
    def test_pathway_state_isolation(self):
        """测试通路状态隔离"""
        engine = DiseaseProgressionEngine()
        
        # 锯齿状个体不应该能够进入腺瘤状态
        serrated_individual = MockIndividual(
            disease_state=DiseaseState.SMALL_SERRATED,
            pathway_type=PathwayType.ADENOMA_CARCINOMA  # 错误的通路分配
        )
        
        with pytest.raises(ValueError, match="状态.*与通路.*不兼容"):
            engine.progress_individual(serrated_individual)
        
        # 腺瘤个体不应该能够进入锯齿状状态
        adenoma_individual = MockIndividual(
            disease_state=DiseaseState.LOW_RISK_ADENOMA,
            pathway_type=PathwayType.SERRATED_ADENOMA  # 错误的通路分配
        )
        
        with pytest.raises(ValueError, match="状态.*与通路.*不兼容"):
            engine.progress_individual(adenoma_individual)
    
    def test_risk_factor_pathway_influence(self):
        """测试风险因素对通路选择的影响"""
        selector = PathwaySelector(random_seed=42)
        
        # 创建有吸烟风险的个体
        smoking_individual = MockIndividual(gender="female")
        smoking_profile = RiskFactorProfile("smoking-individual")
        smoking_factor = RiskFactor(
            factor_type=RiskFactorType.SMOKING,
            value=True,
            weight=1.0
        )
        smoking_profile.add_risk_factor(smoking_factor)
        
        # 创建无风险因素的个体
        normal_individual = MockIndividual(gender="male")
        normal_profile = RiskFactorProfile("normal-individual")
        
        # 比较通路偏好
        smoking_preferences = selector.get_pathway_preferences(smoking_individual, smoking_profile)
        normal_preferences = selector.get_pathway_preferences(normal_individual, normal_profile)
        
        # 吸烟个体应该有更高的锯齿状通路概率
        assert smoking_preferences["serrated_adenoma"] > normal_preferences["serrated_adenoma"]
    
    def test_serrated_anatomical_location_preference(self):
        """测试锯齿状腺瘤解剖位置偏好"""
        feature_model = SerratedAdenomaFeatureModel()
        
        # 生成大量锯齿状腺瘤特征
        characteristics_list = []
        for _ in range(1000):
            characteristics = feature_model.generate_characteristics(
                DiseaseState.SMALL_SERRATED,
                individual_age=60,
                random_seed=None
            )
            characteristics_list.append(characteristics)
        
        # 统计解剖位置分布
        location_counts = {
            AnatomicalLocation.PROXIMAL_COLON: 0,
            AnatomicalLocation.DISTAL_COLON: 0,
            AnatomicalLocation.RECTUM: 0
        }
        
        for char in characteristics_list:
            location_counts[char.anatomical_location] += 1
        
        # 验证近端结肠偏好（应该约65%）
        proximal_percentage = location_counts[AnatomicalLocation.PROXIMAL_COLON] / 1000 * 100
        distal_percentage = location_counts[AnatomicalLocation.DISTAL_COLON] / 1000 * 100
        rectum_percentage = location_counts[AnatomicalLocation.RECTUM] / 1000 * 100
        
        assert 55 <= proximal_percentage <= 75, f"近端结肠比例: {proximal_percentage}%"
        assert 15 <= distal_percentage <= 35, f"远端结肠比例: {distal_percentage}%"
        assert 5 <= rectum_percentage <= 15, f"直肠比例: {rectum_percentage}%"
    
    def test_serrated_screening_sensitivity(self):
        """测试锯齿状腺瘤筛查敏感性"""
        feature_model = SerratedAdenomaFeatureModel()
        
        # 创建不同大小的锯齿状腺瘤特征
        small_characteristics = feature_model.generate_characteristics(
            DiseaseState.SMALL_SERRATED,
            random_seed=42
        )
        
        large_characteristics = feature_model.generate_characteristics(
            DiseaseState.LARGE_SERRATED,
            random_seed=42
        )
        
        # 测试筛查敏感性
        small_colonoscopy_sens = feature_model.get_screening_sensitivity("colonoscopy", small_characteristics)
        large_colonoscopy_sens = feature_model.get_screening_sensitivity("colonoscopy", large_characteristics)
        
        small_fit_sens = feature_model.get_screening_sensitivity("fit", small_characteristics)
        large_fit_sens = feature_model.get_screening_sensitivity("fit", large_characteristics)
        
        # 验证敏感性特征
        assert 0 <= small_colonoscopy_sens <= 1
        assert 0 <= large_colonoscopy_sens <= 1
        assert 0 <= small_fit_sens <= 1
        assert 0 <= large_fit_sens <= 1
        
        # 结肠镜敏感性应该高于FIT
        assert small_colonoscopy_sens > small_fit_sens
        assert large_colonoscopy_sens > large_fit_sens
        
        # 大腺瘤应该比小腺瘤更容易检测
        assert large_colonoscopy_sens >= small_colonoscopy_sens
        assert large_fit_sens >= small_fit_sens
    
    def test_progression_statistics_tracking(self):
        """测试进展统计跟踪"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        # 创建不同通路的个体
        individuals = [
            MockIndividual(f"serrated-{i}", pathway_type=PathwayType.SERRATED_ADENOMA)
            for i in range(5)
        ] + [
            MockIndividual(f"adenoma-{i}", pathway_type=PathwayType.ADENOMA_CARCINOMA)
            for i in range(5)
        ]
        
        # 进行进展
        for individual in individuals:
            engine.progress_individual(individual, time_step=1.0)
        
        # 检查统计信息
        stats = engine.get_statistics()
        
        # 应该有一些进展记录
        assert stats.total_progressions >= 0
        assert len(stats.pathway_assignments) > 0
        
        # 检查通路分布
        distribution = stats.get_pathway_distribution()
        if stats.total_progressions > 0:
            assert "adenoma_carcinoma" in distribution or "serrated_adenoma" in distribution
    
    def test_pathway_integrity_validation(self):
        """测试通路完整性验证"""
        engine = DiseaseProgressionEngine()
        
        integrity = engine.validate_pathway_integrity()
        
        # 所有验证应该通过
        assert integrity["serrated_model_valid"] is True
        assert integrity["pathway_selector_valid"] is True
        assert integrity["pathway_constraints_valid"] is True
    
    def test_progression_time_consistency(self):
        """测试进展时间一致性"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 相同参数应该产生相同结果
        time1 = model.get_progression_time("normal", "small_serrated")
        
        model2 = SerratedProgressionModel(random_seed=42)
        time2 = model2.get_progression_time("normal", "small_serrated")
        
        assert abs(time1 - time2) < 1e-10
    
    def test_end_to_end_simulation(self):
        """测试端到端模拟"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        # 创建个体并分配通路
        individual = MockIndividual("end-to-end-test")
        pathway = engine.assign_pathway(individual)
        individual.pathway_type = pathway
        
        # 模拟多步进展
        progression_history = []
        current_state = individual.disease_state
        
        for step in range(10):
            individual.disease_state = current_state
            result = engine.progress_individual(individual, time_step=0.5)
            
            progression_history.append({
                "step": step,
                "from_state": current_state.value,
                "to_state": result.new_state.value,
                "transition_occurred": result.transition_occurred,
                "pathway": result.pathway_type.value
            })
            
            current_state = result.new_state
            
            # 如果到达终态，停止
            if current_state in [DiseaseState.DEATH_CANCER, DiseaseState.DEATH_OTHER]:
                break
        
        # 验证进展历史
        assert len(progression_history) > 0
        assert all(step["pathway"] == pathway.value for step in progression_history)
        
        # 验证状态转换的有效性
        for step in progression_history:
            if step["transition_occurred"]:
                # 状态应该发生了变化
                assert step["from_state"] != step["to_state"]
