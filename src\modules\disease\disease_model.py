"""
疾病进展引擎

实现双通路疾病进展模型的统一接口，支持腺瘤-癌变通路和锯齿状腺瘤通路。
"""

import logging
from typing import Dict, Optional, Any, List, Tuple
from dataclasses import dataclass
from enum import Enum

from src.core.enums import DiseaseState, PathwayType
from src.modules.disease.adenoma_progression import AdenomaProgressionModel
from src.modules.disease.serrated_progression import SerratedProgressionModel
from src.modules.disease.pathway_selector import PathwaySelector
from src.modules.disease.risk_factors import RiskFactorProfile

logger = logging.getLogger(__name__)


@dataclass
class ProgressionResult:
    """疾病进展结果"""
    new_state: DiseaseState
    progression_time: float
    pathway_type: PathwayType
    transition_occurred: bool
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class DiseaseProgressionStatistics:
    """疾病进展统计信息"""
    total_progressions: int = 0
    adenoma_pathway_progressions: int = 0
    serrated_pathway_progressions: int = 0
    pathway_assignments: Dict[str, int] = None
    state_transitions: Dict[str, int] = None
    
    def __post_init__(self):
        if self.pathway_assignments is None:
            self.pathway_assignments = {}
        if self.state_transitions is None:
            self.state_transitions = {}
    
    def add_progression(self, pathway_type: PathwayType, from_state: str, to_state: str):
        """添加进展记录"""
        self.total_progressions += 1
        
        if pathway_type == PathwayType.ADENOMA_CARCINOMA:
            self.adenoma_pathway_progressions += 1
        elif pathway_type == PathwayType.SERRATED_ADENOMA:
            self.serrated_pathway_progressions += 1
        
        # 记录通路分配
        pathway_key = pathway_type.value
        self.pathway_assignments[pathway_key] = self.pathway_assignments.get(pathway_key, 0) + 1
        
        # 记录状态转换
        transition_key = f"{from_state}_to_{to_state}"
        self.state_transitions[transition_key] = self.state_transitions.get(transition_key, 0) + 1
    
    def get_pathway_distribution(self) -> Dict[str, float]:
        """获取通路分布百分比"""
        if self.total_progressions == 0:
            return {}
        
        return {
            pathway: count / self.total_progressions * 100
            for pathway, count in self.pathway_assignments.items()
        }


class DiseaseProgressionEngine:
    """疾病进展引擎
    
    统一管理双通路疾病进展模型，提供个体疾病状态进展的核心功能。
    """
    
    def __init__(self, random_seed: Optional[int] = None):
        """
        初始化疾病进展引擎
        
        Args:
            random_seed: 随机种子，用于可重现的结果
        """
        self.random_seed = random_seed
        
        # 初始化双通路模型
        self.adenoma_model = AdenomaProgressionModel()
        self.serrated_model = SerratedProgressionModel(random_seed=random_seed)
        self.pathway_selector = PathwaySelector(random_seed=random_seed)
        
        # 统计信息
        self.statistics = DiseaseProgressionStatistics()
        
        # 通路特异性验证规则
        self.pathway_constraints = {
            PathwayType.ADENOMA_CARCINOMA: {
                "valid_states": {
                    DiseaseState.NORMAL,
                    DiseaseState.LOW_RISK_ADENOMA,
                    DiseaseState.HIGH_RISK_ADENOMA,
                    DiseaseState.PRECLINICAL_CANCER,
                    DiseaseState.CLINICAL_CANCER_STAGE_I,
                    DiseaseState.CLINICAL_CANCER_STAGE_II,
                    DiseaseState.CLINICAL_CANCER_STAGE_III,
                    DiseaseState.CLINICAL_CANCER_STAGE_IV,
                    DiseaseState.DEATH_CANCER,
                    DiseaseState.DEATH_OTHER
                }
            },
            PathwayType.SERRATED_ADENOMA: {
                "valid_states": {
                    DiseaseState.NORMAL,
                    DiseaseState.SMALL_SERRATED,
                    DiseaseState.LARGE_SERRATED,
                    DiseaseState.PRECLINICAL_CANCER,
                    DiseaseState.CLINICAL_CANCER_STAGE_I,
                    DiseaseState.CLINICAL_CANCER_STAGE_II,
                    DiseaseState.CLINICAL_CANCER_STAGE_III,
                    DiseaseState.CLINICAL_CANCER_STAGE_IV,
                    DiseaseState.DEATH_CANCER,
                    DiseaseState.DEATH_OTHER
                }
            }
        }
        
        logger.info("疾病进展引擎初始化完成")
    
    def assign_pathway(self, individual, risk_factors: Optional[RiskFactorProfile] = None) -> PathwayType:
        """
        为个体分配疾病进展通路
        
        Args:
            individual: 个体对象
            risk_factors: 风险因素配置文件
            
        Returns:
            分配的通路类型
        """
        pathway = self.pathway_selector.select_pathway(individual, risk_factors)
        logger.debug(f"个体 {getattr(individual, 'individual_id', 'unknown')} 分配到通路: {pathway.value}")
        return pathway
    
    def progress_individual(self, individual, time_step: float = 1.0, 
                          risk_factors: Optional[RiskFactorProfile] = None) -> ProgressionResult:
        """
        推进个体疾病状态
        
        Args:
            individual: 个体对象（需要有 disease_state 和 pathway_type 属性）
            time_step: 时间步长（年）
            risk_factors: 风险因素配置文件
            
        Returns:
            进展结果
        """
        current_state = individual.disease_state
        pathway_type = individual.pathway_type

        # 验证通路类型
        if pathway_type not in [PathwayType.ADENOMA_CARCINOMA, PathwayType.SERRATED_ADENOMA]:
            raise ValueError(f"未知的通路类型: {pathway_type}")

        # 验证通路和状态的兼容性
        if not self._validate_pathway_state_compatibility(pathway_type, current_state):
            raise ValueError(f"状态 {current_state.value} 与通路 {pathway_type.value} 不兼容")

        # 根据通路类型选择相应的模型
        if pathway_type == PathwayType.ADENOMA_CARCINOMA:
            new_state = self._progress_adenoma_pathway(individual, time_step, risk_factors)
        elif pathway_type == PathwayType.SERRATED_ADENOMA:
            new_state = self._progress_serrated_pathway(individual, time_step)
        else:
            raise ValueError(f"未知的通路类型: {pathway_type}")
        
        # 计算进展时间
        progression_time = self._calculate_progression_time(current_state, new_state, pathway_type)
        
        # 检查是否发生了状态转换
        transition_occurred = new_state != current_state
        
        # 更新统计信息
        if transition_occurred:
            self.statistics.add_progression(pathway_type, current_state.value, new_state.value)
        
        # 创建进展结果
        result = ProgressionResult(
            new_state=new_state,
            progression_time=progression_time,
            pathway_type=pathway_type,
            transition_occurred=transition_occurred,
            metadata={
                "time_step": time_step,
                "previous_state": current_state.value,
                "model_used": "adenoma" if pathway_type == PathwayType.ADENOMA_CARCINOMA else "serrated"
            }
        )
        
        logger.debug(f"个体进展: {current_state.value} -> {new_state.value} (通路: {pathway_type.value})")
        return result
    
    def _progress_adenoma_pathway(self, individual, time_step: float, 
                                risk_factors: Optional[RiskFactorProfile]) -> DiseaseState:
        """使用腺瘤模型进展个体状态"""
        # 注意：这里需要根据实际的AdenomaProgressionModel接口调整
        # 目前假设它有一个类似的get_next_state方法
        current_state = individual.disease_state
        
        # 简化的腺瘤通路进展逻辑
        if current_state == DiseaseState.NORMAL:
            # 这里应该调用腺瘤模型的具体方法
            # 暂时使用简化逻辑
            return DiseaseState.LOW_RISK_ADENOMA if time_step > 0.5 else current_state
        elif current_state == DiseaseState.LOW_RISK_ADENOMA:
            return DiseaseState.HIGH_RISK_ADENOMA if time_step > 0.3 else current_state
        elif current_state == DiseaseState.HIGH_RISK_ADENOMA:
            return DiseaseState.PRECLINICAL_CANCER if time_step > 0.2 else current_state
        
        return current_state
    
    def _progress_serrated_pathway(self, individual, time_step: float) -> DiseaseState:
        """使用锯齿状模型进展个体状态"""
        return self.serrated_model.get_next_state(individual.disease_state, time_step)
    
    def _calculate_progression_time(self, from_state: DiseaseState, to_state: DiseaseState, 
                                  pathway_type: PathwayType) -> float:
        """计算进展时间"""
        if from_state == to_state:
            return 0.0
        
        try:
            if pathway_type == PathwayType.SERRATED_ADENOMA:
                return self.serrated_model.get_progression_time(from_state.value, to_state.value)
            else:
                # 腺瘤通路的时间计算（简化版本）
                return 1.0  # 默认1年
        except Exception:
            return 1.0  # 默认值
    
    def _validate_pathway_state_compatibility(self, pathway_type: PathwayType, 
                                            state: DiseaseState) -> bool:
        """验证通路和状态的兼容性"""
        if pathway_type not in self.pathway_constraints:
            return False
        
        valid_states = self.pathway_constraints[pathway_type]["valid_states"]
        return state in valid_states
    
    def get_statistics(self) -> DiseaseProgressionStatistics:
        """获取进展统计信息"""
        return self.statistics
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.statistics = DiseaseProgressionStatistics()
    
    def validate_pathway_integrity(self) -> Dict[str, bool]:
        """验证通路完整性"""
        results = {}
        
        # 验证锯齿状模型参数
        results["serrated_model_valid"] = self.serrated_model.validate_parameters()
        
        # 验证通路选择器分布
        results["pathway_selector_valid"] = self.pathway_selector.validate_distribution()
        
        # 验证通路约束
        results["pathway_constraints_valid"] = len(self.pathway_constraints) == 2
        
        return results
    
    def get_pathway_preferences(self, individual, risk_factors: Optional[RiskFactorProfile] = None) -> Dict[str, float]:
        """获取个体的通路偏好"""
        return self.pathway_selector.get_pathway_preferences(individual, risk_factors)
