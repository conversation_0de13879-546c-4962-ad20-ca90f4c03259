"""
筛查工具枚举定义

定义筛查工具类型、结果类型和相关枚举。
"""

from enum import Enum
from typing import Dict, List


class ScreeningToolType(Enum):
    """筛查工具类型枚举"""
    
    FIT = "fecal_immunochemical_test"           # 粪便免疫化学检测
    COLONOSCOPY = "colonoscopy"                 # 结肠镜检查
    SIGMOIDOSCOPY = "flexible_sigmoidoscopy"    # 乙状结肠镜检查
    RISK_QUESTIONNAIRE = "risk_assessment_questionnaire"  # 风险评估问卷
    CTCOLONOGRAPHY = "ct_colonography"          # CT结肠成像
    STOOL_DNA = "stool_dna_test"               # 粪便DNA检测
    CAPSULE_ENDOSCOPY = "capsule_endoscopy"    # 胶囊内镜
    OTHER = "other"                            # 其他筛查工具

    @property
    def display_name(self) -> str:
        """返回工具的中文显示名称"""
        display_names = {
            self.FIT: "粪便免疫化学检测 (FIT)",
            self.COLONOSCOPY: "结肠镜检查",
            self.SIGMOIDOSCOPY: "乙状结肠镜检查", 
            self.RISK_QUESTIONNAIRE: "风险评估问卷",
            self.CTCOLONOGRAPHY: "CT结肠成像",
            self.STOOL_DNA: "粪便DNA检测",
            self.CAPSULE_ENDOSCOPY: "胶囊内镜",
            self.OTHER: "其他筛查工具"
        }
        return display_names.get(self, self.value)

    @property
    def is_invasive(self) -> bool:
        """返回工具是否为侵入性检查"""
        invasive_tools = {
            self.COLONOSCOPY,
            self.SIGMOIDOSCOPY,
            self.CAPSULE_ENDOSCOPY
        }
        return self in invasive_tools

    @property
    def requires_preparation(self) -> bool:
        """返回工具是否需要特殊准备"""
        preparation_required = {
            self.COLONOSCOPY,
            self.SIGMOIDOSCOPY,
            self.CTCOLONOGRAPHY
        }
        return self in preparation_required


class ScreeningResult(Enum):
    """筛查结果类型枚举"""
    
    NEGATIVE = "negative"           # 阴性结果
    POSITIVE = "positive"           # 阳性结果
    INADEQUATE = "inadequate"       # 样本不足/检查不充分
    FAILED = "failed"              # 检查失败
    NOT_PERFORMED = "not_performed" # 未执行检查

    @property
    def display_name(self) -> str:
        """返回结果的中文显示名称"""
        display_names = {
            self.NEGATIVE: "阴性",
            self.POSITIVE: "阳性",
            self.INADEQUATE: "样本不足",
            self.FAILED: "检查失败",
            self.NOT_PERFORMED: "未执行"
        }
        return display_names.get(self, self.value)

    @property
    def requires_followup(self) -> bool:
        """返回结果是否需要后续检查"""
        followup_required = {
            self.POSITIVE,
            self.INADEQUATE
        }
        return self in followup_required


class InvasivenessLevel(Enum):
    """侵入性程度枚举"""
    
    NON_INVASIVE = "non_invasive"       # 非侵入性
    MINIMALLY_INVASIVE = "minimally_invasive"  # 微创
    INVASIVE = "invasive"               # 侵入性

    @property
    def display_name(self) -> str:
        """返回侵入性程度的中文显示名称"""
        display_names = {
            self.NON_INVASIVE: "非侵入性",
            self.MINIMALLY_INVASIVE: "微创",
            self.INVASIVE: "侵入性"
        }
        return display_names.get(self, self.value)


class OperatorSkillLevel(Enum):
    """操作者技能要求枚举"""
    
    LOW = "low"         # 低技能要求
    MEDIUM = "medium"   # 中等技能要求
    HIGH = "high"       # 高技能要求

    @property
    def display_name(self) -> str:
        """返回技能要求的中文显示名称"""
        display_names = {
            self.LOW: "低技能要求",
            self.MEDIUM: "中等技能要求", 
            self.HIGH: "高技能要求"
        }
        return display_names.get(self, self.value)


# 筛查工具特性映射
SCREENING_TOOL_CHARACTERISTICS: Dict[ScreeningToolType, Dict] = {
    ScreeningToolType.FIT: {
        "invasiveness": InvasivenessLevel.NON_INVASIVE,
        "operator_skill": OperatorSkillLevel.LOW,
        "turnaround_time_days": 3,
        "can_detect_proximal": True,
        "can_detect_distal": True,
        "can_detect_rectal": True
    },
    ScreeningToolType.COLONOSCOPY: {
        "invasiveness": InvasivenessLevel.INVASIVE,
        "operator_skill": OperatorSkillLevel.HIGH,
        "turnaround_time_days": 0,
        "can_detect_proximal": True,
        "can_detect_distal": True,
        "can_detect_rectal": True
    },
    ScreeningToolType.SIGMOIDOSCOPY: {
        "invasiveness": InvasivenessLevel.INVASIVE,
        "operator_skill": OperatorSkillLevel.HIGH,
        "turnaround_time_days": 0,
        "can_detect_proximal": False,
        "can_detect_distal": True,
        "can_detect_rectal": True
    },
    ScreeningToolType.RISK_QUESTIONNAIRE: {
        "invasiveness": InvasivenessLevel.NON_INVASIVE,
        "operator_skill": OperatorSkillLevel.LOW,
        "turnaround_time_days": 0,
        "can_detect_proximal": False,
        "can_detect_distal": False,
        "can_detect_rectal": False
    },
    ScreeningToolType.CTCOLONOGRAPHY: {
        "invasiveness": InvasivenessLevel.MINIMALLY_INVASIVE,
        "operator_skill": OperatorSkillLevel.MEDIUM,
        "turnaround_time_days": 1,
        "can_detect_proximal": True,
        "can_detect_distal": True,
        "can_detect_rectal": True
    },
    ScreeningToolType.STOOL_DNA: {
        "invasiveness": InvasivenessLevel.NON_INVASIVE,
        "operator_skill": OperatorSkillLevel.LOW,
        "turnaround_time_days": 7,
        "can_detect_proximal": True,
        "can_detect_distal": True,
        "can_detect_rectal": True
    },
    ScreeningToolType.CAPSULE_ENDOSCOPY: {
        "invasiveness": InvasivenessLevel.MINIMALLY_INVASIVE,
        "operator_skill": OperatorSkillLevel.MEDIUM,
        "turnaround_time_days": 1,
        "can_detect_proximal": True,
        "can_detect_distal": True,
        "can_detect_rectal": False
    }
}


def get_tool_characteristics(tool_type: ScreeningToolType) -> Dict:
    """获取筛查工具特性"""
    return SCREENING_TOOL_CHARACTERISTICS.get(tool_type, {})


def get_available_tools() -> List[ScreeningToolType]:
    """获取所有可用的筛查工具类型"""
    return list(ScreeningToolType)


def get_invasive_tools() -> List[ScreeningToolType]:
    """获取所有侵入性筛查工具"""
    return [tool for tool in ScreeningToolType if tool.is_invasive]


def get_non_invasive_tools() -> List[ScreeningToolType]:
    """获取所有非侵入性筛查工具"""
    return [tool for tool in ScreeningToolType if not tool.is_invasive]
