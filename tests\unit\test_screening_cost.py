"""
筛查工具成本模型测试

测试成本计算、地区调整和成本组件管理功能。
"""

import pytest
from unittest.mock import patch

from src.core.enums import Gender
from src.core.individual import Individual
from src.modules.screening import (
    ScreeningCostModel, CostItem, CostComponent, CostCategory,
    RegionalCostProfile, ScreeningToolType
)


class TestCostItem:
    """测试成本项目"""
    
    def test_cost_item_creation(self):
        """测试成本项目创建"""
        cost_item = CostItem(
            component=CostComponent.PROCEDURE_FEE,
            category=CostCategory.DIRECT_MEDICAL,
            base_cost=100.0,
            description="检查费用"
        )
        
        assert cost_item.component == CostComponent.PROCEDURE_FEE
        assert cost_item.category == CostCategory.DIRECT_MEDICAL
        assert cost_item.base_cost == 100.0
        assert cost_item.occurrence_probability == 1.0
    
    def test_cost_item_validation(self):
        """测试成本项目验证"""
        # 负成本应该抛出错误
        with pytest.raises(ValueError, match="基础成本不能为负数"):
            CostItem(
                component=CostComponent.PROCEDURE_FEE,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=-10.0
            )
        
        # 无效的发生概率
        with pytest.raises(ValueError, match="发生概率必须在0-1之间"):
            CostItem(
                component=CostComponent.PROCEDURE_FEE,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=100.0,
                occurrence_probability=1.5
            )
        
        # 最小成本大于基础成本
        with pytest.raises(ValueError, match="最小成本不能大于基础成本"):
            CostItem(
                component=CostComponent.PROCEDURE_FEE,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=100.0,
                min_cost=150.0
            )


class TestRegionalCostProfile:
    """测试地区成本档案"""
    
    def test_regional_profile_creation(self):
        """测试地区档案创建"""
        profile = RegionalCostProfile(
            region_name="北京",
            cost_level_index=1.5,
            medical_cost_multiplier=1.6,
            labor_cost_multiplier=1.8
        )
        
        assert profile.region_name == "北京"
        assert profile.cost_level_index == 1.5
        assert profile.medical_cost_multiplier == 1.6
        assert profile.labor_cost_multiplier == 1.8


class TestScreeningCostModel:
    """测试筛查成本模型"""
    
    @pytest.fixture
    def sample_individual(self):
        """创建样本个体"""
        individual = Individual(
            birth_year=1973,
            gender=Gender.MALE
        )
        return individual
    
    @pytest.fixture
    def fit_cost_items(self):
        """创建FIT成本项目"""
        return [
            CostItem(
                component=CostComponent.PROCEDURE_FEE,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=20.0,
                description="FIT检查费用"
            ),
            CostItem(
                component=CostComponent.MATERIAL_COST,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=5.0,
                description="采样盒费用"
            ),
            CostItem(
                component=CostComponent.TIME_COST,
                category=CostCategory.INDIRECT,
                base_cost=30.0,
                description="时间成本"
            ),
            CostItem(
                component=CostComponent.TRANSPORTATION,
                category=CostCategory.DIRECT_NON_MEDICAL,
                base_cost=10.0,
                description="交通费用"
            )
        ]
    
    @pytest.fixture
    def colonoscopy_cost_items(self):
        """创建结肠镜成本项目"""
        return [
            CostItem(
                component=CostComponent.PROCEDURE_FEE,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=600.0,
                description="结肠镜检查费用"
            ),
            CostItem(
                component=CostComponent.EQUIPMENT_COST,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=200.0,
                description="设备使用费"
            ),
            CostItem(
                component=CostComponent.STAFF_COST,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=300.0,
                description="医护人员费用"
            ),
            CostItem(
                component=CostComponent.ANESTHESIA_COST,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=200.0,
                description="麻醉费用"
            ),
            CostItem(
                component=CostComponent.TIME_COST,
                category=CostCategory.INDIRECT,
                base_cost=400.0,
                description="时间成本"
            )
        ]
    
    @pytest.fixture
    def beijing_profile(self):
        """创建北京地区成本档案"""
        return RegionalCostProfile(
            region_name="北京",
            cost_level_index=1.5,
            medical_cost_multiplier=1.6,
            labor_cost_multiplier=1.8,
            facility_cost_multiplier=1.4,
            transportation_cost_multiplier=1.2
        )
    
    def test_cost_model_initialization(self, fit_cost_items):
        """测试成本模型初始化"""
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=fit_cost_items
        )
        
        assert model.tool_type == ScreeningToolType.FIT
        assert len(model.cost_items) == 4
        assert CostComponent.PROCEDURE_FEE in model.cost_items
        assert model.reference_year == 2023
    
    def test_basic_cost_calculation(self, fit_cost_items, sample_individual):
        """测试基础成本计算"""
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=fit_cost_items
        )
        
        cost_breakdown = model.calculate_total_cost(sample_individual)
        
        # 检查总成本
        expected_total = 20.0 + 5.0 + 30.0 + 10.0  # 65.0
        assert cost_breakdown["total_cost"] == expected_total
        
        # 检查各组件成本
        assert cost_breakdown["procedure_fee"] == 20.0
        assert cost_breakdown["material_cost"] == 5.0
        assert cost_breakdown["time_cost"] == 30.0
        assert cost_breakdown["transportation"] == 10.0
    
    def test_regional_cost_adjustment(self, fit_cost_items, sample_individual, beijing_profile):
        """测试地区成本调整"""
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=fit_cost_items,
            regional_profile=beijing_profile
        )
        
        cost_breakdown = model.calculate_total_cost(sample_individual)
        
        # 医疗成本应该按医疗成本倍数调整
        expected_procedure_fee = 20.0 * beijing_profile.medical_cost_multiplier
        assert cost_breakdown["procedure_fee"] == expected_procedure_fee
        
        # 交通成本应该按交通成本倍数调整
        expected_transportation = 10.0 * beijing_profile.transportation_cost_multiplier
        assert cost_breakdown["transportation"] == expected_transportation
    
    def test_age_adjustment(self, fit_cost_items):
        """测试年龄调整"""
        # 创建带年龄调整的成本项目
        cost_items_with_age = []
        for item in fit_cost_items:
            if item.component == CostComponent.TIME_COST:
                item.age_adjustment = {
                    "young": 1.0,
                    "middle": 1.2,
                    "elderly": 1.5
                }
            cost_items_with_age.append(item)
        
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=cost_items_with_age
        )
        
        # 年轻个体
        young_individual = Individual(birth_year=1990, gender=Gender.MALE)
        young_cost = model.calculate_total_cost(young_individual)
        
        # 老年个体
        elderly_individual = Individual(birth_year=1940, gender=Gender.MALE)
        elderly_cost = model.calculate_total_cost(elderly_individual)
        
        # 老年个体的时间成本应该更高
        assert elderly_cost["time_cost"] > young_cost["time_cost"]
    
    def test_gender_adjustment(self, colonoscopy_cost_items):
        """测试性别调整"""
        # 为结肠镜检查添加性别调整
        for item in colonoscopy_cost_items:
            if item.component == CostComponent.PROCEDURE_FEE:
                item.gender_adjustment = {
                    "male": 1.0,
                    "female": 1.1  # 女性检查稍复杂
                }
        
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.COLONOSCOPY,
            cost_items=colonoscopy_cost_items
        )
        
        # 男性个体
        male_individual = Individual(birth_year=1970, gender=Gender.MALE)
        male_cost = model.calculate_total_cost(male_individual)
        
        # 女性个体
        female_individual = Individual(birth_year=1970, gender=Gender.FEMALE)
        female_cost = model.calculate_total_cost(female_individual)
        
        # 女性的检查费用应该稍高
        assert female_cost["procedure_fee"] > male_cost["procedure_fee"]
    
    def test_time_adjustment(self, fit_cost_items, sample_individual):
        """测试时间调整（通胀）"""
        # 添加时间调整
        for item in fit_cost_items:
            item.time_adjustment = {
                "annual_inflation": 0.05  # 5%年通胀率
            }
        
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=fit_cost_items,
            reference_year=2020
        )
        
        # 2025年的成本（5年后）
        context_2025 = {"year": 2025}
        cost_2025 = model.calculate_total_cost(sample_individual, context_2025)
        
        # 2020年的成本
        context_2020 = {"year": 2020}
        cost_2020 = model.calculate_total_cost(sample_individual, context_2020)
        
        # 2025年的成本应该更高
        assert cost_2025["total_cost"] > cost_2020["total_cost"]
    
    def test_procedure_complexity_adjustment(self, colonoscopy_cost_items, sample_individual):
        """测试检查复杂度调整"""
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.COLONOSCOPY,
            cost_items=colonoscopy_cost_items
        )
        
        # 简单检查
        simple_context = {"procedure_complexity": "simple"}
        simple_cost = model.calculate_total_cost(sample_individual, simple_context)
        
        # 复杂检查
        complex_context = {"procedure_complexity": "complex"}
        complex_cost = model.calculate_total_cost(sample_individual, complex_context)
        
        # 复杂检查的成本应该更高
        assert complex_cost["total_cost"] > simple_cost["total_cost"]
    
    def test_procedure_duration_adjustment(self, colonoscopy_cost_items, sample_individual):
        """测试检查时长调整"""
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.COLONOSCOPY,
            cost_items=colonoscopy_cost_items
        )
        
        # 短时间检查
        short_context = {"procedure_duration_minutes": 20}
        short_cost = model.calculate_total_cost(sample_individual, short_context)
        
        # 长时间检查
        long_context = {"procedure_duration_minutes": 60}
        long_cost = model.calculate_total_cost(sample_individual, long_context)
        
        # 长时间检查的人员成本应该更高
        assert long_cost["staff_cost"] > short_cost["staff_cost"]
    
    @patch('random.random')
    def test_occurrence_probability(self, mock_random, fit_cost_items, sample_individual):
        """测试成本发生概率"""
        # 设置一个成本项目的发生概率为0.5
        for item in fit_cost_items:
            if item.component == CostComponent.TRANSPORTATION:
                item.occurrence_probability = 0.5
        
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=fit_cost_items
        )
        
        # 模拟不发生（random > 0.5）
        mock_random.return_value = 0.8
        cost_not_occurred = model.calculate_total_cost(sample_individual)
        assert cost_not_occurred["transportation"] == 0.0
        
        # 模拟发生（random <= 0.5）
        mock_random.return_value = 0.3
        cost_occurred = model.calculate_total_cost(sample_individual)
        assert cost_occurred["transportation"] == 10.0
    
    def test_category_totals(self, fit_cost_items, sample_individual):
        """测试成本类别汇总"""
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=fit_cost_items
        )
        
        cost_breakdown = model.calculate_total_cost(sample_individual)
        
        # 检查类别总计
        assert "direct_medical_total" in cost_breakdown
        assert "indirect_total" in cost_breakdown
        assert "direct_non_medical_total" in cost_breakdown
        
        # 验证直接医疗成本总计
        expected_direct_medical = 20.0 + 5.0  # procedure_fee + material_cost
        assert cost_breakdown["direct_medical_total"] == expected_direct_medical
        
        # 验证间接成本总计
        expected_indirect = 30.0  # time_cost
        assert cost_breakdown["indirect_total"] == expected_indirect
    
    def test_cost_summary(self, fit_cost_items):
        """测试成本模型摘要"""
        model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=fit_cost_items
        )
        
        summary = model.get_cost_summary()
        
        assert summary["tool_type"] == "fecal_immunochemical_test"
        assert summary["total_cost_items"] == 4
        assert summary["regional_profile"] == "默认地区"
        assert summary["reference_year"] == 2023
        assert len(summary["cost_components"]) == 4
        assert summary["base_total_cost"] == 65.0
