"""
测试通路选择和分配机制

测试PathwaySelector类的功能，包括通路选择概率、风险因素调整和统计跟踪。
"""

import pytest
import random
from unittest.mock import Mock

from src.core.enums import PathwayType
from src.modules.disease.pathway_selector import PathwaySelector, PathwaySelectionStatistics, MockIndividual
from src.modules.disease.risk_factors import RiskFactorType, RiskFactor, RiskFactorProfile


class TestPathwaySelectionStatistics:
    """测试通路选择统计信息"""
    
    def test_initial_statistics(self):
        """测试初始统计信息"""
        stats = PathwaySelectionStatistics()
        
        assert stats.total_selections == 0
        assert stats.adenoma_carcinoma_count == 0
        assert stats.serrated_adenoma_count == 0
        assert stats.adenoma_carcinoma_percentage == 0.0
        assert stats.serrated_adenoma_percentage == 0.0
    
    def test_percentage_calculation(self):
        """测试百分比计算"""
        stats = PathwaySelectionStatistics()
        stats.total_selections = 100
        stats.adenoma_carcinoma_count = 85
        stats.serrated_adenoma_count = 15
        
        assert stats.adenoma_carcinoma_percentage == 85.0
        assert stats.serrated_adenoma_percentage == 15.0
    
    def test_reset(self):
        """测试重置功能"""
        stats = PathwaySelectionStatistics()
        stats.total_selections = 100
        stats.adenoma_carcinoma_count = 85
        stats.serrated_adenoma_count = 15
        
        stats.reset()
        
        assert stats.total_selections == 0
        assert stats.adenoma_carcinoma_count == 0
        assert stats.serrated_adenoma_count == 0


class TestPathwaySelector:
    """测试通路选择器"""
    
    def test_initialization(self):
        """测试初始化"""
        selector = PathwaySelector(base_serrated_probability=0.2, random_seed=42)
        
        assert selector.base_serrated_probability == 0.2
        assert isinstance(selector.statistics, PathwaySelectionStatistics)
        assert selector.statistics.total_selections == 0
    
    def test_basic_pathway_selection(self):
        """测试基本通路选择"""
        selector = PathwaySelector(random_seed=42)
        individual = MockIndividual(gender="male")
        
        pathway = selector.select_pathway(individual)
        
        assert pathway in [PathwayType.ADENOMA_CARCINOMA, PathwayType.SERRATED_ADENOMA]
        assert selector.statistics.total_selections == 1
    
    def test_gender_modification(self):
        """测试性别对通路选择的影响"""
        selector = PathwaySelector(base_serrated_probability=0.15, random_seed=42)
        
        # 测试女性个体
        female_individual = MockIndividual(gender="female")
        female_prob = selector._calculate_adjusted_probability(female_individual, None)
        
        # 测试男性个体
        male_individual = MockIndividual(gender="male")
        male_prob = selector._calculate_adjusted_probability(male_individual, None)
        
        # 女性应该有更高的锯齿状腺瘤概率
        assert female_prob > male_prob
        assert female_prob == 0.15 * 1.1  # 女性修正系数1.1
    
    def test_risk_factor_modification(self):
        """测试风险因素对通路选择的影响"""
        selector = PathwaySelector(base_serrated_probability=0.15, random_seed=42)
        individual = MockIndividual(gender="male")
        
        # 创建包含吸烟风险因素的配置文件
        risk_profile = RiskFactorProfile("test-individual")
        smoking_factor = RiskFactor(
            factor_type=RiskFactorType.SMOKING,
            value=True,
            weight=1.0
        )
        risk_profile.add_risk_factor(smoking_factor)
        
        # 计算调整后概率
        adjusted_prob = selector._calculate_adjusted_probability(individual, risk_profile)
        
        # 吸烟应该增加锯齿状腺瘤概率
        expected_prob = 0.15 * 1.3  # 吸烟修正系数1.3
        assert adjusted_prob == expected_prob
    
    def test_multiple_risk_factors(self):
        """测试多个风险因素的累积效应"""
        selector = PathwaySelector(base_serrated_probability=0.15, random_seed=42)
        individual = MockIndividual(gender="female")
        
        # 创建多个风险因素
        risk_profile = RiskFactorProfile("test-individual")
        smoking_factor = RiskFactor(
            factor_type=RiskFactorType.SMOKING,
            value=True,
            weight=1.0
        )
        family_history_factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True,
            weight=1.0
        )
        risk_profile.add_risk_factor(smoking_factor)
        risk_profile.add_risk_factor(family_history_factor)
        
        adjusted_prob = selector._calculate_adjusted_probability(individual, risk_profile)
        
        # 预期概率：基础(0.15) * 性别(1.1) * 吸烟(1.3) * 家族史(0.8)
        expected_prob = 0.15 * 1.1 * 1.3 * 0.8
        assert abs(adjusted_prob - expected_prob) < 1e-10
    
    def test_batch_selection(self):
        """测试批量通路选择"""
        selector = PathwaySelector(random_seed=42)
        individuals = [MockIndividual() for _ in range(10)]
        
        pathways = selector.batch_select_pathways(individuals)
        
        assert len(pathways) == 10
        assert all(p in [PathwayType.ADENOMA_CARCINOMA, PathwayType.SERRATED_ADENOMA] for p in pathways)
        assert selector.statistics.total_selections == 10
    
    def test_batch_selection_with_risk_factors(self):
        """测试带风险因素的批量选择"""
        selector = PathwaySelector(random_seed=42)
        individuals = [MockIndividual() for _ in range(5)]
        
        # 创建风险因素列表
        risk_factors_list = []
        for i in range(5):
            risk_profile = RiskFactorProfile(f"test-individual-{i}")
            if i % 2 == 0:  # 偶数索引有吸烟风险
                smoking_factor = RiskFactor(
                    factor_type=RiskFactorType.SMOKING,
                    value=True,
                    weight=1.0
                )
                risk_profile.add_risk_factor(smoking_factor)
            risk_factors_list.append(risk_profile)
        
        pathways = selector.batch_select_pathways(individuals, risk_factors_list)
        
        assert len(pathways) == 5
        assert selector.statistics.total_selections == 5
    
    def test_batch_selection_length_mismatch(self):
        """测试批量选择时长度不匹配的错误"""
        selector = PathwaySelector()
        individuals = [MockIndividual() for _ in range(5)]
        risk_factors_list = [RiskFactorProfile(f"test-{i}") for i in range(3)]
        
        with pytest.raises(ValueError, match="风险因素列表长度必须与个体列表长度相同"):
            selector.batch_select_pathways(individuals, risk_factors_list)
    
    def test_distribution_validation(self):
        """测试分布验证"""
        selector = PathwaySelector(base_serrated_probability=0.15, random_seed=42)
        
        # 模拟大量选择以接近预期分布
        individuals = [MockIndividual() for _ in range(1000)]
        selector.batch_select_pathways(individuals)
        
        # 验证分布（允许5%偏差）
        is_valid = selector.validate_distribution(tolerance=0.05)
        assert is_valid
    
    def test_pathway_preferences(self):
        """测试通路偏好计算"""
        selector = PathwaySelector(base_serrated_probability=0.15)
        individual = MockIndividual(gender="female")
        
        preferences = selector.get_pathway_preferences(individual)
        
        assert "serrated_adenoma" in preferences
        assert "adenoma_carcinoma" in preferences
        assert abs(preferences["serrated_adenoma"] + preferences["adenoma_carcinoma"] - 1.0) < 1e-10
        assert preferences["serrated_adenoma"] == 0.15 * 1.1  # 女性修正
    
    def test_population_distribution_simulation(self):
        """测试人群分布模拟"""
        selector = PathwaySelector(base_serrated_probability=0.15, random_seed=42)
        
        distribution = selector.simulate_population_distribution(population_size=100)
        
        assert distribution["total_population"] == 100
        assert distribution["serrated_adenoma_count"] + distribution["adenoma_carcinoma_count"] == 100
        assert abs(distribution["serrated_adenoma_percentage"] + distribution["adenoma_carcinoma_percentage"] - 100.0) < 1e-10
    
    def test_statistics_reset(self):
        """测试统计信息重置"""
        selector = PathwaySelector(random_seed=42)
        individual = MockIndividual()
        
        # 进行一些选择
        for _ in range(5):
            selector.select_pathway(individual)
        
        assert selector.statistics.total_selections == 5
        
        # 重置统计
        selector.reset_statistics()
        
        assert selector.statistics.total_selections == 0
        assert selector.statistics.adenoma_carcinoma_count == 0
        assert selector.statistics.serrated_adenoma_count == 0
    
    def test_probability_bounds(self):
        """测试概率边界"""
        selector = PathwaySelector(base_serrated_probability=0.15)
        
        # 创建极端风险因素组合
        risk_profile = RiskFactorProfile("test-individual")
        smoking_factor = RiskFactor(
            factor_type=RiskFactorType.SMOKING,
            value=True,
            weight=1.0
        )
        alcohol_factor = RiskFactor(
            factor_type=RiskFactorType.ALCOHOL_CONSUMPTION,
            value=20.0,
            weight=1.0
        )
        risk_profile.add_risk_factor(smoking_factor)
        risk_profile.add_risk_factor(alcohol_factor)
        individual = MockIndividual(gender="female")
        
        adjusted_prob = selector._calculate_adjusted_probability(individual, risk_profile)
        
        # 概率应该在0-1范围内
        assert 0.0 <= adjusted_prob <= 1.0
