"""
参数导出演示

演示参数管理器的多格式导出功能，包括YAML、JSON、Excel和CSV格式。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.config.parameter_manager import ParameterManager, ParameterConstraint


def main():
    """演示参数导出功能"""
    print("=== 参数管理器多格式导出演示 ===\n")
    
    # 创建参数管理器
    manager = ParameterManager()
    
    # 修改一些参数值
    print("1. 修改参数值...")
    manager.set_parameter("villous_risk_multiplier", 1.8)
    manager.set_parameter("high_grade_dysplasia_multiplier", 2.2)
    manager.set_parameter("adenoma_initiation_inflection_point", 52.0)
    
    # 添加自定义参数
    print("2. 添加自定义参数...")
    constraint = ParameterConstraint(
        min_value=0.0,
        max_value=1.0,
        data_type="float",
        description="自定义概率参数"
    )
    
    manager.add_parameter(
        name="custom_probability",
        value=0.75,
        trainable=True,
        constraint=constraint,
        category="custom",
        description="演示用的自定义概率参数",
        unit="probability"
    )
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    print("3. 导出到不同格式...")
    
    # 导出为YAML格式
    yaml_file = output_dir / "parameters.yaml"
    manager.save_to_file(str(yaml_file), "yaml")
    print(f"   ✓ YAML格式已导出到: {yaml_file}")
    
    # 导出为JSON格式
    json_file = output_dir / "parameters.json"
    manager.save_to_file(str(json_file), "json")
    print(f"   ✓ JSON格式已导出到: {json_file}")
    
    # 导出为Excel格式
    excel_file = output_dir / "parameters.xlsx"
    manager.save_to_file(str(excel_file), "excel")
    print(f"   ✓ Excel格式已导出到: {excel_file}")
    
    # 导出为CSV格式
    csv_file = output_dir / "parameters.csv"
    manager.save_to_file(str(csv_file), "csv")
    print(f"   ✓ CSV格式已导出到: {csv_file}")
    
    print("\n4. 验证导出内容...")
    
    # 显示参数摘要
    summary = manager.get_parameter_summary()
    print(f"   总参数数: {summary['total_parameters']}")
    print(f"   可训练参数数: {summary['trainable_parameters']}")
    print(f"   参数类别数: {len(summary['categories'])}")
    
    # 显示各类别的参数数量
    print("\n   各类别参数数量:")
    for category, info in summary['categories'].items():
        print(f"     {category}: {info['count']} (可训练: {info['trainable']})")
    
    print("\n5. 测试从文件加载...")
    
    # 创建新的管理器并从Excel文件加载
    new_manager = ParameterManager()
    original_param_count = len(new_manager.parameters)
    
    new_manager.load_from_file(str(excel_file))
    print(f"   从Excel文件加载完成")
    print(f"   加载前参数数: {original_param_count}")
    print(f"   加载后参数数: {len(new_manager.parameters)}")
    
    # 验证自定义参数是否正确加载
    if "custom_probability" in new_manager.parameters:
        custom_param = new_manager.get_parameter("custom_probability")
        print(f"   自定义参数值: {custom_param}")
        print(f"   ✓ 参数加载验证成功")
    else:
        print("   ✗ 自定义参数未找到")
    
    print("\n6. 演示神经网络校准目标...")
    
    # 导入校准接口
    from src.modules.calibration.nn_interface import NeuralNetworkCalibrator
    
    calibrator = NeuralNetworkCalibrator(manager)
    
    print(f"   校准目标总数: {len(calibrator.calibration_targets)}")
    
    # 显示一些校准目标示例
    print("\n   校准目标示例:")
    target_examples = list(calibrator.calibration_targets.items())[:5]
    for name, target in target_examples:
        print(f"     {name}: 目标值={target.target_value}, 权重={target.weight}")
    
    print("\n=== 演示完成 ===")
    print(f"\n所有导出文件已保存到: {output_dir.absolute()}")
    print("您可以使用Excel或文本编辑器查看这些文件。")


if __name__ == "__main__":
    main()
