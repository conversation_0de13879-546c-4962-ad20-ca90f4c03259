"""
解剖位置分配系统

实现基于概率的解剖位置分配，包括位置特异性疾病特征和进展参数调整。
支持近端结肠、远端结肠和直肠的不同特征建模。
"""

import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import logging
from enum import Enum
from collections import defaultdict

from src.core.enums import AnatomicalLocation, Gender, DiseaseState

logger = logging.getLogger(__name__)


@dataclass
class LocationSpecificFeatures:
    """位置特异性特征"""
    screening_sensitivity_modifier: float    # 筛查敏感性调整因子
    progression_rate_modifier: float        # 进展率调整因子
    cancer_stage_distribution: List[float]  # 癌症分期分布（I-IV期）
    detection_difficulty: float             # 检测难度（0-1，越高越难检测）
    symptom_onset_modifier: float           # 症状出现调整因子


@dataclass
class LocationStatistics:
    """位置统计信息"""
    location: str
    assignment_count: int = 0
    gender_distribution: Dict[str, int] = field(default_factory=lambda: {"male": 0, "female": 0})
    age_distribution: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    disease_stage_distribution: Dict[str, int] = field(default_factory=lambda: defaultdict(int))


class AnatomicalLocationAssigner:
    """解剖位置分配器
    
    基于概率分配解剖位置，并管理位置特异性的疾病特征。
    支持年龄、性别相关的位置分配概率调整。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化解剖位置分配器
        
        Args:
            config: 配置字典，包含位置分配概率和特征参数
        """
        self.config = config or {}
        self.location_probabilities: Dict[AnatomicalLocation, float] = {}
        self.location_features: Dict[AnatomicalLocation, LocationSpecificFeatures] = {}
        self.age_gender_adjustments: Dict[str, Dict[str, float]] = {}
        self.statistics: Dict[str, LocationStatistics] = {}
        self.random_state = np.random.RandomState(42)
        
        # 初始化默认参数
        self._initialize_default_probabilities()
        self._initialize_location_features()
        self._initialize_age_gender_adjustments()
        self._initialize_statistics()
        
        # 如果提供了配置，则更新参数
        if config:
            self._load_configuration(config)
        
        logger.info("解剖位置分配器初始化完成")

    def _initialize_default_probabilities(self) -> None:
        """初始化默认位置分配概率"""
        # 基于医学文献的解剖位置分布
        self.location_probabilities = {
            AnatomicalLocation.PROXIMAL_COLON: 0.40,  # 近端结肠 40%
            AnatomicalLocation.DISTAL_COLON: 0.35,    # 远端结肠 35%
            AnatomicalLocation.RECTUM: 0.25           # 直肠 25%
        }

    def _initialize_location_features(self) -> None:
        """初始化位置特异性特征"""
        self.location_features = {
            AnatomicalLocation.PROXIMAL_COLON: LocationSpecificFeatures(
                screening_sensitivity_modifier=0.8,     # 筛查敏感性较低
                progression_rate_modifier=1.1,          # 进展稍快
                cancer_stage_distribution=[0.25, 0.35, 0.25, 0.15],  # I-IV期分布
                detection_difficulty=0.7,               # 检测较困难
                symptom_onset_modifier=1.2               # 症状出现较晚
            ),
            AnatomicalLocation.DISTAL_COLON: LocationSpecificFeatures(
                screening_sensitivity_modifier=1.0,     # 标准筛查敏感性
                progression_rate_modifier=1.0,          # 标准进展率
                cancer_stage_distribution=[0.30, 0.40, 0.20, 0.10],  # I-IV期分布
                detection_difficulty=0.5,               # 中等检测难度
                symptom_onset_modifier=1.0               # 标准症状出现
            ),
            AnatomicalLocation.RECTUM: LocationSpecificFeatures(
                screening_sensitivity_modifier=1.2,     # 筛查敏感性较高
                progression_rate_modifier=0.9,          # 进展稍慢
                cancer_stage_distribution=[0.35, 0.35, 0.20, 0.10],  # I-IV期分布
                detection_difficulty=0.3,               # 检测相对容易
                symptom_onset_modifier=0.8               # 症状出现较早
            )
        }

    def _initialize_age_gender_adjustments(self) -> None:
        """初始化年龄性别调整因子"""
        # 基于医学文献的年龄性别相关位置分布调整
        self.age_gender_adjustments = {
            "male": {
                "proximal_colon": 1.0,      # 男性近端结肠基准
                "distal_colon": 1.2,        # 男性远端结肠风险较高
                "rectum": 1.3               # 男性直肠风险最高
            },
            "female": {
                "proximal_colon": 1.2,      # 女性近端结肠风险较高
                "distal_colon": 0.9,        # 女性远端结肠风险较低
                "rectum": 0.7               # 女性直肠风险最低
            },
            "age_factors": {
                "proximal_colon": 0.02,     # 年龄每增加1岁，近端结肠概率增加2%
                "distal_colon": 0.0,        # 远端结肠无年龄趋势
                "rectum": -0.01             # 年龄每增加1岁，直肠概率减少1%
            }
        }

    def _initialize_statistics(self) -> None:
        """初始化统计对象"""
        for location in AnatomicalLocation:
            self.statistics[location.value] = LocationStatistics(location=location.value)

    def _load_configuration(self, config: Dict[str, Any]) -> None:
        """从配置字典加载参数"""
        # 加载位置概率
        if "location_probabilities" in config:
            for location_str, prob in config["location_probabilities"].items():
                try:
                    location = AnatomicalLocation(location_str)
                    self.location_probabilities[location] = float(prob)
                except (ValueError, TypeError) as e:
                    logger.warning(f"配置位置概率 {location_str} 时出错: {e}")
        
        # 加载位置特征
        if "location_features" in config:
            for location_str, features in config["location_features"].items():
                try:
                    location = AnatomicalLocation(location_str)
                    self.location_features[location] = LocationSpecificFeatures(**features)
                except (ValueError, TypeError) as e:
                    logger.warning(f"配置位置特征 {location_str} 时出错: {e}")

    def assign_anatomical_location(
        self,
        age: Optional[float] = None,
        gender: Optional[Gender] = None,
        risk_factors: Optional[Dict[str, float]] = None
    ) -> AnatomicalLocation:
        """
        分配解剖位置
        
        Args:
            age: 年龄（可选，用于年龄相关调整）
            gender: 性别（可选，用于性别相关调整）
            risk_factors: 风险因素（可选，用于风险相关调整）
            
        Returns:
            AnatomicalLocation: 分配的解剖位置
        """
        # 获取调整后的概率
        adjusted_probabilities = self._calculate_adjusted_probabilities(age, gender, risk_factors)
        
        # 基于概率进行随机分配
        locations = list(adjusted_probabilities.keys())
        probabilities = list(adjusted_probabilities.values())
        
        # 确保概率和为1
        prob_sum = sum(probabilities)
        if prob_sum > 0:
            probabilities = [p / prob_sum for p in probabilities]
        else:
            # 如果所有概率都为0，使用默认概率
            probabilities = [1.0 / len(locations)] * len(locations)
        
        # 随机选择
        chosen_index = self.random_state.choice(len(locations), p=probabilities)
        assigned_location = locations[chosen_index]
        
        # 更新统计信息
        self._update_statistics(assigned_location, age, gender)
        
        logger.debug(f"分配解剖位置: {assigned_location.value}, 年龄: {age}, 性别: {gender}")
        
        return assigned_location

    def _calculate_adjusted_probabilities(
        self,
        age: Optional[float],
        gender: Optional[Gender],
        risk_factors: Optional[Dict[str, float]]
    ) -> Dict[AnatomicalLocation, float]:
        """
        计算调整后的位置分配概率
        
        Args:
            age: 年龄
            gender: 性别
            risk_factors: 风险因素
            
        Returns:
            Dict: 调整后的位置概率
        """
        adjusted_probs = self.location_probabilities.copy()
        
        # 应用性别调整
        if gender:
            gender_str = gender.value.lower()
            if gender_str in self.age_gender_adjustments:
                gender_adjustments = self.age_gender_adjustments[gender_str]
                for location in adjusted_probs:
                    location_str = location.value
                    if location_str in gender_adjustments:
                        adjusted_probs[location] *= gender_adjustments[location_str]
        
        # 应用年龄调整
        if age and age >= 50:  # 只对50岁以上应用年龄调整
            age_factors = self.age_gender_adjustments.get("age_factors", {})
            for location in adjusted_probs:
                location_str = location.value
                if location_str in age_factors:
                    age_adjustment = 1.0 + (age - 50) * age_factors[location_str]
                    adjusted_probs[location] *= max(0.1, age_adjustment)  # 确保概率不为负
        
        # 应用风险因素调整
        if risk_factors:
            risk_adjustments = self._calculate_risk_factor_adjustments(risk_factors)
            for location in adjusted_probs:
                location_str = location.value
                if location_str in risk_adjustments:
                    adjusted_probs[location] *= risk_adjustments[location_str]
        
        return adjusted_probs

    def _calculate_risk_factor_adjustments(self, risk_factors: Dict[str, float]) -> Dict[str, float]:
        """
        计算风险因素对位置分配的调整
        
        Args:
            risk_factors: 风险因素字典
            
        Returns:
            Dict: 位置特异性风险调整因子
        """
        # 基于医学文献的风险因素对不同位置的影响
        risk_location_effects = {
            "smoking": {
                "proximal_colon": 1.1,
                "distal_colon": 1.2,
                "rectum": 1.3
            },
            "alcohol": {
                "proximal_colon": 1.0,
                "distal_colon": 1.1,
                "rectum": 1.2
            },
            "obesity": {
                "proximal_colon": 1.2,
                "distal_colon": 1.1,
                "rectum": 1.0
            },
            "inflammatory_bowel_disease": {
                "proximal_colon": 1.3,
                "distal_colon": 1.2,
                "rectum": 1.1
            }
        }
        
        adjustments = {"proximal_colon": 1.0, "distal_colon": 1.0, "rectum": 1.0}
        
        for factor, value in risk_factors.items():
            if factor in risk_location_effects:
                effects = risk_location_effects[factor]
                for location, effect in effects.items():
                    # 风险因素值在0-1之间，应用相应的调整
                    adjustments[location] *= 1.0 + (value * (effect - 1.0))
        
        return adjustments

    def _update_statistics(
        self,
        location: AnatomicalLocation,
        age: Optional[float],
        gender: Optional[Gender]
    ) -> None:
        """更新统计信息"""
        location_str = location.value
        stats = self.statistics[location_str]
        
        # 更新分配计数
        stats.assignment_count += 1
        
        # 更新性别分布
        if gender:
            gender_str = gender.value.lower()
            if gender_str in stats.gender_distribution:
                stats.gender_distribution[gender_str] += 1
        
        # 更新年龄分布
        if age:
            age_group = self._get_age_group(age)
            stats.age_distribution[age_group] += 1

    def _get_age_group(self, age: float) -> str:
        """获取年龄组"""
        if age < 50:
            return "<50"
        elif age < 60:
            return "50-59"
        elif age < 70:
            return "60-69"
        elif age < 80:
            return "70-79"
        else:
            return ">=80"

    def get_location_features(self, location: AnatomicalLocation) -> LocationSpecificFeatures:
        """
        获取位置特异性特征
        
        Args:
            location: 解剖位置
            
        Returns:
            LocationSpecificFeatures: 位置特异性特征
        """
        return self.location_features.get(location, LocationSpecificFeatures(
            screening_sensitivity_modifier=1.0,
            progression_rate_modifier=1.0,
            cancer_stage_distribution=[0.25, 0.25, 0.25, 0.25],
            detection_difficulty=0.5,
            symptom_onset_modifier=1.0
        ))

    def apply_location_progression_adjustment(
        self,
        base_progression_rate: float,
        location: AnatomicalLocation
    ) -> float:
        """
        应用位置相关的进展参数调整
        
        Args:
            base_progression_rate: 基础进展率
            location: 解剖位置
            
        Returns:
            float: 调整后的进展率
        """
        features = self.get_location_features(location)
        adjusted_rate = base_progression_rate * features.progression_rate_modifier
        
        return max(0.0, min(adjusted_rate, 1.0))  # 确保在有效范围内

    def get_cancer_stage_distribution(self, location: AnatomicalLocation) -> List[float]:
        """
        获取位置特异性的癌症分期分布
        
        Args:
            location: 解剖位置
            
        Returns:
            List[float]: 癌症分期分布（I-IV期）
        """
        features = self.get_location_features(location)
        return features.cancer_stage_distribution.copy()

    def get_location_statistics(self) -> Dict[str, Dict[str, Any]]:
        """
        获取位置分配统计信息
        
        Returns:
            Dict: 位置统计摘要
        """
        total_assignments = sum(stats.assignment_count for stats in self.statistics.values())
        
        summary = {}
        for location_str, stats in self.statistics.items():
            if total_assignments > 0:
                assignment_percentage = (stats.assignment_count / total_assignments) * 100
            else:
                assignment_percentage = 0.0
            
            summary[location_str] = {
                "assignment_count": stats.assignment_count,
                "assignment_percentage": assignment_percentage,
                "gender_distribution": stats.gender_distribution.copy(),
                "age_distribution": dict(stats.age_distribution),
                "disease_stage_distribution": dict(stats.disease_stage_distribution)
            }
        
        return summary

    def export_configuration(self) -> Dict[str, Any]:
        """
        导出当前配置
        
        Returns:
            Dict: 配置字典
        """
        return {
            "location_probabilities": {
                location.value: prob for location, prob in self.location_probabilities.items()
            },
            "location_features": {
                location.value: {
                    "screening_sensitivity_modifier": features.screening_sensitivity_modifier,
                    "progression_rate_modifier": features.progression_rate_modifier,
                    "cancer_stage_distribution": features.cancer_stage_distribution,
                    "detection_difficulty": features.detection_difficulty,
                    "symptom_onset_modifier": features.symptom_onset_modifier
                }
                for location, features in self.location_features.items()
            },
            "age_gender_adjustments": self.age_gender_adjustments,
            "statistics": self.get_location_statistics()
        }

    def reset_statistics(self) -> None:
        """重置统计信息"""
        self._initialize_statistics()
        logger.info("位置分配统计信息已重置")

    def set_random_seed(self, seed: int) -> None:
        """设置随机种子"""
        self.random_state = np.random.RandomState(seed)
        logger.info(f"随机种子已设置为: {seed}")
