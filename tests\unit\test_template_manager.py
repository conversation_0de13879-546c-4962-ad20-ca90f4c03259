"""
策略模板管理系统测试

测试模板的保存、加载、验证和管理功能。
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from src.modules.screening.template_manager import TemplateManager, TemplateValidationError
from src.modules.screening.strategy import (
    ScreeningStrategy, ScreeningInterval, TargetPopulation, ScreeningFrequency
)
from src.modules.screening.enums import ScreeningToolType


class TestTemplateManager:
    """测试模板管理器"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def template_manager(self, temp_dir):
        """创建模板管理器"""
        return TemplateManager(temp_dir)
    
    @pytest.fixture
    def sample_strategy(self):
        """创建示例策略"""
        strategy = ScreeningStrategy(
            name="测试策略",
            description="用于测试的筛查策略",
            version="1.0",
            author="测试作者"
        )
        
        # 添加筛查间隔
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL
        )
        strategy.add_interval(interval)
        
        return strategy
    
    def test_template_manager_initialization(self, temp_dir):
        """测试模板管理器初始化"""
        manager = TemplateManager(temp_dir)
        
        assert manager.templates_dir == temp_dir
        assert temp_dir.exists()
        assert manager.supported_formats == {'.yaml', '.yml', '.json'}
        assert manager._template_cache == {}
    
    def test_save_template_yaml(self, template_manager, sample_strategy):
        """测试保存YAML格式模板"""
        file_path = template_manager.save_template(
            sample_strategy, 
            filename="test_strategy",
            format="yaml"
        )
        
        assert file_path.exists()
        assert file_path.suffix == ".yaml"
        assert file_path.name == "test_strategy.yaml"
        
        # 检查文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "测试策略" in content
            assert "screening_strategy" in content
    
    def test_save_template_json(self, template_manager, sample_strategy):
        """测试保存JSON格式模板"""
        file_path = template_manager.save_template(
            sample_strategy,
            filename="test_strategy",
            format="json"
        )
        
        assert file_path.exists()
        assert file_path.suffix == ".json"
        assert file_path.name == "test_strategy.json"
    
    def test_save_template_auto_filename(self, template_manager, sample_strategy):
        """测试自动生成文件名"""
        file_path = template_manager.save_template(sample_strategy)
        
        assert file_path.exists()
        assert "测试策略" in file_path.stem or "test" in file_path.stem.lower()
    
    def test_save_template_overwrite_protection(self, template_manager, sample_strategy):
        """测试覆盖保护"""
        # 先保存一次
        template_manager.save_template(sample_strategy, filename="test_strategy")
        
        # 再次保存应该失败
        with pytest.raises(FileExistsError):
            template_manager.save_template(sample_strategy, filename="test_strategy")
        
        # 允许覆盖应该成功
        file_path = template_manager.save_template(
            sample_strategy, 
            filename="test_strategy", 
            overwrite=True
        )
        assert file_path.exists()
    
    def test_load_template(self, template_manager, sample_strategy):
        """测试加载模板"""
        # 先保存模板
        template_manager.save_template(sample_strategy, filename="test_load")
        
        # 加载模板
        loaded_strategy = template_manager.load_template("test_load")
        
        assert loaded_strategy.name == sample_strategy.name
        assert loaded_strategy.description == sample_strategy.description
        assert loaded_strategy.version == sample_strategy.version
        assert loaded_strategy.author == sample_strategy.author
        assert len(loaded_strategy.intervals) == len(sample_strategy.intervals)
    
    def test_load_template_with_extension(self, template_manager, sample_strategy):
        """测试加载带扩展名的模板"""
        template_manager.save_template(sample_strategy, filename="test_ext")
        
        # 使用完整文件名加载
        loaded_strategy = template_manager.load_template("test_ext.yaml")
        assert loaded_strategy.name == sample_strategy.name
    
    def test_load_nonexistent_template(self, template_manager):
        """测试加载不存在的模板"""
        with pytest.raises(FileNotFoundError):
            template_manager.load_template("nonexistent")
    
    def test_template_caching(self, template_manager, sample_strategy):
        """测试模板缓存"""
        # 保存模板
        template_manager.save_template(sample_strategy, filename="test_cache")
        
        # 第一次加载
        strategy1 = template_manager.load_template("test_cache")
        
        # 第二次加载应该使用缓存
        strategy2 = template_manager.load_template("test_cache")
        
        # 检查缓存是否工作
        assert len(template_manager._template_cache) > 0
        assert template_manager._cache_timestamp is not None
    
    def test_list_templates(self, template_manager, sample_strategy):
        """测试列出模板"""
        # 保存几个模板
        template_manager.save_template(sample_strategy, filename="template1")
        
        strategy2 = ScreeningStrategy(name="策略2", description="第二个策略")
        strategy2.template_category = "测试分类"
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=50,
            end_age=75
        )
        strategy2.add_interval(interval)
        template_manager.save_template(strategy2, filename="template2")
        
        # 列出所有模板
        templates = template_manager.list_templates()
        
        assert len(templates) == 2
        assert any(t['name'] == "测试策略" for t in templates)
        assert any(t['name'] == "策略2" for t in templates)
        
        # 按分类过滤
        filtered = template_manager.list_templates(category="测试分类")
        assert len(filtered) == 1
        assert filtered[0]['name'] == "策略2"
    
    def test_delete_template(self, template_manager, sample_strategy):
        """测试删除模板"""
        # 保存模板
        template_manager.save_template(sample_strategy, filename="test_delete")
        
        # 确认文件存在
        templates = template_manager.list_templates()
        assert any(t['filename'] == "test_delete.yaml" for t in templates)
        
        # 删除模板
        result = template_manager.delete_template("test_delete")
        assert result is True
        
        # 确认文件已删除
        templates = template_manager.list_templates()
        assert not any(t['filename'] == "test_delete.yaml" for t in templates)
        
        # 删除不存在的模板
        result = template_manager.delete_template("nonexistent")
        assert result is False
    
    def test_validate_template(self, template_manager, sample_strategy):
        """测试模板验证"""
        # 保存有效模板
        template_manager.save_template(sample_strategy, filename="test_valid")
        
        # 验证模板
        errors = template_manager.validate_template("test_valid")
        assert isinstance(errors, list)
        # 有效策略应该没有错误或只有警告
    
    def test_migrate_template(self, template_manager, sample_strategy):
        """测试模板迁移"""
        # 保存模板
        template_manager.save_template(sample_strategy, filename="test_migrate")
        
        # 迁移到新版本
        result = template_manager.migrate_template("test_migrate", "2.0")
        assert result is True
        
        # 验证版本已更新
        migrated_strategy = template_manager.load_template("test_migrate")
        assert migrated_strategy.version == "2.0"
    
    def test_get_categories(self, template_manager):
        """测试获取分类"""
        # 创建不同分类的策略
        strategy1 = ScreeningStrategy(name="策略1")
        strategy1.template_category = "分类A"
        interval = ScreeningInterval(tool_type=ScreeningToolType.FIT, start_age=50, end_age=75)
        strategy1.add_interval(interval)
        
        strategy2 = ScreeningStrategy(name="策略2")
        strategy2.template_category = "分类B"
        strategy2.add_interval(interval)
        
        template_manager.save_template(strategy1, filename="cat_a")
        template_manager.save_template(strategy2, filename="cat_b")
        
        categories = template_manager.get_categories()
        assert "分类A" in categories
        assert "分类B" in categories
        assert isinstance(categories, list)
    
    def test_sanitize_filename(self, template_manager):
        """测试文件名清理"""
        unsafe_name = "策略<>:\"/\\|?*名称"
        safe_name = template_manager._sanitize_filename(unsafe_name)
        
        # 检查不安全字符已被替换
        unsafe_chars = '<>:"/\\|?*'
        for char in unsafe_chars:
            assert char not in safe_name
        
        # 检查长文件名被截断
        long_name = "a" * 100
        truncated = template_manager._sanitize_filename(long_name)
        assert len(truncated) <= 50
    
    def test_template_validation_error(self, template_manager, temp_dir):
        """测试模板验证错误"""
        # 创建无效的模板文件
        invalid_file = temp_dir / "invalid.yaml"
        with open(invalid_file, 'w') as f:
            f.write("invalid: yaml: content:")
        
        with pytest.raises(Exception):
            template_manager.load_template("invalid")
    
    def test_prepare_template_data(self, template_manager, sample_strategy):
        """测试准备模板数据"""
        data = template_manager._prepare_template_data(sample_strategy)
        
        assert "template_metadata" in data
        assert "screening_strategy" in data
        assert data["template_metadata"]["format_version"] == "1.0"
        assert data["screening_strategy"]["name"] == sample_strategy.name
    
    def test_validate_template_data(self, template_manager):
        """测试模板数据验证"""
        # 有效数据
        valid_data = {
            "screening_strategy": {
                "name": "测试",
                "intervals": [],
                "target_population": {}
            }
        }
        
        # 应该不抛出异常
        template_manager._validate_template_data(valid_data)
        
        # 无效数据 - 缺少必需字段
        invalid_data = {
            "screening_strategy": {
                "name": "测试"
                # 缺少 intervals 和 target_population
            }
        }
        
        with pytest.raises(TemplateValidationError):
            template_manager._validate_template_data(invalid_data)
        
        # 无效数据 - 不是字典
        with pytest.raises(TemplateValidationError):
            template_manager._validate_template_data("not a dict")
        
        # 无效数据 - 缺少 screening_strategy
        with pytest.raises(TemplateValidationError):
            template_manager._validate_template_data({"other": "data"})
    
    def test_clear_cache(self, template_manager, sample_strategy):
        """测试清除缓存"""
        # 加载模板以填充缓存
        template_manager.save_template(sample_strategy, filename="test_cache_clear")
        template_manager.load_template("test_cache_clear")
        
        # 确认缓存有内容
        assert len(template_manager._template_cache) > 0
        assert template_manager._cache_timestamp is not None
        
        # 清除缓存
        template_manager._clear_cache()
        
        # 确认缓存已清空
        assert len(template_manager._template_cache) == 0
        assert template_manager._cache_timestamp is None
