"""
肠镜筛查逻辑测试

测试当初筛工具为肠镜时，不需要诊断性肠镜检查的逻辑。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.core.individual import Individual
from src.core.enums import DiseaseState, Gender
from src.modules.screening import (
    ScreeningResultProcessor, ScreeningResult, ScreeningResultType,
    FollowupAction, FollowupManager, ScreeningToolType
)
from src.modules.screening.tools import ColonoscopyTool


class TestColonoscopyScreeningLogic:
    """肠镜筛查逻辑测试类"""
    
    @pytest.fixture
    def individual(self):
        """创建测试个体"""
        return Individual(
            birth_year=1970,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.NORMAL
        )
    
    @pytest.fixture
    def colonoscopy_tool(self):
        """创建肠镜工具"""
        config = {
            'sensitivity_by_state': {
                'normal': 0.0,
                'low_risk_adenoma': 0.85,
                'high_risk_adenoma': 0.95,
                'clinical_cancer': 0.98
            },
            'specificity': 0.95,
            'cost_per_procedure': 1500.0
        }
        return ColonoscopyTool(config)
    
    @pytest.fixture
    def result_processor(self):
        """创建结果处理器"""
        config = {
            'sensitivity_by_state': {
                'normal': 0.0,
                'low_risk_adenoma': 0.85,
                'high_risk_adenoma': 0.95,
                'clinical_cancer': 0.98
            },
            'specificity': 0.95
        }
        return ScreeningResultProcessor(config)
    
    @pytest.fixture
    def followup_manager(self):
        """创建后续诊断管理器"""
        config = {
            'diagnostic_capacity': {
                'colonoscopy': 100,
                'specialist_consultation': 50
            },
            'waiting_times': {
                'colonoscopy': 14,  # 14天
                'specialist_consultation': 7
            }
        }
        return FollowupManager(config)
    
    def test_colonoscopy_positive_result_followup_action(self, individual, colonoscopy_tool, result_processor):
        """测试肠镜阳性结果的后续行动"""
        # 设置个体为有病变状态以产生阳性结果
        individual.current_disease_state = DiseaseState.HIGH_RISK_ADENOMA
        
        # 处理肠镜筛查
        result = result_processor.process_screening(individual, colonoscopy_tool)
        
        # 验证肠镜阳性结果的后续行动是立即治疗，而不是诊断性肠镜
        if result.result_type == ScreeningResultType.POSITIVE:
            assert result.followup_action == FollowupAction.IMMEDIATE_TREATMENT
            assert result.followup_action != FollowupAction.DIAGNOSTIC_COLONOSCOPY
    
    def test_followup_manager_no_diagnostic_for_colonoscopy(self, individual, followup_manager):
        """测试后续诊断管理器不为肠镜筛查安排诊断性肠镜"""
        # 创建肠镜筛查阳性结果
        colonoscopy_result = Mock()
        colonoscopy_result.tool_type = ScreeningToolType.COLONOSCOPY
        colonoscopy_result.result_type = ScreeningResultType.POSITIVE
        colonoscopy_result.followup_action = FollowupAction.IMMEDIATE_TREATMENT
        
        # 确定诊断程序类型
        diagnostic_procedure = followup_manager._determine_diagnostic_procedure(colonoscopy_result)
        
        # 肠镜筛查不应该触发额外的诊断性肠镜
        assert diagnostic_procedure is None
    
    def test_fit_positive_result_needs_diagnostic_colonoscopy(self, individual, result_processor):
        """测试FIT阳性结果需要诊断性肠镜（对比测试）"""
        from src.modules.screening.tools import FITTool
        
        # 创建FIT工具
        fit_config = {
            'sensitivity_by_state': {
                'normal': 0.0,
                'low_risk_adenoma': 0.7,
                'high_risk_adenoma': 0.8,
                'clinical_cancer': 0.9
            },
            'specificity': 0.95
        }
        fit_tool = FITTool(fit_config)
        
        # 设置个体为有病变状态以产生阳性结果
        individual.current_disease_state = DiseaseState.HIGH_RISK_ADENOMA
        
        # 处理FIT筛查
        result = result_processor.process_screening(individual, fit_tool)
        
        # 验证FIT阳性结果的后续行动是诊断性肠镜
        if result.result_type == ScreeningResultType.POSITIVE:
            assert result.followup_action == FollowupAction.DIAGNOSTIC_COLONOSCOPY
    
    def test_followup_manager_schedules_diagnostic_for_fit(self, individual, followup_manager):
        """测试后续诊断管理器为FIT筛查安排诊断性肠镜（对比测试）"""
        # 创建FIT筛查阳性结果
        fit_result = Mock()
        fit_result.tool_type = ScreeningToolType.FIT
        fit_result.result_type = ScreeningResultType.POSITIVE
        fit_result.followup_action = FollowupAction.DIAGNOSTIC_COLONOSCOPY
        
        # 确定诊断程序类型
        diagnostic_procedure = followup_manager._determine_diagnostic_procedure(fit_result)
        
        # FIT筛查阳性应该触发诊断性肠镜
        from src.modules.screening.followup_manager import DiagnosticProcedure
        assert diagnostic_procedure == DiagnosticProcedure.COLONOSCOPY
    
    def test_colonoscopy_screening_workflow_integration(self, individual, result_processor, followup_manager):
        """测试肠镜筛查的完整工作流程集成"""
        from src.modules.screening.tools import ColonoscopyTool
        
        # 创建肠镜工具
        colonoscopy_config = {
            'sensitivity_by_state': {
                'normal': 0.0,
                'low_risk_adenoma': 0.85,
                'high_risk_adenoma': 0.95,
                'clinical_cancer': 0.98
            },
            'specificity': 0.95
        }
        colonoscopy_tool = ColonoscopyTool(colonoscopy_config)
        
        # 设置个体为有病变状态
        individual.current_disease_state = DiseaseState.HIGH_RISK_ADENOMA
        
        # 1. 处理肠镜筛查
        screening_result = result_processor.process_screening(individual, colonoscopy_tool)
        
        # 2. 检查后续行动
        if screening_result.result_type == ScreeningResultType.POSITIVE:
            # 肠镜阳性应该是立即治疗
            assert screening_result.followup_action == FollowupAction.IMMEDIATE_TREATMENT
            
            # 3. 尝试安排后续诊断
            appointment = followup_manager.schedule_followup(screening_result, individual)
            
            # 肠镜筛查不应该安排额外的诊断性肠镜
            assert appointment is None
    
    def test_different_screening_tools_comparison(self, individual, result_processor):
        """测试不同筛查工具的后续行动对比"""
        from src.modules.screening.tools import FITTool, ColonoscopyTool
        
        # 设置个体为有病变状态
        individual.current_disease_state = DiseaseState.HIGH_RISK_ADENOMA
        
        # 测试FIT工具
        fit_config = {
            'sensitivity_by_state': {
                'normal': 0.0,
                'low_risk_adenoma': 0.7,
                'high_risk_adenoma': 0.8,
                'clinical_cancer': 0.9
            },
            'specificity': 0.95
        }
        fit_tool = FITTool(fit_config)
        fit_result = result_processor.process_screening(individual, fit_tool)
        
        # 测试肠镜工具
        colonoscopy_config = {
            'sensitivity_by_state': {
                'normal': 0.0,
                'low_risk_adenoma': 0.85,
                'high_risk_adenoma': 0.95,
                'clinical_cancer': 0.98
            },
            'specificity': 0.95
        }
        colonoscopy_tool = ColonoscopyTool(colonoscopy_config)
        colonoscopy_result = result_processor.process_screening(individual, colonoscopy_tool)
        
        # 验证不同的后续行动
        if fit_result.result_type == ScreeningResultType.POSITIVE:
            assert fit_result.followup_action == FollowupAction.DIAGNOSTIC_COLONOSCOPY
        
        if colonoscopy_result.result_type == ScreeningResultType.POSITIVE:
            assert colonoscopy_result.followup_action == FollowupAction.IMMEDIATE_TREATMENT
        
        # 确保两者不同
        if (fit_result.result_type == ScreeningResultType.POSITIVE and 
            colonoscopy_result.result_type == ScreeningResultType.POSITIVE):
            assert fit_result.followup_action != colonoscopy_result.followup_action
