"""
筛查策略模板管理系统

实现策略模板的保存、加载、验证和迁移功能。
"""

import os
import yaml
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from dataclasses import asdict

from .strategy import ScreeningStrategy, ScreeningInterval, TargetPopulation, ScreeningFrequency
from .enums import ScreeningToolType

logger = logging.getLogger(__name__)


class TemplateValidationError(Exception):
    """模板验证错误"""
    pass


class TemplateManager:
    """
    策略模板管理器
    
    负责策略模板的文件操作、验证和版本管理。
    """
    
    def __init__(self, templates_dir: Union[str, Path] = "data/screening_strategies"):
        """
        初始化模板管理器
        
        Args:
            templates_dir: 模板文件目录
        """
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的文件格式
        self.supported_formats = {'.yaml', '.yml', '.json'}
        
        # 模板缓存
        self._template_cache: Dict[str, ScreeningStrategy] = {}
        self._cache_timestamp: Optional[datetime] = None
        
        logger.info(f"模板管理器初始化完成，模板目录: {self.templates_dir}")
    
    def save_template(
        self,
        strategy: ScreeningStrategy,
        filename: Optional[str] = None,
        format: str = 'yaml',
        overwrite: bool = False
    ) -> Path:
        """
        保存策略模板
        
        Args:
            strategy: 策略对象
            filename: 文件名（不包含扩展名）
            format: 文件格式 ('yaml' 或 'json')
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            保存的文件路径
        """
        if not filename:
            # 生成默认文件名
            safe_name = self._sanitize_filename(strategy.name)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_name}_{timestamp}"
        
        # 确定文件扩展名
        if format.lower() == 'json':
            file_path = self.templates_dir / f"{filename}.json"
        else:
            file_path = self.templates_dir / f"{filename}.yaml"
        
        # 检查文件是否已存在
        if file_path.exists() and not overwrite:
            raise FileExistsError(f"模板文件已存在: {file_path}")
        
        # 验证策略
        validation_errors = strategy.validate()
        if validation_errors:
            logger.warning(f"策略验证有警告: {validation_errors}")
        
        # 准备数据
        template_data = self._prepare_template_data(strategy)
        
        # 保存文件
        try:
            if format.lower() == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(template_data, f, ensure_ascii=False, indent=2)
            else:
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(template_data, f, allow_unicode=True, default_flow_style=False)
            
            logger.info(f"策略模板已保存: {file_path}")
            
            # 清除缓存
            self._clear_cache()
            
            return file_path
            
        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            raise
    
    def load_template(self, filename: str) -> ScreeningStrategy:
        """
        加载策略模板
        
        Args:
            filename: 文件名（可包含或不包含扩展名）
            
        Returns:
            策略对象
        """
        file_path = self._find_template_file(filename)
        if not file_path:
            raise FileNotFoundError(f"未找到模板文件: {filename}")
        
        # 检查缓存
        cache_key = str(file_path)
        if (cache_key in self._template_cache and 
            self._cache_timestamp and 
            file_path.stat().st_mtime <= self._cache_timestamp.timestamp()):
            logger.debug(f"从缓存加载模板: {filename}")
            return self._template_cache[cache_key]
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    template_data = json.load(f)
                else:
                    template_data = yaml.safe_load(f)
            
            # 验证模板数据
            self._validate_template_data(template_data)
            
            # 创建策略对象
            strategy = self._create_strategy_from_template(template_data)
            
            # 更新缓存
            self._template_cache[cache_key] = strategy
            self._cache_timestamp = datetime.now()
            
            logger.info(f"策略模板已加载: {file_path}")
            return strategy
            
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            raise
    
    def list_templates(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        列出所有可用模板
        
        Args:
            category: 模板分类过滤
            
        Returns:
            模板信息列表
        """
        templates = []
        
        for file_path in self.templates_dir.iterdir():
            if file_path.is_file() and file_path.suffix in self.supported_formats:
                try:
                    template_info = self._get_template_info(file_path)
                    
                    # 分类过滤
                    if category and template_info.get('category') != category:
                        continue
                    
                    templates.append(template_info)
                    
                except Exception as e:
                    logger.warning(f"读取模板信息失败: {file_path}, 错误: {e}")
        
        # 按名称排序
        templates.sort(key=lambda x: x['name'])
        
        return templates
    
    def delete_template(self, filename: str) -> bool:
        """
        删除策略模板
        
        Args:
            filename: 文件名
            
        Returns:
            删除是否成功
        """
        file_path = self._find_template_file(filename)
        if not file_path:
            return False
        
        try:
            file_path.unlink()
            
            # 清除缓存
            cache_key = str(file_path)
            if cache_key in self._template_cache:
                del self._template_cache[cache_key]
            
            logger.info(f"模板已删除: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"删除模板失败: {e}")
            return False
    
    def validate_template(self, filename: str) -> List[str]:
        """
        验证模板文件
        
        Args:
            filename: 文件名
            
        Returns:
            验证错误列表
        """
        try:
            strategy = self.load_template(filename)
            return strategy.validate()
        except Exception as e:
            return [f"模板加载失败: {str(e)}"]
    
    def migrate_template(self, filename: str, target_version: str = "1.0") -> bool:
        """
        迁移模板到新版本
        
        Args:
            filename: 文件名
            target_version: 目标版本
            
        Returns:
            迁移是否成功
        """
        try:
            # 加载现有模板
            strategy = self.load_template(filename)
            
            # 更新版本
            strategy.version = target_version
            strategy.modified_date = datetime.now()
            
            # 重新保存
            file_path = self._find_template_file(filename)
            format = 'json' if file_path.suffix.lower() == '.json' else 'yaml'
            
            self.save_template(strategy, filename.split('.')[0], format, overwrite=True)
            
            logger.info(f"模板迁移完成: {filename} -> v{target_version}")
            return True
            
        except Exception as e:
            logger.error(f"模板迁移失败: {e}")
            return False
    
    def get_categories(self) -> List[str]:
        """获取所有模板分类"""
        categories = set()
        
        for template_info in self.list_templates():
            if template_info.get('category'):
                categories.add(template_info['category'])
        
        return sorted(list(categories))
    
    def _find_template_file(self, filename: str) -> Optional[Path]:
        """查找模板文件"""
        # 如果已包含扩展名
        if '.' in filename:
            file_path = self.templates_dir / filename
            if file_path.exists():
                return file_path
        else:
            # 尝试所有支持的扩展名
            for ext in self.supported_formats:
                file_path = self.templates_dir / f"{filename}{ext}"
                if file_path.exists():
                    return file_path
        
        return None
    
    def _sanitize_filename(self, name: str) -> str:
        """清理文件名"""
        # 移除或替换不安全字符
        unsafe_chars = '<>:"/\\|?*'
        safe_name = name
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
        
        # 限制长度
        if len(safe_name) > 50:
            safe_name = safe_name[:50]
        
        return safe_name
    
    def _prepare_template_data(self, strategy: ScreeningStrategy) -> Dict[str, Any]:
        """准备模板数据"""
        # 转换为字典，使用序列化标志
        data = strategy.to_dict(for_serialization=True)
        
        # 添加模板元数据
        template_data = {
            "template_metadata": {
                "format_version": "1.0",
                "created_by": "ScreeningStrategyDesigner",
                "created_at": datetime.now().isoformat(),
                "description": "筛查策略模板文件"
            },
            "screening_strategy": data
        }
        
        return template_data
    
    def _validate_template_data(self, data: Dict[str, Any]) -> None:
        """验证模板数据格式"""
        if not isinstance(data, dict):
            raise TemplateValidationError("模板数据必须是字典格式")
        
        if "screening_strategy" not in data:
            raise TemplateValidationError("模板数据缺少 'screening_strategy' 字段")
        
        strategy_data = data["screening_strategy"]
        
        # 验证必需字段
        required_fields = ["name", "intervals", "target_population"]
        for field in required_fields:
            if field not in strategy_data:
                raise TemplateValidationError(f"策略数据缺少必需字段: {field}")
        
        # 验证间隔数据
        if not isinstance(strategy_data["intervals"], list):
            raise TemplateValidationError("间隔数据必须是列表格式")
        
        # 验证目标人群数据
        if not isinstance(strategy_data["target_population"], dict):
            raise TemplateValidationError("目标人群数据必须是字典格式")
    
    def _create_strategy_from_template(self, data: Dict[str, Any]) -> ScreeningStrategy:
        """从模板数据创建策略对象"""
        strategy_data = data["screening_strategy"]
        return ScreeningStrategy.from_dict(strategy_data)
    
    def _get_template_info(self, file_path: Path) -> Dict[str, Any]:
        """获取模板基本信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    data = json.load(f)
                else:
                    data = yaml.safe_load(f)
            
            strategy_data = data.get("screening_strategy", {})
            
            return {
                "filename": file_path.name,
                "name": strategy_data.get("name", "未命名策略"),
                "description": strategy_data.get("description", ""),
                "version": strategy_data.get("version", "1.0"),
                "author": strategy_data.get("author", ""),
                "category": strategy_data.get("template_category", "其他"),
                "created_date": strategy_data.get("created_date", ""),
                "modified_date": strategy_data.get("modified_date", ""),
                "file_size": file_path.stat().st_size,
                "file_modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
            }
            
        except Exception as e:
            logger.warning(f"获取模板信息失败: {file_path}, 错误: {e}")
            return {
                "filename": file_path.name,
                "name": "读取失败",
                "description": f"错误: {str(e)}",
                "version": "未知",
                "author": "",
                "category": "错误",
                "created_date": "",
                "modified_date": "",
                "file_size": file_path.stat().st_size,
                "file_modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
            }
    
    def _clear_cache(self) -> None:
        """清除模板缓存"""
        self._template_cache.clear()
        self._cache_timestamp = None
