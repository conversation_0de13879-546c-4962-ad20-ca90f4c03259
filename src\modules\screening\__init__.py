"""
筛查模块

此模块包含筛查工具配置系统的核心组件，包括：
- 筛查工具枚举和类型定义
- 筛查工具基础类和特异性实现
- 成本模型和性能参数管理
- 配置导入/导出功能
"""

from .enums import ScreeningToolType, ScreeningResult, InvasivenessLevel, OperatorSkillLevel
from .screening_tool import ScreeningTool, ScreeningPerformance, ScreeningCharacteristics, ScreeningToolFactory
from .tools import FITTool, ColonoscopyTool, SigmoidoscopyTool, RiskQuestionnaireTool, OtherTool
from .performance_model import AdvancedPerformanceModel, DiseaseStatePerformance, LocationSpecificPerformance
from .cost_model import ScreeningCostModel, CostItem, CostComponent, CostCategory, RegionalCostProfile
from .config_manager import ScreeningToolConfigManager, ConfigurationMetadata
from .config_templates import ScreeningToolConfigTemplates
from .strategy import ScreeningStrategy, ScreeningInterval, TargetPopulation, ScreeningFrequency
from .result_processor import (
    ScreeningResultProcessor, ScreeningResult as DetailedScreeningResult,
    ScreeningResultType, FollowupAction, FalseResultType, FalseResultImpact
)
from .followup_manager import (
    FollowupManager, DiagnosticAppointment, DiagnosticResult,
    DiagnosticProcedure, AppointmentStatus
)
from .effectiveness_metrics import (
    EffectivenessMetrics, DetectionMetrics, IncidenceReductionMetrics,
    MortalityReductionMetrics, SurvivalBenefitMetrics, CostEffectivenessMetrics
)
from .tool_sequence import ToolSequence, ScheduledScreening, SequenceConstraint, SchedulingStatus, TriggerCondition
from .template_manager import TemplateManager, TemplateValidationError
from .strategy_validator import StrategyValidator, ValidationResult, ValidationSeverity
from .eligibility_checker import ScreeningEligibilityChecker, EligibilityResult, EligibilityReason

# 注册所有工具类型
ScreeningToolFactory.register_tool(ScreeningToolType.FIT, FITTool)
ScreeningToolFactory.register_tool(ScreeningToolType.COLONOSCOPY, ColonoscopyTool)
ScreeningToolFactory.register_tool(ScreeningToolType.SIGMOIDOSCOPY, SigmoidoscopyTool)
ScreeningToolFactory.register_tool(ScreeningToolType.RISK_QUESTIONNAIRE, RiskQuestionnaireTool)
ScreeningToolFactory.register_tool(ScreeningToolType.OTHER, OtherTool)

__all__ = [
    # 基础枚举和类型
    "ScreeningToolType",
    "ScreeningResult",
    "InvasivenessLevel",
    "OperatorSkillLevel",

    # 核心工具类
    "ScreeningTool",
    "ScreeningPerformance",
    "ScreeningCharacteristics",
    "ScreeningToolFactory",

    # 具体工具实现
    "FITTool",
    "ColonoscopyTool",
    "SigmoidoscopyTool",
    "RiskQuestionnaireTool",
    "OtherTool",

    # 高级性能模型
    "AdvancedPerformanceModel",
    "DiseaseStatePerformance",
    "LocationSpecificPerformance",

    # 成本模型
    "ScreeningCostModel",
    "CostItem",
    "CostComponent",
    "CostCategory",
    "RegionalCostProfile",

    # 配置管理
    "ScreeningToolConfigManager",
    "ConfigurationMetadata",
    "ScreeningToolConfigTemplates",

    # 策略管理
    "ScreeningStrategy",
    "ScreeningInterval",
    "TargetPopulation",
    "ScreeningFrequency",

    # 结果处理
    "ScreeningResultProcessor",
    "DetailedScreeningResult",
    "ScreeningResultType",
    "FollowupAction",
    "FalseResultType",
    "FalseResultImpact",

    # 后续诊断管理
    "FollowupManager",
    "DiagnosticAppointment",
    "DiagnosticResult",
    "DiagnosticProcedure",
    "AppointmentStatus",

    # 效果评估指标
    "EffectivenessMetrics",
    "DetectionMetrics",
    "IncidenceReductionMetrics",
    "MortalityReductionMetrics",
    "SurvivalBenefitMetrics",
    "CostEffectivenessMetrics",

    # 工具序列管理
    "ToolSequence",
    "ScheduledScreening",
    "SequenceConstraint",
    "SchedulingStatus",
    "TriggerCondition",

    # 模板管理
    "TemplateManager",
    "TemplateValidationError",

    # 策略验证
    "StrategyValidator",
    "ValidationResult",
    "ValidationSeverity",

    # 筛查资格检查
    "ScreeningEligibilityChecker",
    "EligibilityResult",
    "EligibilityReason",
]
