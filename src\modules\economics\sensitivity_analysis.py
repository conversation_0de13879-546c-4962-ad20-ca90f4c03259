"""
成本敏感性分析工具

实现单因素和多因素敏感性分析，支持蒙特卡洛模拟和结果可视化。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Union, Callable, Any
from enum import Enum
import logging
import numpy as np
import pandas as pd
from pathlib import Path
import json
import yaml

logger = logging.getLogger(__name__)


class ParameterType(Enum):
    """参数类型"""
    COST = "cost"
    PROBABILITY = "probability"
    RATE = "rate"
    MULTIPLIER = "multiplier"
    DURATION = "duration"


class DistributionType(Enum):
    """分布类型"""
    UNIFORM = "uniform"
    NORMAL = "normal"
    LOGNORMAL = "lognormal"
    BETA = "beta"
    GAMMA = "gamma"
    TRIANGULAR = "triangular"


@dataclass
class ParameterDistribution:
    """参数分布定义"""
    name: str
    parameter_type: ParameterType
    distribution_type: DistributionType
    base_value: float
    parameters: Dict[str, float] = field(default_factory=dict)
    bounds: Optional[Tuple[float, float]] = None
    
    def sample(self, size: int = 1) -> np.ndarray:
        """从分布中采样"""
        if self.distribution_type == DistributionType.UNIFORM:
            low = self.parameters.get('low', self.base_value * 0.8)
            high = self.parameters.get('high', self.base_value * 1.2)
            samples = np.random.uniform(low, high, size)
        
        elif self.distribution_type == DistributionType.NORMAL:
            mean = self.parameters.get('mean', self.base_value)
            std = self.parameters.get('std', self.base_value * 0.1)
            samples = np.random.normal(mean, std, size)
        
        elif self.distribution_type == DistributionType.LOGNORMAL:
            mu = self.parameters.get('mu', np.log(self.base_value))
            sigma = self.parameters.get('sigma', 0.1)
            samples = np.random.lognormal(mu, sigma, size)
        
        elif self.distribution_type == DistributionType.BETA:
            alpha = self.parameters.get('alpha', 2.0)
            beta = self.parameters.get('beta', 2.0)
            scale = self.parameters.get('scale', self.base_value)
            samples = np.random.beta(alpha, beta, size) * scale
        
        elif self.distribution_type == DistributionType.GAMMA:
            shape = self.parameters.get('shape', 2.0)
            scale = self.parameters.get('scale', self.base_value / shape)
            samples = np.random.gamma(shape, scale, size)
        
        elif self.distribution_type == DistributionType.TRIANGULAR:
            left = self.parameters.get('left', self.base_value * 0.8)
            mode = self.parameters.get('mode', self.base_value)
            right = self.parameters.get('right', self.base_value * 1.2)
            samples = np.random.triangular(left, mode, right, size)
        
        else:
            raise ValueError(f"不支持的分布类型: {self.distribution_type}")
        
        # 应用边界约束
        if self.bounds:
            lower, upper = self.bounds
            samples = np.clip(samples, lower, upper)
        
        # 应用参数类型约束
        if self.parameter_type == ParameterType.PROBABILITY:
            samples = np.clip(samples, 0.0, 1.0)
        elif self.parameter_type in [ParameterType.COST, ParameterType.DURATION]:
            samples = np.maximum(samples, 0.0)
        
        return samples


@dataclass
class SensitivityResult:
    """敏感性分析结果"""
    parameter_name: str
    parameter_values: List[float]
    outcome_values: List[float]
    base_outcome: float
    sensitivity_coefficient: float
    elasticity: float
    
    def get_tornado_data(self) -> Dict:
        """获取龙卷风图数据"""
        min_outcome = min(self.outcome_values)
        max_outcome = max(self.outcome_values)
        range_outcome = max_outcome - min_outcome
        
        return {
            'parameter': self.parameter_name,
            'low_value': min(self.parameter_values),
            'high_value': max(self.parameter_values),
            'low_outcome': min_outcome,
            'high_outcome': max_outcome,
            'range': range_outcome,
            'base_outcome': self.base_outcome
        }


class SensitivityAnalyzer:
    """敏感性分析器类"""
    
    def __init__(self, model_function: Callable):
        """
        初始化敏感性分析器
        
        Args:
            model_function: 模型计算函数，接受参数字典，返回结果
        """
        self.model_function = model_function
        self.parameter_distributions: Dict[str, ParameterDistribution] = {}
        self.base_parameters: Dict[str, float] = {}
        
    def add_parameter(
        self,
        name: str,
        base_value: float,
        parameter_type: ParameterType = ParameterType.COST,
        distribution_type: Optional[DistributionType] = None,
        distribution_params: Optional[Dict] = None,
        bounds: Optional[Tuple[float, float]] = None
    ) -> None:
        """
        添加敏感性分析参数

        Args:
            name: 参数名称
            base_value: 基础值
            parameter_type: 参数类型
            distribution_type: 分布类型（可选，如果不提供则只添加基础参数）
            distribution_params: 分布参数
            bounds: 参数边界
        """
        self.base_parameters[name] = base_value

        # 只有提供了分布类型才创建分布
        if distribution_type is not None:
            if distribution_params is None:
                distribution_params = {}

            self.parameter_distributions[name] = ParameterDistribution(
                name=name,
                parameter_type=parameter_type,
                distribution_type=distribution_type,
                base_value=base_value,
                parameters=distribution_params,
                bounds=bounds
            )
            logger.info(f"添加敏感性分析参数（含分布）: {name} = {base_value}")
        else:
            logger.info(f"添加敏感性分析参数（仅基础值）: {name} = {base_value}")
    
    def one_way_sensitivity_analysis(
        self,
        parameter_name: str,
        variation_range: Tuple[float, float] = (-0.2, 0.2),
        steps: int = 10,
        relative: bool = True
    ) -> SensitivityResult:
        """
        单因素敏感性分析
        
        Args:
            parameter_name: 参数名称
            variation_range: 变化范围（相对或绝对）
            steps: 分析步数
            relative: 是否为相对变化
            
        Returns:
            敏感性分析结果
        """
        if parameter_name not in self.base_parameters:
            raise ValueError(f"未定义的参数: {parameter_name}")
        
        base_value = self.base_parameters[parameter_name]
        
        # 生成参数变化范围
        if relative:
            min_mult, max_mult = variation_range
            parameter_values = [
                base_value * (1 + min_mult + (max_mult - min_mult) * i / (steps - 1))
                for i in range(steps)
            ]
        else:
            min_val, max_val = variation_range
            parameter_values = [
                min_val + (max_val - min_val) * i / (steps - 1)
                for i in range(steps)
            ]
        
        # 计算基础结果
        base_outcome = self.model_function(self.base_parameters)
        
        # 计算每个参数值对应的结果
        outcome_values = []
        for param_value in parameter_values:
            # 创建修改后的参数字典
            modified_params = self.base_parameters.copy()
            modified_params[parameter_name] = param_value
            
            # 计算结果
            outcome = self.model_function(modified_params)
            outcome_values.append(outcome)
        
        # 计算敏感性系数和弹性
        sensitivity_coefficient = self._calculate_sensitivity_coefficient(
            parameter_values, outcome_values, base_value, base_outcome
        )
        
        elasticity = self._calculate_elasticity(
            parameter_values, outcome_values, base_value, base_outcome
        )
        
        return SensitivityResult(
            parameter_name=parameter_name,
            parameter_values=parameter_values,
            outcome_values=outcome_values,
            base_outcome=base_outcome,
            sensitivity_coefficient=sensitivity_coefficient,
            elasticity=elasticity
        )
    
    def _calculate_sensitivity_coefficient(
        self,
        param_values: List[float],
        outcome_values: List[float],
        base_param: float,
        base_outcome: float
    ) -> float:
        """计算敏感性系数"""
        if len(param_values) < 2:
            return 0.0
        
        # 使用线性回归计算斜率
        param_array = np.array(param_values)
        outcome_array = np.array(outcome_values)
        
        # 计算斜率 (dY/dX)
        coeff = np.polyfit(param_array, outcome_array, 1)[0]
        return coeff
    
    def _calculate_elasticity(
        self,
        param_values: List[float],
        outcome_values: List[float],
        base_param: float,
        base_outcome: float
    ) -> float:
        """计算弹性系数"""
        if base_param == 0 or base_outcome == 0:
            return 0.0
        
        # 找到最接近基础值的点
        base_idx = min(range(len(param_values)), 
                      key=lambda i: abs(param_values[i] - base_param))
        
        if base_idx == 0:
            next_idx = 1
        elif base_idx == len(param_values) - 1:
            next_idx = base_idx - 1
        else:
            # 选择变化较大的方向
            next_idx = base_idx + 1
        
        # 计算弹性 = (ΔY/Y) / (ΔX/X)
        # 添加除零保护
        if param_values[base_idx] == 0 or outcome_values[base_idx] == 0:
            return 0.0

        delta_param = (param_values[next_idx] - param_values[base_idx]) / param_values[base_idx]
        delta_outcome = (outcome_values[next_idx] - outcome_values[base_idx]) / outcome_values[base_idx]

        if abs(delta_param) < 1e-10:  # 使用更严格的阈值
            return 0.0

        elasticity = delta_outcome / delta_param
        return elasticity

    def multi_way_sensitivity_analysis(
        self,
        parameter_names: List[str],
        variation_ranges: Optional[Dict[str, Tuple[float, float]]] = None,
        steps: int = 5
    ) -> Dict:
        """
        多因素敏感性分析

        Args:
            parameter_names: 参数名称列表
            variation_ranges: 各参数的变化范围
            steps: 每个参数的分析步数

        Returns:
            多因素敏感性分析结果
        """
        if variation_ranges is None:
            variation_ranges = {name: (-0.2, 0.2) for name in parameter_names}

        # 生成参数组合
        param_combinations = []
        param_grids = {}

        for param_name in parameter_names:
            if param_name not in self.base_parameters:
                raise ValueError(f"未定义的参数: {param_name}")

            base_value = self.base_parameters[param_name]
            min_mult, max_mult = variation_ranges.get(param_name, (-0.2, 0.2))

            param_values = [
                base_value * (1 + min_mult + (max_mult - min_mult) * i / (steps - 1))
                for i in range(steps)
            ]
            param_grids[param_name] = param_values

        # 生成所有参数组合
        import itertools
        combinations = list(itertools.product(*param_grids.values()))

        # 计算每个组合的结果
        results = []
        for combination in combinations:
            modified_params = self.base_parameters.copy()
            param_dict = dict(zip(parameter_names, combination))
            modified_params.update(param_dict)

            outcome = self.model_function(modified_params)

            result_entry = param_dict.copy()
            result_entry['outcome'] = outcome
            results.append(result_entry)

        # 计算基础结果
        base_outcome = self.model_function(self.base_parameters)

        return {
            'parameter_names': parameter_names,
            'parameter_grids': param_grids,
            'results': results,
            'base_outcome': base_outcome,
            'base_parameters': {name: self.base_parameters[name] for name in parameter_names}
        }

    def monte_carlo_simulation(
        self,
        parameter_names: Optional[List[str]] = None,
        n_simulations: int = 1000,
        confidence_level: float = 0.95,
        seed: Optional[int] = None
    ) -> Dict:
        """
        蒙特卡洛敏感性分析

        Args:
            parameter_names: 参数名称列表（None表示使用所有参数）
            n_simulations: 模拟次数
            confidence_level: 置信水平
            seed: 随机种子

        Returns:
            蒙特卡洛分析结果
        """
        if seed is not None:
            np.random.seed(seed)

        if parameter_names is None:
            parameter_names = list(self.parameter_distributions.keys())

        # 验证参数
        for param_name in parameter_names:
            if param_name not in self.parameter_distributions:
                raise ValueError(f"未定义参数分布: {param_name}")

        # 生成参数样本
        parameter_samples = {}
        for param_name in parameter_names:
            distribution = self.parameter_distributions[param_name]
            samples = distribution.sample(n_simulations)
            parameter_samples[param_name] = samples

        # 运行模拟
        outcomes = []
        simulation_data = []

        for i in range(n_simulations):
            # 构建参数字典
            sim_params = self.base_parameters.copy()
            sim_entry = {'simulation': i}

            for param_name in parameter_names:
                param_value = parameter_samples[param_name][i]
                sim_params[param_name] = param_value
                sim_entry[param_name] = param_value

            # 计算结果
            outcome = self.model_function(sim_params)
            outcomes.append(outcome)
            sim_entry['outcome'] = outcome
            simulation_data.append(sim_entry)

        # 统计分析
        outcomes_array = np.array(outcomes)

        # 计算置信区间
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100

        confidence_interval = (
            np.percentile(outcomes_array, lower_percentile),
            np.percentile(outcomes_array, upper_percentile)
        )

        # 计算相关性分析
        correlations = {}
        for param_name in parameter_names:
            param_values = parameter_samples[param_name]
            correlation = np.corrcoef(param_values, outcomes_array)[0, 1]
            correlations[param_name] = correlation

        # 计算基础结果
        base_outcome = self.model_function(self.base_parameters)

        return {
            'n_simulations': n_simulations,
            'parameter_names': parameter_names,
            'outcomes': outcomes,
            'simulation_data': simulation_data,
            'statistics': {
                'mean': np.mean(outcomes_array),
                'std': np.std(outcomes_array),
                'min': np.min(outcomes_array),
                'max': np.max(outcomes_array),
                'median': np.median(outcomes_array),
                'confidence_interval': confidence_interval,
                'confidence_level': confidence_level
            },
            'correlations': correlations,
            'base_outcome': base_outcome
        }

    def tornado_analysis(
        self,
        parameter_names: Optional[List[str]] = None,
        variation_range: Tuple[float, float] = (-0.2, 0.2)
    ) -> Dict:
        """
        龙卷风图分析

        Args:
            parameter_names: 参数名称列表
            variation_range: 变化范围

        Returns:
            龙卷风图分析结果
        """
        if parameter_names is None:
            parameter_names = list(self.base_parameters.keys())

        tornado_data = []

        for param_name in parameter_names:
            # 进行单因素敏感性分析
            sensitivity_result = self.one_way_sensitivity_analysis(
                param_name, variation_range, steps=2
            )

            # 获取龙卷风图数据
            tornado_entry = sensitivity_result.get_tornado_data()
            tornado_entry['sensitivity_coefficient'] = sensitivity_result.sensitivity_coefficient
            tornado_entry['elasticity'] = sensitivity_result.elasticity

            tornado_data.append(tornado_entry)

        # 按影响范围排序
        tornado_data.sort(key=lambda x: abs(x['range']), reverse=True)

        return {
            'tornado_data': tornado_data,
            'base_outcome': tornado_data[0]['base_outcome'] if tornado_data else 0,
            'variation_range': variation_range
        }

    def generate_sensitivity_report(
        self,
        output_file: Optional[str] = None,
        include_monte_carlo: bool = True,
        n_simulations: int = 1000
    ) -> Dict:
        """
        生成敏感性分析报告

        Args:
            output_file: 输出文件路径
            include_monte_carlo: 是否包含蒙特卡洛分析
            n_simulations: 蒙特卡洛模拟次数

        Returns:
            完整的敏感性分析报告
        """
        report = {
            'analysis_date': pd.Timestamp.now().isoformat(),
            'base_parameters': self.base_parameters.copy(),
            'base_outcome': self.model_function(self.base_parameters)
        }

        # 单因素敏感性分析
        one_way_results = {}
        for param_name in self.base_parameters.keys():
            try:
                result = self.one_way_sensitivity_analysis(param_name)
                one_way_results[param_name] = {
                    'sensitivity_coefficient': result.sensitivity_coefficient,
                    'elasticity': result.elasticity,
                    'parameter_range': (min(result.parameter_values), max(result.parameter_values)),
                    'outcome_range': (min(result.outcome_values), max(result.outcome_values))
                }
            except Exception as e:
                logger.warning(f"单因素分析失败 {param_name}: {e}")
                continue

        report['one_way_analysis'] = one_way_results

        # 龙卷风图分析
        try:
            tornado_result = self.tornado_analysis()
            report['tornado_analysis'] = tornado_result
        except Exception as e:
            logger.warning(f"龙卷风图分析失败: {e}")

        # 蒙特卡洛分析
        if include_monte_carlo and self.parameter_distributions:
            try:
                mc_result = self.monte_carlo_simulation(n_simulations=n_simulations)
                report['monte_carlo_analysis'] = {
                    'statistics': mc_result['statistics'],
                    'correlations': mc_result['correlations'],
                    'n_simulations': mc_result['n_simulations']
                }
            except Exception as e:
                logger.warning(f"蒙特卡洛分析失败: {e}")

        # 保存报告
        if output_file:
            try:
                output_path = Path(output_file)
                if output_path.suffix.lower() == '.json':
                    with open(output_path, 'w', encoding='utf-8') as f:
                        json.dump(report, f, indent=2, ensure_ascii=False)
                elif output_path.suffix.lower() in ['.yaml', '.yml']:
                    with open(output_path, 'w', encoding='utf-8') as f:
                        yaml.dump(report, f, default_flow_style=False, allow_unicode=True)

                logger.info(f"敏感性分析报告已保存: {output_file}")
            except Exception as e:
                logger.error(f"保存报告失败: {e}")

        return report

    def clear_parameters(self) -> None:
        """清除所有参数"""
        self.parameter_distributions.clear()
        self.base_parameters.clear()
        logger.info("已清除所有敏感性分析参数")

    def get_parameter_summary(self) -> Dict:
        """获取参数摘要"""
        summary = {
            'total_parameters': len(self.base_parameters),
            'parameters_with_distributions': len(self.parameter_distributions),
            'parameter_details': {}
        }

        for name, value in self.base_parameters.items():
            detail = {
                'base_value': value,
                'has_distribution': name in self.parameter_distributions
            }

            if name in self.parameter_distributions:
                dist = self.parameter_distributions[name]
                detail.update({
                    'parameter_type': dist.parameter_type.value,
                    'distribution_type': dist.distribution_type.value,
                    'bounds': dist.bounds
                })

            summary['parameter_details'][name] = detail

        return summary
