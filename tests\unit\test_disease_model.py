"""
测试疾病进展引擎

测试DiseaseProgressionEngine类的功能，包括双通路集成、状态转换和统计跟踪。
"""

import pytest
from unittest.mock import Mock, patch

from src.core.enums import DiseaseState, PathwayType
from src.modules.disease.disease_model import (
    DiseaseProgressionEngine,
    ProgressionResult,
    DiseaseProgressionStatistics
)
from src.modules.disease.risk_factors import RiskFactorProfile


class MockIndividual:
    """模拟个体类"""
    
    def __init__(self, individual_id: str = "test-001", 
                 disease_state: DiseaseState = DiseaseState.NORMAL,
                 pathway_type: PathwayType = PathwayType.ADENOMA_CARCINOMA,
                 gender: str = "male"):
        self.individual_id = individual_id
        self.disease_state = disease_state
        self.pathway_type = pathway_type
        self.gender = gender


class TestDiseaseProgressionStatistics:
    """测试疾病进展统计信息"""
    
    def test_initial_statistics(self):
        """测试初始统计信息"""
        stats = DiseaseProgressionStatistics()
        
        assert stats.total_progressions == 0
        assert stats.adenoma_pathway_progressions == 0
        assert stats.serrated_pathway_progressions == 0
        assert stats.pathway_assignments == {}
        assert stats.state_transitions == {}
    
    def test_add_progression(self):
        """测试添加进展记录"""
        stats = DiseaseProgressionStatistics()
        
        # 添加腺瘤通路进展
        stats.add_progression(
            PathwayType.ADENOMA_CARCINOMA,
            "normal",
            "low_risk_adenoma"
        )
        
        assert stats.total_progressions == 1
        assert stats.adenoma_pathway_progressions == 1
        assert stats.serrated_pathway_progressions == 0
        assert stats.pathway_assignments["adenoma_carcinoma"] == 1
        assert stats.state_transitions["normal_to_low_risk_adenoma"] == 1
        
        # 添加锯齿状通路进展
        stats.add_progression(
            PathwayType.SERRATED_ADENOMA,
            "normal",
            "small_serrated"
        )
        
        assert stats.total_progressions == 2
        assert stats.adenoma_pathway_progressions == 1
        assert stats.serrated_pathway_progressions == 1
        assert stats.pathway_assignments["serrated_adenoma"] == 1
        assert stats.state_transitions["normal_to_small_serrated"] == 1
    
    def test_pathway_distribution(self):
        """测试通路分布计算"""
        stats = DiseaseProgressionStatistics()
        
        # 空统计
        distribution = stats.get_pathway_distribution()
        assert distribution == {}
        
        # 添加一些进展
        for _ in range(8):
            stats.add_progression(PathwayType.ADENOMA_CARCINOMA, "normal", "low_risk_adenoma")
        for _ in range(2):
            stats.add_progression(PathwayType.SERRATED_ADENOMA, "normal", "small_serrated")
        
        distribution = stats.get_pathway_distribution()
        assert distribution["adenoma_carcinoma"] == 80.0
        assert distribution["serrated_adenoma"] == 20.0


class TestDiseaseProgressionEngine:
    """测试疾病进展引擎"""
    
    def test_initialization(self):
        """测试引擎初始化"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        assert engine.random_seed == 42
        assert engine.adenoma_model is not None
        assert engine.serrated_model is not None
        assert engine.pathway_selector is not None
        assert isinstance(engine.statistics, DiseaseProgressionStatistics)
        assert len(engine.pathway_constraints) == 2
    
    def test_pathway_assignment(self):
        """测试通路分配"""
        engine = DiseaseProgressionEngine(random_seed=42)
        individual = MockIndividual()
        
        pathway = engine.assign_pathway(individual)
        
        assert pathway in [PathwayType.ADENOMA_CARCINOMA, PathwayType.SERRATED_ADENOMA]
    
    def test_pathway_state_compatibility_validation(self):
        """测试通路状态兼容性验证"""
        engine = DiseaseProgressionEngine()
        
        # 腺瘤通路兼容性
        assert engine._validate_pathway_state_compatibility(
            PathwayType.ADENOMA_CARCINOMA, 
            DiseaseState.LOW_RISK_ADENOMA
        )
        assert not engine._validate_pathway_state_compatibility(
            PathwayType.ADENOMA_CARCINOMA, 
            DiseaseState.SMALL_SERRATED
        )
        
        # 锯齿状通路兼容性
        assert engine._validate_pathway_state_compatibility(
            PathwayType.SERRATED_ADENOMA, 
            DiseaseState.SMALL_SERRATED
        )
        assert not engine._validate_pathway_state_compatibility(
            PathwayType.SERRATED_ADENOMA, 
            DiseaseState.LOW_RISK_ADENOMA
        )
        
        # 共同状态兼容性
        assert engine._validate_pathway_state_compatibility(
            PathwayType.ADENOMA_CARCINOMA, 
            DiseaseState.NORMAL
        )
        assert engine._validate_pathway_state_compatibility(
            PathwayType.SERRATED_ADENOMA, 
            DiseaseState.NORMAL
        )
    
    def test_serrated_pathway_progression(self):
        """测试锯齿状通路进展"""
        engine = DiseaseProgressionEngine(random_seed=42)
        individual = MockIndividual(
            disease_state=DiseaseState.NORMAL,
            pathway_type=PathwayType.SERRATED_ADENOMA
        )
        
        result = engine.progress_individual(individual, time_step=1.0)
        
        assert isinstance(result, ProgressionResult)
        assert result.pathway_type == PathwayType.SERRATED_ADENOMA
        assert result.new_state in [DiseaseState.NORMAL, DiseaseState.SMALL_SERRATED]
        assert isinstance(result.progression_time, float)
        assert result.progression_time >= 0
        assert "model_used" in result.metadata
        assert result.metadata["model_used"] == "serrated"
    
    def test_adenoma_pathway_progression(self):
        """测试腺瘤通路进展"""
        engine = DiseaseProgressionEngine(random_seed=42)
        individual = MockIndividual(
            disease_state=DiseaseState.NORMAL,
            pathway_type=PathwayType.ADENOMA_CARCINOMA
        )
        
        result = engine.progress_individual(individual, time_step=1.0)
        
        assert isinstance(result, ProgressionResult)
        assert result.pathway_type == PathwayType.ADENOMA_CARCINOMA
        assert result.new_state in [DiseaseState.NORMAL, DiseaseState.LOW_RISK_ADENOMA]
        assert isinstance(result.progression_time, float)
        assert result.progression_time >= 0
        assert "model_used" in result.metadata
        assert result.metadata["model_used"] == "adenoma"
    
    def test_incompatible_pathway_state_error(self):
        """测试不兼容通路状态的错误处理"""
        engine = DiseaseProgressionEngine()
        individual = MockIndividual(
            disease_state=DiseaseState.SMALL_SERRATED,  # 锯齿状状态
            pathway_type=PathwayType.ADENOMA_CARCINOMA   # 但分配到腺瘤通路
        )
        
        with pytest.raises(ValueError, match="状态.*与通路.*不兼容"):
            engine.progress_individual(individual)
    
    def test_unknown_pathway_error(self):
        """测试未知通路类型的错误处理"""
        engine = DiseaseProgressionEngine()
        individual = MockIndividual()

        # 创建一个模拟的无效通路类型
        class InvalidPathway:
            value = "unknown_pathway"

        individual.pathway_type = InvalidPathway()

        with pytest.raises(ValueError, match="未知的通路类型"):
            engine.progress_individual(individual)
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        # 初始统计
        stats = engine.get_statistics()
        assert stats.total_progressions == 0
        
        # 进行一些进展
        individual1 = MockIndividual(
            disease_state=DiseaseState.NORMAL,
            pathway_type=PathwayType.SERRATED_ADENOMA
        )
        result1 = engine.progress_individual(individual1)
        
        individual2 = MockIndividual(
            disease_state=DiseaseState.NORMAL,
            pathway_type=PathwayType.ADENOMA_CARCINOMA
        )
        result2 = engine.progress_individual(individual2)
        
        # 检查统计更新
        stats = engine.get_statistics()
        expected_progressions = (1 if result1.transition_occurred else 0) + (1 if result2.transition_occurred else 0)
        assert stats.total_progressions == expected_progressions
    
    def test_statistics_reset(self):
        """测试统计信息重置"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        # 进行一些进展
        individual = MockIndividual(
            disease_state=DiseaseState.NORMAL,
            pathway_type=PathwayType.SERRATED_ADENOMA
        )
        engine.progress_individual(individual)
        
        # 重置统计
        engine.reset_statistics()
        
        stats = engine.get_statistics()
        assert stats.total_progressions == 0
        assert stats.adenoma_pathway_progressions == 0
        assert stats.serrated_pathway_progressions == 0
    
    def test_pathway_integrity_validation(self):
        """测试通路完整性验证"""
        engine = DiseaseProgressionEngine()
        
        integrity = engine.validate_pathway_integrity()
        
        assert "serrated_model_valid" in integrity
        assert "pathway_selector_valid" in integrity
        assert "pathway_constraints_valid" in integrity
        assert isinstance(integrity["serrated_model_valid"], bool)
        assert isinstance(integrity["pathway_selector_valid"], bool)
        assert integrity["pathway_constraints_valid"] is True
    
    def test_pathway_preferences(self):
        """测试通路偏好获取"""
        engine = DiseaseProgressionEngine()
        individual = MockIndividual(gender="female")
        
        preferences = engine.get_pathway_preferences(individual)
        
        assert "serrated_adenoma" in preferences
        assert "adenoma_carcinoma" in preferences
        assert isinstance(preferences["serrated_adenoma"], float)
        assert isinstance(preferences["adenoma_carcinoma"], float)
        assert abs(preferences["serrated_adenoma"] + preferences["adenoma_carcinoma"] - 1.0) < 1e-10
    
    def test_progression_time_calculation(self):
        """测试进展时间计算"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        # 相同状态应该返回0时间
        time = engine._calculate_progression_time(
            DiseaseState.NORMAL, 
            DiseaseState.NORMAL, 
            PathwayType.SERRATED_ADENOMA
        )
        assert time == 0.0
        
        # 不同状态应该返回正时间
        time = engine._calculate_progression_time(
            DiseaseState.NORMAL, 
            DiseaseState.SMALL_SERRATED, 
            PathwayType.SERRATED_ADENOMA
        )
        assert time > 0.0
    
    def test_batch_progression(self):
        """测试批量进展处理"""
        engine = DiseaseProgressionEngine(random_seed=42)
        
        # 创建多个个体
        individuals = [
            MockIndividual(f"individual-{i}", pathway_type=PathwayType.SERRATED_ADENOMA)
            for i in range(5)
        ]
        
        # 批量处理
        results = []
        for individual in individuals:
            result = engine.progress_individual(individual)
            results.append(result)
        
        assert len(results) == 5
        assert all(isinstance(result, ProgressionResult) for result in results)
        assert all(result.pathway_type == PathwayType.SERRATED_ADENOMA for result in results)
