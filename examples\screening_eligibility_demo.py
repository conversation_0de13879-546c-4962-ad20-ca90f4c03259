"""
筛查资格检查演示

展示筛查资格检查器的主要功能，包括：
1. 初筛资格检查（年龄、诊断性肠镜间隔、筛查间隔、依从性）
2. 诊断性肠镜筛查资格检查（初筛阳性、依从性）
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.individual import Individual
from src.core.enums import DiseaseState, Gender
from src.modules.screening import (
    ScreeningEligibilityChecker, EligibilityResult, EligibilityReason,
    ScreeningInterval, ScreeningToolType, ScreeningFrequency
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_individuals():
    """创建样本个体"""
    individuals = []
    
    # 1. 正常年龄个体（54岁）
    individual1 = Individual(
        birth_year=1970,
        gender=Gender.MALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    individuals.append(("正常年龄个体(54岁)", individual1))
    
    # 2. 年龄过小个体（45岁）
    individual2 = Individual(
        birth_year=1979,
        gender=Gender.FEMALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    individuals.append(("年龄过小个体(45岁)", individual2))
    
    # 3. 年龄过大个体（80岁）
    individual3 = Individual(
        birth_year=1944,
        gender=Gender.MALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    individuals.append(("年龄过大个体(80岁)", individual3))
    
    # 4. 有近期肠镜检查的个体
    individual4 = Individual(
        birth_year=1970,
        gender=Gender.FEMALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    # 添加5年前的诊断性肠镜记录
    individual4.diagnostic_history = [{
        'procedure_type': 'diagnostic_colonoscopy',
        'date': datetime.now() - timedelta(days=365 * 5),
        'findings': False
    }]
    individuals.append(("有近期肠镜检查个体", individual4))
    
    # 5. 有近期筛查的个体
    individual5 = Individual(
        birth_year=1970,
        gender=Gender.MALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    # 添加6个月前的FIT筛查记录
    individual5.screening_history = [{
        'tool_type': ScreeningToolType.FIT,
        'test_date': datetime.now() - timedelta(days=180),
        'result_type': 'negative'
    }]
    individuals.append(("有近期筛查个体", individual5))
    
    # 6. 有阳性筛查历史的个体（FIT）
    individual6 = Individual(
        birth_year=1970,
        gender=Gender.FEMALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    # 添加1个月前的阳性FIT筛查记录
    individual6.screening_history = [{
        'tool_type': ScreeningToolType.FIT,
        'test_date': datetime.now() - timedelta(days=30),
        'result_type': 'positive'
    }]
    individuals.append(("有阳性FIT筛查历史个体", individual6))

    # 7. 有阳性肠镜筛查历史的个体
    individual7 = Individual(
        birth_year=1970,
        gender=Gender.MALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    # 添加1个月前的阳性肠镜筛查记录
    individual7.screening_history = [{
        'tool_type': ScreeningToolType.COLONOSCOPY,
        'test_date': datetime.now() - timedelta(days=30),
        'result_type': 'positive'
    }]
    individuals.append(("有阳性肠镜筛查历史个体", individual7))
    
    return individuals


def create_screening_intervals():
    """创建筛查间隔配置"""
    intervals = []
    
    # FIT年度筛查
    fit_interval = ScreeningInterval(
        tool_type=ScreeningToolType.FIT,
        start_age=50,
        end_age=75,
        frequency=ScreeningFrequency.ANNUAL,
        sequence_order=1
    )
    intervals.append(("FIT年度筛查", fit_interval))
    
    # 肠镜10年筛查
    colonoscopy_interval = ScreeningInterval(
        tool_type=ScreeningToolType.COLONOSCOPY,
        start_age=50,
        end_age=75,
        frequency=ScreeningFrequency.CUSTOM,
        custom_frequency_years=10.0,
        sequence_order=2
    )
    intervals.append(("肠镜10年筛查", colonoscopy_interval))
    
    return intervals


def demonstrate_primary_screening_eligibility():
    """演示初筛资格检查"""
    print("=" * 60)
    print("初筛资格检查演示")
    print("=" * 60)
    
    # 创建资格检查器
    eligibility_checker = ScreeningEligibilityChecker()
    
    # 创建样本数据
    individuals = create_sample_individuals()
    intervals = create_screening_intervals()
    
    current_time = datetime.now()
    
    for interval_name, interval in intervals:
        print(f"\n{interval_name} 资格检查:")
        print("-" * 40)
        
        for individual_name, individual in individuals:
            result = eligibility_checker.check_primary_screening_eligibility(
                individual, interval, current_time
            )
            
            status = "✓ 符合" if result.is_eligible else "✗ 不符合"
            reason = result.reason.value
            
            print(f"  {individual_name}: {status} ({reason})")
            
            if not result.is_eligible and result.next_eligible_date:
                days_to_wait = (result.next_eligible_date - current_time).days
                print(f"    下次符合日期: {result.next_eligible_date.strftime('%Y-%m-%d')} ({days_to_wait}天后)")


def demonstrate_diagnostic_colonoscopy_eligibility():
    """演示诊断性肠镜筛查资格检查"""
    print("\n" + "=" * 60)
    print("诊断性肠镜筛查资格检查演示")
    print("=" * 60)
    
    # 创建资格检查器
    eligibility_checker = ScreeningEligibilityChecker()
    
    # 创建样本数据
    individuals = create_sample_individuals()
    current_time = datetime.now()
    
    for individual_name, individual in individuals:
        result = eligibility_checker.check_diagnostic_colonoscopy_eligibility(
            individual, current_time
        )
        
        status = "✓ 符合" if result.is_eligible else "✗ 不符合"
        reason = result.reason.value
        
        print(f"{individual_name}: {status} ({reason})")
        
        # 显示详细信息
        if 'has_positive_screening' in result.details:
            has_positive = result.details['has_positive_screening']
            print(f"  有阳性筛查: {'是' if has_positive else '否'}")
        
        if 'diagnostic_compliance_probability' in result.details:
            compliance_prob = result.details['diagnostic_compliance_probability']
            print(f"  诊断依从性概率: {compliance_prob:.2%}")


def demonstrate_colonoscopy_special_logic():
    """演示肠镜筛查的特殊逻辑"""
    print("\n" + "=" * 60)
    print("肠镜筛查特殊逻辑演示")
    print("=" * 60)
    print("重要：当初筛工具为肠镜时，不需要诊断性肠镜检查")
    print("因为肠镜本身就是诊断性的。")
    print()

    # 创建资格检查器
    eligibility_checker = ScreeningEligibilityChecker()
    current_time = datetime.now()

    # 创建有阳性FIT筛查的个体
    individual_fit = Individual(
        birth_year=1970,
        gender=Gender.MALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    individual_fit.screening_history = [{
        'tool_type': ScreeningToolType.FIT,
        'test_date': current_time - timedelta(days=30),
        'result_type': 'positive'
    }]

    # 创建有阳性肠镜筛查的个体
    individual_colonoscopy = Individual(
        birth_year=1970,
        gender=Gender.FEMALE,
        initial_disease_state=DiseaseState.NORMAL
    )
    individual_colonoscopy.screening_history = [{
        'tool_type': ScreeningToolType.COLONOSCOPY,
        'test_date': current_time - timedelta(days=30),
        'result_type': 'positive'
    }]

    print("1. FIT阳性筛查后的诊断性肠镜资格:")
    result_fit = eligibility_checker.check_diagnostic_colonoscopy_eligibility(
        individual_fit, current_time
    )
    status_fit = "✓ 需要" if result_fit.is_eligible else "✗ 不需要"
    print(f"   {status_fit} ({result_fit.reason.value})")
    if 'diagnostic_compliance_probability' in result_fit.details:
        print(f"   诊断依从性概率: {result_fit.details['diagnostic_compliance_probability']:.2%}")

    print("\n2. 肠镜阳性筛查后的诊断性肠镜资格:")
    result_colonoscopy = eligibility_checker.check_diagnostic_colonoscopy_eligibility(
        individual_colonoscopy, current_time
    )
    status_colonoscopy = "✓ 需要" if result_colonoscopy.is_eligible else "✗ 不需要"
    print(f"   {status_colonoscopy} ({result_colonoscopy.reason.value})")
    print("   说明: 肠镜筛查本身就是诊断性的，无需额外诊断")

    print("\n3. 对比说明:")
    print("   - FIT阳性 → 需要诊断性肠镜确认")
    print("   - 肠镜阳性 → 已经是诊断结果，直接进入治疗流程")


def demonstrate_eligibility_details():
    """演示资格检查详细信息"""
    print("\n" + "=" * 60)
    print("资格检查详细信息演示")
    print("=" * 60)

    # 创建资格检查器
    eligibility_checker = ScreeningEligibilityChecker()

    # 使用正常年龄个体
    individual = Individual(
        birth_year=1970,
        gender=Gender.MALE,
        initial_disease_state=DiseaseState.NORMAL
    )

    # FIT筛查间隔
    fit_interval = ScreeningInterval(
        tool_type=ScreeningToolType.FIT,
        start_age=50,
        end_age=75,
        frequency=ScreeningFrequency.ANNUAL,
        sequence_order=1
    )

    current_time = datetime.now()

    result = eligibility_checker.check_primary_screening_eligibility(
        individual, fit_interval, current_time
    )

    print("详细检查结果:")
    print(f"  是否符合: {'是' if result.is_eligible else '否'}")
    print(f"  判断原因: {result.reason.value}")
    print(f"  详细信息:")
    for key, value in result.details.items():
        print(f"    {key}: {value}")


def main():
    """主函数"""
    print("筛查资格检查器演示")
    print("=" * 60)
    print("本演示展示筛查资格检查的主要功能：")
    print("1. 初筛资格检查（年龄、诊断性肠镜间隔、筛查间隔、依从性）")
    print("2. 诊断性肠镜筛查资格检查（初筛阳性、依从性）")
    print()
    
    try:
        # 演示初筛资格检查
        demonstrate_primary_screening_eligibility()
        
        # 演示诊断性肠镜筛查资格检查
        demonstrate_diagnostic_colonoscopy_eligibility()

        # 演示肠镜筛查的特殊逻辑
        demonstrate_colonoscopy_special_logic()

        # 演示详细信息
        demonstrate_eligibility_details()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
