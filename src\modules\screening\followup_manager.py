"""
后续诊断流程管理器

实现筛查阳性结果的后续诊断流程触发和管理，包括诊断性肠镜安排、
等待时间建模和诊断结果处理。
"""

import logging
from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta
from enum import Enum

from src.core.individual import Individual
from src.core.enums import DiseaseState
from .result_processor import ScreeningResult, FollowupAction

logger = logging.getLogger(__name__)


class DiagnosticProcedure(Enum):
    """诊断程序类型枚举"""
    COLONOSCOPY = "diagnostic_colonoscopy"
    SIGMOIDOSCOPY = "diagnostic_sigmoidoscopy"
    CT_COLONOGRAPHY = "diagnostic_ct_colonography"
    BIOPSY = "biopsy"
    SPECIALIST_CONSULTATION = "specialist_consultation"


class AppointmentStatus(Enum):
    """预约状态枚举"""
    SCHEDULED = "scheduled"
    CONFIRMED = "confirmed"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"
    RESCHEDULED = "rescheduled"


@dataclass
class DiagnosticAppointment:
    """诊断预约数据结构"""
    individual_id: str
    procedure_type: DiagnosticProcedure
    scheduled_date: datetime
    referring_screening: ScreeningResult
    status: AppointmentStatus = AppointmentStatus.SCHEDULED
    actual_date: Optional[datetime] = None
    waiting_time_days: int = 0
    priority_level: int = 1  # 1-5, 5为最高优先级
    facility_id: Optional[str] = None
    provider_id: Optional[str] = None
    cost: float = 0.0
    additional_data: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """验证诊断预约数据"""
        if self.waiting_time_days < 0:
            raise ValueError("等待时间不能为负数")
        if not 1 <= self.priority_level <= 5:
            raise ValueError("优先级必须在1-5之间")
        if self.cost < 0:
            raise ValueError("成本不能为负数")


@dataclass
class DiagnosticResult:
    """诊断结果数据结构"""
    appointment: DiagnosticAppointment
    findings: List[str]
    confirmed_disease_state: Optional[DiseaseState] = None
    biopsy_required: bool = False
    treatment_recommended: bool = False
    followup_interval_months: int = 12
    quality_score: float = 1.0
    complications: List[str] = field(default_factory=list)
    additional_data: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """验证诊断结果数据"""
        if not 0 <= self.quality_score <= 1:
            raise ValueError("质量分数必须在0-1之间")
        if self.followup_interval_months < 0:
            raise ValueError("随访间隔不能为负数")


class FollowupManager:
    """后续诊断流程管理器"""
    
    def __init__(self, healthcare_system_config: Dict[str, Any]):
        """
        初始化后续诊断管理器
        
        Args:
            healthcare_system_config: 医疗系统配置
        """
        self.config = healthcare_system_config
        self.diagnostic_capacity = healthcare_system_config.get('diagnostic_capacity', {})
        self.waiting_times = healthcare_system_config.get('waiting_times', {})
        self.facility_locations = healthcare_system_config.get('facility_locations', {})
        
        # 预约队列
        self.appointment_queue: List[DiagnosticAppointment] = []
        self.completed_appointments: List[DiagnosticAppointment] = []
        
        logger.info("初始化后续诊断流程管理器")

    def schedule_followup(
        self, 
        screening_result: ScreeningResult, 
        individual: Individual,
        requested_date: Optional[datetime] = None
    ) -> Optional[DiagnosticAppointment]:
        """
        安排后续诊断
        
        Args:
            screening_result: 筛查结果
            individual: 个体
            requested_date: 请求的预约日期
            
        Returns:
            诊断预约对象，如果无法安排则返回None
        """
        if screening_result.followup_action == FollowupAction.NONE:
            return None
            
        # 确定诊断程序类型
        procedure_type = self._determine_diagnostic_procedure(screening_result)
        if not procedure_type:
            return None
            
        # 计算优先级
        priority_level = self._calculate_priority_level(screening_result, individual)
        
        # 检查诊断容量
        if not self._check_diagnostic_capacity(procedure_type):
            logger.warning(f"诊断容量不足: {procedure_type.value}")
            return None
            
        # 计算等待时间
        waiting_time = self._calculate_waiting_time(procedure_type, priority_level)
        
        # 确定预约日期
        if requested_date:
            scheduled_date = max(requested_date, 
                               screening_result.test_date + timedelta(days=waiting_time))
        else:
            scheduled_date = screening_result.test_date + timedelta(days=waiting_time)
            
        # 创建预约
        appointment = DiagnosticAppointment(
            individual_id=individual.individual_id,
            procedure_type=procedure_type,
            scheduled_date=scheduled_date,
            referring_screening=screening_result,
            waiting_time_days=waiting_time,
            priority_level=priority_level,
            cost=self._calculate_diagnostic_cost(procedure_type)
        )
        
        # 添加到预约队列
        self.appointment_queue.append(appointment)
        
        logger.info(f"安排诊断预约: {individual.individual_id}, "
                   f"程序: {procedure_type.value}, 日期: {scheduled_date}")
        
        return appointment

    def process_diagnostic_result(
        self, 
        diagnostic_result: DiagnosticResult, 
        individual: Individual
    ) -> Dict[str, Any]:
        """
        处理诊断结果
        
        Args:
            diagnostic_result: 诊断结果
            individual: 个体
            
        Returns:
            处理结果信息
        """
        appointment = diagnostic_result.appointment
        
        # 更新预约状态
        appointment.status = AppointmentStatus.COMPLETED
        appointment.actual_date = datetime.now()
        
        # 移动到已完成列表
        if appointment in self.appointment_queue:
            self.appointment_queue.remove(appointment)
        self.completed_appointments.append(appointment)
        
        # 处理诊断发现
        actions_taken = []
        
        if diagnostic_result.findings:
            # 发现病变，更新个体状态
            if diagnostic_result.confirmed_disease_state:
                success = individual.transition_to_state(
                    diagnostic_result.confirmed_disease_state
                )
                if success:
                    actions_taken.append(f"更新疾病状态为: {diagnostic_result.confirmed_disease_state.value}")
                    
                    # 如果发现病变，模拟手术摘除
                    if diagnostic_result.confirmed_disease_state != DiseaseState.NORMAL:
                        self._simulate_lesion_removal(individual, diagnostic_result)
                        actions_taken.append("模拟病变摘除")
            
            # 触发治疗流程
            if diagnostic_result.treatment_recommended:
                treatment_info = self._initiate_treatment(individual, diagnostic_result)
                actions_taken.append(f"启动治疗: {treatment_info}")
        
        # 记录诊断历史
        self._add_diagnostic_history(individual, diagnostic_result)
        actions_taken.append("记录诊断历史")
        
        return {
            "appointment_id": id(appointment),
            "individual_id": individual.individual_id,
            "procedure_type": appointment.procedure_type.value,
            "findings": diagnostic_result.findings,
            "actions_taken": actions_taken,
            "followup_interval_months": diagnostic_result.followup_interval_months,
            "treatment_recommended": diagnostic_result.treatment_recommended
        }

    def _determine_diagnostic_procedure(
        self,
        screening_result: ScreeningResult
    ) -> Optional[DiagnosticProcedure]:
        """确定诊断程序类型"""
        # 检查初筛工具类型，如果已经是肠镜，则不需要额外的诊断性肠镜
        screening_tool_type = getattr(screening_result, 'tool_type', None)
        if screening_tool_type and hasattr(screening_tool_type, 'value'):
            tool_type_value = screening_tool_type.value
        else:
            tool_type_value = str(screening_tool_type) if screening_tool_type else ''

        # 如果初筛工具是肠镜，则不需要额外的诊断性肠镜
        if 'colonoscopy' in tool_type_value.lower():
            return None

        if screening_result.followup_action == FollowupAction.DIAGNOSTIC_COLONOSCOPY:
            return DiagnosticProcedure.COLONOSCOPY
        elif screening_result.followup_action == FollowupAction.SPECIALIST_REFERRAL:
            return DiagnosticProcedure.SPECIALIST_CONSULTATION
        elif screening_result.followup_action == FollowupAction.IMMEDIATE_TREATMENT:
            return DiagnosticProcedure.COLONOSCOPY  # 通常需要肠镜确认
        else:
            return None

    def _calculate_priority_level(
        self, 
        screening_result: ScreeningResult, 
        individual: Individual
    ) -> int:
        """计算优先级"""
        priority = 1  # 基础优先级
        
        # 根据筛查结果置信度调整
        if screening_result.confidence_score > 0.8:
            priority += 1
            
        # 根据个体年龄调整
        age = individual.get_current_age()
        if age > 70:
            priority += 1
            
        # 根据后续行动类型调整
        if screening_result.followup_action == FollowupAction.IMMEDIATE_TREATMENT:
            priority += 2
            
        return min(5, priority)

    def _check_diagnostic_capacity(self, procedure_type: DiagnosticProcedure) -> bool:
        """检查诊断容量"""
        capacity = self.diagnostic_capacity.get(procedure_type.value, 100)
        current_load = len([a for a in self.appointment_queue 
                           if a.procedure_type == procedure_type])
        
        return current_load < capacity

    def _calculate_waiting_time(
        self, 
        procedure_type: DiagnosticProcedure, 
        priority_level: int
    ) -> int:
        """计算等待时间（天）"""
        base_waiting_time = self.waiting_times.get(procedure_type.value, 14)
        
        # 根据优先级调整
        priority_multiplier = {
            1: 1.5,
            2: 1.2,
            3: 1.0,
            4: 0.8,
            5: 0.5
        }
        
        adjusted_time = base_waiting_time * priority_multiplier.get(priority_level, 1.0)
        
        return max(1, int(adjusted_time))

    def _calculate_diagnostic_cost(self, procedure_type: DiagnosticProcedure) -> float:
        """计算诊断成本"""
        costs = {
            DiagnosticProcedure.COLONOSCOPY: 1200.0,
            DiagnosticProcedure.SIGMOIDOSCOPY: 600.0,
            DiagnosticProcedure.CT_COLONOGRAPHY: 800.0,
            DiagnosticProcedure.BIOPSY: 300.0,
            DiagnosticProcedure.SPECIALIST_CONSULTATION: 200.0
        }
        
        return costs.get(procedure_type, 500.0)

    def _simulate_lesion_removal(
        self, 
        individual: Individual, 
        diagnostic_result: DiagnosticResult
    ):
        """模拟病变摘除"""
        # 根据故事要求，筛查并被确诊为病灶的，默认此处病灶被手术摘除
        # 将个体状态重置为正常
        individual.transition_to_state(DiseaseState.NORMAL)
        
        # 记录摘除信息
        diagnostic_result.additional_data['lesion_removed'] = True
        diagnostic_result.additional_data['removal_date'] = datetime.now()

    def _initiate_treatment(
        self, 
        individual: Individual, 
        diagnostic_result: DiagnosticResult
    ) -> str:
        """启动治疗流程"""
        if diagnostic_result.confirmed_disease_state:
            state = diagnostic_result.confirmed_disease_state
            
            if state in [DiseaseState.LOW_RISK_ADENOMA, DiseaseState.HIGH_RISK_ADENOMA,
                        DiseaseState.SMALL_SERRATED, DiseaseState.LARGE_SERRATED]:
                return "内镜下摘除"
            elif state == DiseaseState.PRECLINICAL_CANCER:
                return "早期癌症治疗"
            elif state.value.startswith('clinical_cancer'):
                return "癌症综合治疗"
        
        return "观察随访"

    def _add_diagnostic_history(
        self, 
        individual: Individual, 
        diagnostic_result: DiagnosticResult
    ):
        """添加诊断历史记录"""
        # 这里可以扩展Individual类来支持诊断历史
        # 目前作为占位符实现
        if not hasattr(individual, 'diagnostic_history'):
            individual.diagnostic_history = []
        
        individual.diagnostic_history.append({
            'date': diagnostic_result.appointment.actual_date or datetime.now(),
            'procedure': diagnostic_result.appointment.procedure_type.value,
            'findings': diagnostic_result.findings,
            'confirmed_state': diagnostic_result.confirmed_disease_state.value if diagnostic_result.confirmed_disease_state else None
        })

    def get_appointment_statistics(self) -> Dict[str, Any]:
        """获取预约统计信息"""
        total_appointments = len(self.appointment_queue) + len(self.completed_appointments)
        
        if total_appointments == 0:
            return {}
            
        # 按程序类型统计
        procedure_stats = {}
        for appointment in self.appointment_queue + self.completed_appointments:
            proc_type = appointment.procedure_type.value
            if proc_type not in procedure_stats:
                procedure_stats[proc_type] = {
                    'count': 0,
                    'avg_waiting_time': 0,
                    'total_cost': 0
                }
            
            procedure_stats[proc_type]['count'] += 1
            procedure_stats[proc_type]['total_cost'] += appointment.cost
            
        # 计算平均等待时间
        for proc_type in procedure_stats:
            appointments = [a for a in self.appointment_queue + self.completed_appointments
                          if a.procedure_type.value == proc_type]
            if appointments:
                avg_waiting = sum(a.waiting_time_days for a in appointments) / len(appointments)
                procedure_stats[proc_type]['avg_waiting_time'] = avg_waiting
        
        return {
            'total_appointments': total_appointments,
            'pending_appointments': len(self.appointment_queue),
            'completed_appointments': len(self.completed_appointments),
            'procedure_statistics': procedure_stats
        }
