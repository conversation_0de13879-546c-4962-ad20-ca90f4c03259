"""
疾病状态停留时间建模系统

实现每个疾病状态的停留时间分布建模，支持随机抽样和参数化配置。
"""

import numpy as np
from typing import Dict, Optional, List, Any
from dataclasses import dataclass, field
from enum import Enum
import logging
from scipy import stats

logger = logging.getLogger(__name__)


class DistributionType(Enum):
    """分布类型枚举"""
    NORMAL = "normal"
    LOGNORMAL = "lognormal"
    EXPONENTIAL = "exponential"
    WEIBULL = "weibull"
    GAMMA = "gamma"


@dataclass
class DwellTimeParameters:
    """停留时间分布参数"""
    distribution_type: DistributionType
    mean: float
    std: float
    min_time: float = 0.0
    max_time: float = float('inf')
    shape_param: Optional[float] = None  # 用于Weibull、Gamma等分布
    scale_param: Optional[float] = None

    def __post_init__(self):
        """参数验证"""
        if self.mean <= 0:
            raise ValueError("平均停留时间必须大于0")
        if self.std <= 0:
            raise ValueError("标准差必须大于0")
        if self.min_time < 0:
            raise ValueError("最小停留时间不能为负")
        if self.max_time <= self.min_time:
            raise ValueError("最大停留时间必须大于最小停留时间")


@dataclass
class DwellTimeStatistics:
    """停留时间统计信息"""
    state: str
    sample_count: int = 0
    mean_time: float = 0.0
    std_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    samples: List[float] = field(default_factory=list)

    def update_statistics(self, new_sample: float) -> None:
        """更新统计信息"""
        self.samples.append(new_sample)
        self.sample_count += 1

        # 更新基本统计量
        self.mean_time = np.mean(self.samples)
        self.std_time = np.std(self.samples, ddof=1) if len(self.samples) > 1 else 0.0
        self.min_time = min(self.min_time, new_sample)
        self.max_time = max(self.max_time, new_sample)

    def get_summary(self) -> Dict[str, float]:
        """获取统计摘要"""
        return {
            "sample_count": self.sample_count,
            "mean_time": self.mean_time,
            "std_time": self.std_time,
            "min_time": self.min_time,
            "max_time": self.max_time,
            "median_time": np.median(self.samples) if self.samples else 0.0,
            "q25": np.percentile(self.samples, 25) if self.samples else 0.0,
            "q75": np.percentile(self.samples, 75) if self.samples else 0.0
        }


@dataclass
class GenderSpecificDwellTimeDistribution:
    """性别特异性停留时间分布"""
    distribution_type: str      # 分布类型
    mean: float                # 平均停留时间
    std: float                 # 标准差
    min_time: float            # 最小停留时间
    max_time: float            # 最大停留时间
    shape_param: float = 1.0   # 形状参数（用于Weibull、Gamma等分布）
    scale_param: float = 1.0   # 尺度参数


class DwellTimeModel:
    """疾病状态停留时间建模类

    管理各个疾病状态的停留时间分布，提供随机抽样和统计功能。
    支持性别特异性停留时间分布。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化停留时间模型

        Args:
            config: 配置字典，包含各状态的停留时间参数
        """
        self.config = config or {}
        self.state_parameters: Dict[str, DwellTimeParameters] = {}
        self.statistics: Dict[str, DwellTimeStatistics] = {}
        self.gender_specific_distributions: Dict[str, Dict[str, GenderSpecificDwellTimeDistribution]] = {}
        self.random_state = np.random.RandomState(42)  # 可重现的随机数

        # 初始化默认参数
        self._initialize_default_parameters()
        self._initialize_gender_specific_distributions()

        # 如果提供了配置，则更新参数
        if config:
            self._load_configuration(config)

    def _initialize_default_parameters(self) -> None:
        """初始化默认的停留时间参数"""
        # 基于故事中Dev Notes的进展时间参数
        default_params = {
            "normal": DwellTimeParameters(
                distribution_type=DistributionType.EXPONENTIAL,
                mean=50.0,  # 平均50年保持正常状态
                std=20.0,
                min_time=0.0,
                max_time=100.0
            ),
            "low_risk_adenoma": DwellTimeParameters(
                distribution_type=DistributionType.NORMAL,
                mean=5.0,   # 平均5年
                std=2.0,    # 标准差2年
                min_time=1.0,   # 最小1年
                max_time=15.0   # 最大15年
            ),
            "high_risk_adenoma": DwellTimeParameters(
                distribution_type=DistributionType.NORMAL,
                mean=8.0,   # 平均8年
                std=3.0,    # 标准差3年
                min_time=2.0,   # 最小2年
                max_time=20.0   # 最大20年
            ),
            "preclinical_cancer": DwellTimeParameters(
                distribution_type=DistributionType.NORMAL,
                mean=2.0,   # 平均2年
                std=1.0,    # 标准差1年
                min_time=0.5,   # 最小6个月
                max_time=5.0    # 最大5年
            ),
            "clinical_cancer_stage_i": DwellTimeParameters(
                distribution_type=DistributionType.LOGNORMAL,
                mean=3.0,   # 平均3年
                std=1.5,
                min_time=0.5,
                max_time=10.0
            ),
            "clinical_cancer_stage_ii": DwellTimeParameters(
                distribution_type=DistributionType.LOGNORMAL,
                mean=2.5,   # 平均2.5年
                std=1.2,
                min_time=0.5,
                max_time=8.0
            ),
            "clinical_cancer_stage_iii": DwellTimeParameters(
                distribution_type=DistributionType.LOGNORMAL,
                mean=2.0,   # 平均2年
                std=1.0,
                min_time=0.3,
                max_time=6.0
            ),
            "clinical_cancer_stage_iv": DwellTimeParameters(
                distribution_type=DistributionType.LOGNORMAL,
                mean=1.0,   # 平均1年
                std=0.8,
                min_time=0.1,
                max_time=3.0
            )
        }

        self.state_parameters.update(default_params)

        # 初始化统计对象
        for state in default_params.keys():
            self.statistics[state] = DwellTimeStatistics(state=state)

    def _initialize_gender_specific_distributions(self) -> None:
        """初始化性别特异性停留时间分布"""
        # 男性停留时间分布（通常进展更快，停留时间更短）
        male_distributions = {
            "low_risk_adenoma": GenderSpecificDwellTimeDistribution(
                distribution_type="normal",
                mean=4.5,      # 比基准短0.5年
                std=1.8,
                min_time=0.5,
                max_time=12.0
            ),
            "high_risk_adenoma": GenderSpecificDwellTimeDistribution(
                distribution_type="normal",
                mean=2.7,      # 比基准短0.3年
                std=1.1,
                min_time=0.3,
                max_time=8.0
            ),
            "preclinical_cancer": GenderSpecificDwellTimeDistribution(
                distribution_type="lognormal",
                mean=1.8,      # 比基准短0.2年
                std=0.7,
                min_time=0.2,
                max_time=5.0
            ),
            "clinical_cancer_stage_i": GenderSpecificDwellTimeDistribution(
                distribution_type="exponential",
                mean=0.9,      # 比基准短0.1年
                std=0.7,
                min_time=0.1,
                max_time=2.8
            )
        }

        # 女性停留时间分布（通常进展较慢，停留时间较长）
        female_distributions = {
            "low_risk_adenoma": GenderSpecificDwellTimeDistribution(
                distribution_type="normal",
                mean=5.5,      # 比基准长0.5年
                std=2.2,
                min_time=0.5,
                max_time=15.0
            ),
            "high_risk_adenoma": GenderSpecificDwellTimeDistribution(
                distribution_type="normal",
                mean=3.3,      # 比基准长0.3年
                std=1.3,
                min_time=0.3,
                max_time=10.0
            ),
            "preclinical_cancer": GenderSpecificDwellTimeDistribution(
                distribution_type="lognormal",
                mean=2.2,      # 比基准长0.2年
                std=0.9,
                min_time=0.2,
                max_time=6.0
            ),
            "clinical_cancer_stage_i": GenderSpecificDwellTimeDistribution(
                distribution_type="exponential",
                mean=1.1,      # 比基准长0.1年
                std=0.9,
                min_time=0.1,
                max_time=3.2
            )
        }

        self.gender_specific_distributions = {
            "male": male_distributions,
            "female": female_distributions
        }

    def _load_configuration(self, config: Dict[str, Any]) -> None:
        """从配置字典加载参数"""
        for state, params in config.items():
            if isinstance(params, dict):
                try:
                    dist_type = params.get('distribution_type', 'normal')
                    self.state_parameters[state] = DwellTimeParameters(
                        distribution_type=DistributionType(dist_type),
                        mean=float(params['mean']),
                        std=float(params['std']),
                        min_time=float(params.get('min_time', 0.0)),
                        max_time=float(params.get('max_time', float('inf'))),
                        shape_param=params.get('shape_param'),
                        scale_param=params.get('scale_param')
                    )

                    # 初始化统计对象
                    if state not in self.statistics:
                        self.statistics[state] = DwellTimeStatistics(state=state)

                except (KeyError, ValueError, TypeError) as e:
                    logger.warning(f"配置状态 {state} 的参数时出错: {e}")

    def sample_dwell_time(
        self,
        state: str,
        age: Optional[float] = None,
        gender: Optional[str] = None
    ) -> float:
        """
        为指定状态抽样停留时间

        Args:
            state: 疾病状态
            age: 年龄（可选，用于年龄相关调整）
            gender: 性别（可选，用于性别相关调整）

        Returns:
            float: 停留时间（年）
        """
        # 优先使用性别特异性分布
        if gender and gender in self.gender_specific_distributions:
            if state in self.gender_specific_distributions[gender]:
                sample = self._sample_from_gender_specific_distribution(state, gender)
            else:
                # 回退到通用分布
                sample = self._sample_from_general_distribution(state)
        else:
            # 使用通用分布
            sample = self._sample_from_general_distribution(state)

        # 应用年龄调整（如果提供）
        if age is not None:
            sample = self._apply_age_adjustment(sample, state, age)

        # 更新统计信息
        if state in self.statistics:
            self.statistics[state].update_statistics(sample)

        return sample

    def _sample_from_general_distribution(self, state: str) -> float:
        """从通用分布抽样停留时间"""
        if state not in self.state_parameters:
            raise ValueError(f"未知的疾病状态: {state}")

        params = self.state_parameters[state]
        sample = self._generate_sample(params)

        # 确保样本在有效范围内
        return np.clip(sample, params.min_time, params.max_time)

    def _sample_from_gender_specific_distribution(self, state: str, gender: str) -> float:
        """从性别特异性分布抽样停留时间"""
        dist = self.gender_specific_distributions[gender][state]

        # 根据分布类型生成样本
        if dist.distribution_type == "normal":
            sample = self.random_state.normal(dist.mean, dist.std)
        elif dist.distribution_type == "lognormal":
            sample = self.random_state.lognormal(np.log(dist.mean), dist.std)
        elif dist.distribution_type == "exponential":
            sample = self.random_state.exponential(dist.mean)
        elif dist.distribution_type == "weibull":
            sample = self.random_state.weibull(dist.shape_param) * dist.scale_param
        elif dist.distribution_type == "gamma":
            sample = self.random_state.gamma(dist.shape_param, dist.scale_param)
        else:
            # 默认使用正态分布
            sample = self.random_state.normal(dist.mean, dist.std)

        # 确保样本在有效范围内
        return np.clip(sample, dist.min_time, dist.max_time)

    def _generate_sample(self, params: DwellTimeParameters) -> float:
        """根据分布类型生成样本"""
        if params.distribution_type == DistributionType.NORMAL:
            return self.random_state.normal(params.mean, params.std)

        elif params.distribution_type == DistributionType.LOGNORMAL:
            # 对数正态分布：先计算对数空间的参数
            mu = np.log(params.mean ** 2 / np.sqrt(params.std ** 2 + params.mean ** 2))
            sigma = np.sqrt(np.log(1 + (params.std / params.mean) ** 2))
            return self.random_state.lognormal(mu, sigma)

        elif params.distribution_type == DistributionType.EXPONENTIAL:
            return self.random_state.exponential(params.mean)

        elif params.distribution_type == DistributionType.WEIBULL:
            # Weibull分布需要形状参数
            shape = params.shape_param or 2.0
            scale = params.scale_param or params.mean / stats.gamma(1 + 1 / shape)
            return self.random_state.weibull(shape) * scale

        elif params.distribution_type == DistributionType.GAMMA:
            # Gamma分布
            shape = params.shape_param or (params.mean / params.std) ** 2
            scale = params.scale_param or params.std ** 2 / params.mean
            return self.random_state.gamma(shape, scale)

        else:
            # 默认使用正态分布
            logger.warning(f"未知的分布类型 {params.distribution_type}，使用正态分布")
            return self.random_state.normal(params.mean, params.std)

    def _apply_age_adjustment(self, sample: float, state: str, age: float) -> float:
        """应用年龄相关的停留时间调整"""
        # 年龄调整因子：年龄越大，某些状态的停留时间可能越短
        age_adjustment_factors = {
            "low_risk_adenoma": 1.0 - (age - 50) * 0.01,  # 年龄每增加1岁，停留时间减少1%
            "high_risk_adenoma": 1.0 - (age - 50) * 0.015,  # 年龄每增加1岁，停留时间减少1.5%
            "preclinical_cancer": 1.0 - (age - 60) * 0.02,  # 年龄每增加1岁，停留时间减少2%
            "clinical_cancer_stage_i": 1.0 - (age - 65) * 0.01,
            "clinical_cancer_stage_ii": 1.0 - (age - 65) * 0.015,
            "clinical_cancer_stage_iii": 1.0 - (age - 65) * 0.02,
            "clinical_cancer_stage_iv": 1.0 - (age - 65) * 0.025
        }

        factor = age_adjustment_factors.get(state, 1.0)
        # 确保调整因子在合理范围内
        factor = np.clip(factor, 0.5, 1.5)

        return sample * factor

    def _apply_gender_adjustment(self, sample: float, state: str, gender: str) -> float:
        """应用性别相关的停留时间调整"""
        # 性别调整因子：基于医学文献的性别差异
        gender_adjustment_factors = {
            "male": {
                "low_risk_adenoma": 0.95,  # 男性进展稍快
                "high_risk_adenoma": 0.90,
                "preclinical_cancer": 0.95,
                "clinical_cancer_stage_i": 1.0,
                "clinical_cancer_stage_ii": 1.0,
                "clinical_cancer_stage_iii": 1.0,
                "clinical_cancer_stage_iv": 1.0
            },
            "female": {
                "low_risk_adenoma": 1.05,  # 女性进展稍慢
                "high_risk_adenoma": 1.10,
                "preclinical_cancer": 1.05,
                "clinical_cancer_stage_i": 1.0,
                "clinical_cancer_stage_ii": 1.0,
                "clinical_cancer_stage_iii": 1.0,
                "clinical_cancer_stage_iv": 1.0
            }
        }

        factor = gender_adjustment_factors.get(gender, {}).get(state, 1.0)
        return sample * factor

    def get_state_parameters(self, state: str) -> Optional[DwellTimeParameters]:
        """获取指定状态的参数"""
        return self.state_parameters.get(state)

    def update_state_parameters(self, state: str, params: DwellTimeParameters) -> None:
        """更新指定状态的参数"""
        self.state_parameters[state] = params

        # 如果统计对象不存在，则创建
        if state not in self.statistics:
            self.statistics[state] = DwellTimeStatistics(state=state)

    def get_statistics(self, state: str) -> Optional[DwellTimeStatistics]:
        """获取指定状态的统计信息"""
        return self.statistics.get(state)

    def get_all_statistics(self) -> Dict[str, Dict[str, float]]:
        """获取所有状态的统计摘要"""
        return {state: stats.get_summary() for state, stats in self.statistics.items()}

    def reset_statistics(self, state: Optional[str] = None) -> None:
        """重置统计信息"""
        if state is None:
            # 重置所有状态的统计
            for state_stats in self.statistics.values():
                state_stats.samples.clear()
                state_stats.sample_count = 0
                state_stats.mean_time = 0.0
                state_stats.std_time = 0.0
                state_stats.min_time = float('inf')
                state_stats.max_time = 0.0
        else:
            # 重置指定状态的统计
            if state in self.statistics:
                state_stats = self.statistics[state]
                state_stats.samples.clear()
                state_stats.sample_count = 0
                state_stats.mean_time = 0.0
                state_stats.std_time = 0.0
                state_stats.min_time = float('inf')
                state_stats.max_time = 0.0

    def validate_parameters(self) -> Dict[str, List[str]]:
        """验证所有状态的参数"""
        validation_results = {}

        for state, params in self.state_parameters.items():
            errors = []

            # 基本参数验证
            if params.mean <= 0:
                errors.append("平均停留时间必须大于0")
            if params.std <= 0:
                errors.append("标准差必须大于0")
            if params.min_time < 0:
                errors.append("最小停留时间不能为负")
            if params.max_time <= params.min_time:
                errors.append("最大停留时间必须大于最小停留时间")

            # 分布特异性验证
            if params.distribution_type == DistributionType.WEIBULL:
                if params.shape_param is None or params.shape_param <= 0:
                    errors.append("Weibull分布需要正的形状参数")

            if params.distribution_type == DistributionType.GAMMA:
                if params.shape_param is None or params.shape_param <= 0:
                    errors.append("Gamma分布需要正的形状参数")

            if errors:
                validation_results[state] = errors

        return validation_results

    def export_configuration(self) -> Dict[str, Dict[str, Any]]:
        """导出当前配置"""
        config = {}
        for state, params in self.state_parameters.items():
            config[state] = {
                'distribution_type': params.distribution_type.value,
                'mean': params.mean,
                'std': params.std,
                'min_time': params.min_time,
                'max_time': params.max_time,
                'shape_param': params.shape_param,
                'scale_param': params.scale_param
            }
        return config

    def set_random_seed(self, seed: int) -> None:
        """设置随机数种子"""
        self.random_state = np.random.RandomState(seed)

    def get_supported_states(self) -> List[str]:
        """获取支持的疾病状态列表"""
        return list(self.state_parameters.keys())

    def calculate_expected_dwell_time(self, state: str) -> float:
        """计算指定状态的期望停留时间"""
        if state not in self.state_parameters:
            raise ValueError(f"未知的疾病状态: {state}")

        params = self.state_parameters[state]

        # 根据分布类型计算期望值
        if params.distribution_type == DistributionType.NORMAL:
            return params.mean
        elif params.distribution_type == DistributionType.LOGNORMAL:
            return params.mean  # 已经是期望值
        elif params.distribution_type == DistributionType.EXPONENTIAL:
            return params.mean
        elif params.distribution_type == DistributionType.WEIBULL:
            shape = params.shape_param or 2.0
            scale = params.scale_param or params.mean / stats.gamma(1 + 1 / shape)
            return scale * stats.gamma(1 + 1 / shape)
        elif params.distribution_type == DistributionType.GAMMA:
            shape = params.shape_param or (params.mean / params.std) ** 2
            scale = params.scale_param or params.std ** 2 / params.mean
            return shape * scale
        else:
            return params.mean
