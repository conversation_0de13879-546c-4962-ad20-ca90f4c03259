"""
时间趋势依从性模型单元测试

测试TemporalComplianceModel类的各项功能，包括时间趋势分析、
依从性模式识别和未来预测。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.modules.screening.temporal_compliance import (
    TemporalComplianceModel,
    TemporalComplianceParameters,
    CompliancePattern
)
from src.modules.screening.compliance_model import ScreeningEvent
from src.modules.screening.enums import ScreeningToolType, ScreeningResult
from src.core.individual import Individual
from src.core.enums import Gender


class TestTemporalComplianceParameters:
    """测试TemporalComplianceParameters数据类"""
    
    def test_valid_parameters(self):
        """测试有效参数创建"""
        params = TemporalComplianceParameters(
            first_time_multiplier=0.8,
            repeat_multiplier=1.2,
            time_decay_rate=0.05,
            recovery_rate=0.1,
            max_decay_years=10
        )
        
        assert params.first_time_multiplier == 0.8
        assert params.repeat_multiplier == 1.2
        assert params.time_decay_rate == 0.05
        assert params.recovery_rate == 0.1
        assert params.max_decay_years == 10
    
    def test_invalid_decay_rate(self):
        """测试无效的衰减率"""
        with pytest.raises(ValueError, match="时间衰减率必须在0-1之间"):
            TemporalComplianceParameters(time_decay_rate=1.5)
    
    def test_invalid_recovery_rate(self):
        """测试无效的恢复率"""
        with pytest.raises(ValueError, match="恢复率必须在0-1之间"):
            TemporalComplianceParameters(recovery_rate=-0.1)
    
    def test_invalid_max_decay_years(self):
        """测试无效的最大衰减年限"""
        with pytest.raises(ValueError, match="最大衰减年限必须大于0"):
            TemporalComplianceParameters(max_decay_years=0)


class TestTemporalComplianceModel:
    """测试TemporalComplianceModel类"""
    
    @pytest.fixture
    def sample_parameters(self):
        """提供测试用的参数"""
        return TemporalComplianceParameters(
            first_time_multiplier=0.8,
            repeat_multiplier=1.2,
            time_decay_rate=0.05,
            recovery_rate=0.1,
            max_decay_years=10,
            interruption_penalty=0.2,
            consistency_bonus=0.1
        )
    
    @pytest.fixture
    def sample_individual(self):
        """提供测试用的个体"""
        return Individual(
            birth_year=1970,
            gender=Gender.MALE,
            individual_id="test_001"
        )
    
    @pytest.fixture
    def temporal_model(self, sample_parameters):
        """提供测试用的时间趋势模型"""
        return TemporalComplianceModel(sample_parameters)
    
    def test_model_initialization(self, sample_parameters):
        """测试模型初始化"""
        model = TemporalComplianceModel(sample_parameters)
        
        assert model.parameters == sample_parameters
        assert isinstance(model._compliance_cache, dict)
    
    def test_calculate_temporal_factor_first_time(self, temporal_model, sample_individual):
        """测试首次筛查的时间因子计算"""
        # 空的筛查历史表示首次筛查
        screening_history = []
        
        factor = temporal_model.calculate_temporal_factor(
            sample_individual, 
            screening_history
        )
        
        # 应该返回首次筛查倍数
        assert factor == 0.8
    
    def test_calculate_temporal_factor_repeat(self, temporal_model, sample_individual):
        """测试重复筛查的时间因子计算"""
        # 创建1年前的筛查历史
        past_screening = ScreeningEvent(
            date=datetime.now() - timedelta(days=365),
            tool_type=ScreeningToolType.FIT,
            result=ScreeningResult.NEGATIVE,
            individual_id=sample_individual.individual_id
        )
        screening_history = [past_screening]
        
        factor = temporal_model.calculate_temporal_factor(
            sample_individual, 
            screening_history
        )
        
        # 验证因子在合理范围内
        assert 0.1 <= factor <= 2.0
        
        # 验证包含重复筛查倍数、时间衰减和模式调整
        # 基础：1.2 * (1-0.05)^1 = 1.14
        # 模式调整：1.0 + 0.1 (一致性奖励) = 1.1
        # 最终：1.14 * 1.1 = 1.254
        expected_base = 1.2 * (0.95 ** 1)
        expected_adjustment = 1.1  # 一致性奖励
        expected = expected_base * expected_adjustment
        assert abs(factor - expected) < 0.01
    
    def test_analyze_compliance_pattern_consistent(self, temporal_model):
        """测试一致性依从性模式识别"""
        # 创建规律的筛查历史（每年一次）
        base_date = datetime.now() - timedelta(days=365 * 3)
        screening_history = []
        
        for i in range(4):
            event = ScreeningEvent(
                date=base_date + timedelta(days=365 * i),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id="test_001"
            )
            screening_history.append(event)
        
        pattern = temporal_model._analyze_compliance_pattern(screening_history)
        assert pattern == CompliancePattern.CONSISTENT
    
    def test_analyze_compliance_pattern_declining(self, temporal_model):
        """测试下降性依从性模式识别"""
        # 创建间隔逐渐增长的筛查历史
        base_date = datetime.now() - timedelta(days=365 * 3)
        screening_history = [
            ScreeningEvent(
                date=base_date,
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id="test_001"
            ),
            ScreeningEvent(
                date=base_date + timedelta(days=365),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id="test_001"
            ),
            ScreeningEvent(
                date=base_date + timedelta(days=365 + 600),  # 间隔增长
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id="test_001"
            )
        ]
        
        pattern = temporal_model._analyze_compliance_pattern(screening_history)
        assert pattern == CompliancePattern.DECLINING
    
    def test_predict_future_compliance(self, temporal_model, sample_individual):
        """测试未来依从性预测"""
        # 创建筛查历史
        past_screening = ScreeningEvent(
            date=datetime.now() - timedelta(days=365),
            tool_type=ScreeningToolType.FIT,
            result=ScreeningResult.NEGATIVE,
            individual_id=sample_individual.individual_id
        )
        screening_history = [past_screening]
        
        predictions = temporal_model.predict_future_compliance(
            sample_individual, 
            screening_history, 
            prediction_years=3
        )
        
        # 验证预测结果
        assert len(predictions) == 3
        assert all(isinstance(year, int) for year in predictions.keys())
        assert all(isinstance(factor, float) for factor in predictions.values())
        assert all(0.1 <= factor <= 2.0 for factor in predictions.values())
        
        # 验证时间衰减趋势（后续年份的因子应该更小）
        factors = list(predictions.values())
        assert factors[1] <= factors[0]  # 第2年 <= 第1年
        assert factors[2] <= factors[1]  # 第3年 <= 第2年
    
    def test_calculate_screening_interval_recommendation(self, temporal_model, sample_individual):
        """测试筛查间隔推荐"""
        # 创建一致性筛查历史
        base_date = datetime.now() - timedelta(days=365 * 2)
        screening_history = [
            ScreeningEvent(
                date=base_date,
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id=sample_individual.individual_id
            ),
            ScreeningEvent(
                date=base_date + timedelta(days=365),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id=sample_individual.individual_id
            )
        ]
        
        recommendation = temporal_model.calculate_screening_interval_recommendation(
            sample_individual,
            screening_history,
            ScreeningToolType.FIT
        )
        
        # 验证推荐结果结构
        assert 'recommended_interval_months' in recommendation
        assert 'compliance_pattern' in recommendation
        assert 'confidence_level' in recommendation
        assert 'rationale' in recommendation
        
        # 验证推荐间隔合理性
        assert isinstance(recommendation['recommended_interval_months'], int)
        assert 6 <= recommendation['recommended_interval_months'] <= 24  # FIT合理范围
        
        # 验证置信度
        assert 0.0 <= recommendation['confidence_level'] <= 1.0
    
    def test_generate_compliance_trend_report(self, temporal_model, sample_individual):
        """测试依从性趋势报告生成"""
        # 创建筛查历史
        base_date = datetime.now() - timedelta(days=365 * 2)
        screening_history = [
            ScreeningEvent(
                date=base_date,
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id=sample_individual.individual_id
            ),
            ScreeningEvent(
                date=base_date + timedelta(days=365),
                tool_type=ScreeningToolType.FIT,
                result=ScreeningResult.NEGATIVE,
                individual_id=sample_individual.individual_id
            )
        ]
        
        report = temporal_model.generate_compliance_trend_report(
            sample_individual,
            screening_history
        )
        
        # 验证报告结构
        assert 'individual_id' in report
        assert 'compliance_pattern' in report
        assert 'current_temporal_factor' in report
        assert 'screening_count' in report
        assert 'date_range' in report
        assert 'interval_statistics' in report
        assert 'future_predictions' in report
        assert 'recommendations' in report
        
        # 验证具体内容
        assert report['individual_id'] == sample_individual.individual_id
        assert report['screening_count'] == 2
        assert isinstance(report['current_temporal_factor'], float)
        assert isinstance(report['recommendations'], list)
    
    def test_generate_compliance_trend_report_no_history(self, temporal_model, sample_individual):
        """测试无历史记录的趋势报告"""
        screening_history = []
        
        report = temporal_model.generate_compliance_trend_report(
            sample_individual,
            screening_history
        )
        
        assert report['status'] == 'no_history'
        assert 'message' in report
    
    def test_get_pattern_adjustment(self, temporal_model):
        """测试模式调整因子"""
        # 测试各种模式的调整因子
        consistent_adj = temporal_model._get_pattern_adjustment(CompliancePattern.CONSISTENT)
        declining_adj = temporal_model._get_pattern_adjustment(CompliancePattern.DECLINING)
        recovering_adj = temporal_model._get_pattern_adjustment(CompliancePattern.RECOVERING)
        
        # 验证调整方向
        assert consistent_adj > 1.0  # 一致性应该有奖励
        assert declining_adj < 1.0   # 下降性应该有惩罚
        assert recovering_adj > 1.0  # 恢复性应该有奖励
    
    def test_calculate_recommendation_confidence(self, temporal_model):
        """测试推荐置信度计算"""
        # 测试不同历史记录数量的置信度
        no_history = temporal_model._calculate_recommendation_confidence([])
        short_history = temporal_model._calculate_recommendation_confidence([Mock(), Mock()])
        long_history = temporal_model._calculate_recommendation_confidence([Mock()] * 5)
        
        # 验证置信度递增
        assert no_history < short_history < long_history
        assert all(0.0 <= conf <= 1.0 for conf in [no_history, short_history, long_history])


if __name__ == "__main__":
    pytest.main([__file__])
