"""
筛查工具配置模板生成器

提供标准化的配置模板，用于创建新的筛查工具配置文件。
"""

from typing import Dict, Any, Optional
from datetime import datetime
import logging

from .enums import ScreeningToolType, InvasivenessLevel, OperatorSkillLevel
from src.core.enums import DiseaseState, AnatomicalLocation


logger = logging.getLogger(__name__)


class ScreeningToolConfigTemplates:
    """筛查工具配置模板生成器"""
    
    @staticmethod
    def create_base_template(
        tool_type: ScreeningToolType,
        tool_name: str,
        description: str = "",
        version: str = "1.0"
    ) -> Dict[str, Any]:
        """
        创建基础配置模板
        
        Args:
            tool_type: 工具类型
            tool_name: 工具名称
            description: 工具描述
            version: 版本号
            
        Returns:
            Dict[str, Any]: 基础配置模板
        """
        return {
            "screening_tool": {
                "name": tool_name,
                "type": tool_type.name,
                "version": version,
                "description": description,
                
                "performance": {
                    "specificity": 0.95,
                    "sensitivity_by_state": {},
                    "operator_dependency": 1.0,
                    "location_sensitivity_modifiers": {
                        "proximal_colon": 1.0,
                        "distal_colon": 1.0,
                        "rectum": 1.0
                    },
                    "age_sensitivity_adjustment": {
                        "slope": 0.0,
                        "baseline_age": 50.0,
                        "min_sensitivity": 0.1,
                        "max_sensitivity": 1.0
                    },
                    "gender_sensitivity_modifiers": {
                        "male": 1.0,
                        "female": 1.0
                    }
                },
                
                "costs": {
                    "direct_cost": 0.0,
                    "indirect_cost": 0.0
                },
                
                "characteristics": {
                    "invasiveness": "non_invasive",
                    "preparation_required": False,
                    "operator_skill_required": "low",
                    "turnaround_time_days": 0,
                    "requires_sedation": False,
                    "radiation_exposure": False,
                    "sample_collection_required": False,
                    "can_detect_proximal": True,
                    "can_detect_distal": True,
                    "can_detect_rectal": True
                }
            },
            
            "metadata": {
                "created_date": datetime.now().isoformat(),
                "created_by": "ScreeningToolConfigTemplates",
                "last_modified": datetime.now().isoformat(),
                "version_history": [
                    {
                        "version": version,
                        "date": datetime.now().isoformat(),
                        "changes": "初始模板创建"
                    }
                ]
            }
        }
    
    @staticmethod
    def create_fit_template() -> Dict[str, Any]:
        """创建FIT工具配置模板"""
        template = ScreeningToolConfigTemplates.create_base_template(
            ScreeningToolType.FIT,
            "粪便免疫化学检测 (FIT)",
            "基于免疫化学方法检测粪便中隐血的非侵入性筛查工具"
        )
        
        # FIT特异性配置
        tool_config = template["screening_tool"]
        
        # 性能参数
        tool_config["performance"].update({
            "specificity": 0.95,
            "sensitivity_by_state": {
                "normal": 0.0,
                "low_risk_adenoma": 0.15,
                "high_risk_adenoma": 0.35,
                "small_serrated": 0.10,
                "large_serrated": 0.25,
                "preclinical_cancer": 0.75,
                "clinical_cancer_stage_i": 0.85,
                "clinical_cancer_stage_ii": 0.90,
                "clinical_cancer_stage_iii": 0.95,
                "clinical_cancer_stage_iv": 0.98
            },
            "detection_threshold": 100.0,
            "operator_dependency": 1.1,
            "location_sensitivity_modifiers": {
                "proximal_colon": 0.8,
                "distal_colon": 1.0,
                "rectum": 1.2
            },
            "age_sensitivity_adjustment": {
                "slope": 0.002,
                "baseline_age": 50.0,
                "min_sensitivity": 0.5,
                "max_sensitivity": 1.5
            },
            "gender_sensitivity_modifiers": {
                "male": 1.1,
                "female": 0.9
            }
        })
        
        # 成本配置
        tool_config["costs"] = {
            "direct_cost": 25.0,
            "material_cost": 5.0,
            "processing_cost": 15.0,
            "indirect_cost": 50.0
        }
        
        # 特性配置
        tool_config["characteristics"].update({
            "invasiveness": "non_invasive",
            "preparation_required": False,
            "operator_skill_required": "low",
            "turnaround_time_days": 3,
            "sample_collection_required": True
        })
        
        return template
    
    @staticmethod
    def create_colonoscopy_template() -> Dict[str, Any]:
        """创建结肠镜检查配置模板"""
        template = ScreeningToolConfigTemplates.create_base_template(
            ScreeningToolType.COLONOSCOPY,
            "结肠镜检查",
            "全结肠内镜检查，金标准筛查和诊断工具"
        )
        
        # 结肠镜特异性配置
        tool_config = template["screening_tool"]
        
        # 性能参数
        tool_config["performance"].update({
            "specificity": 0.99,
            "sensitivity_by_state": {
                "normal": 0.0,
                "low_risk_adenoma": 0.85,
                "high_risk_adenoma": 0.95,
                "small_serrated": 0.75,
                "large_serrated": 0.90,
                "preclinical_cancer": 0.98,
                "clinical_cancer_stage_i": 0.99,
                "clinical_cancer_stage_ii": 0.995,
                "clinical_cancer_stage_iii": 0.998,
                "clinical_cancer_stage_iv": 1.0
            },
            "operator_dependency": 2.0,
            "location_sensitivity_modifiers": {
                "proximal_colon": 0.9,
                "distal_colon": 1.0,
                "rectum": 1.0
            },
            "age_sensitivity_adjustment": {
                "slope": -0.001,
                "baseline_age": 50.0,
                "min_sensitivity": 0.7,
                "max_sensitivity": 1.0
            },
            "gender_sensitivity_modifiers": {
                "male": 1.0,
                "female": 0.95
            }
        })
        
        # 成本配置
        tool_config["costs"] = {
            "direct_cost": 800.0,
            "equipment_cost": 200.0,
            "staff_cost": 300.0,
            "facility_cost": 150.0,
            "anesthesia_cost": 200.0,
            "material_cost": 100.0,
            "indirect_cost": 400.0
        }
        
        # 特性配置
        tool_config["characteristics"].update({
            "invasiveness": "invasive",
            "preparation_required": True,
            "operator_skill_required": "high",
            "turnaround_time_days": 0,
            "requires_sedation": True
        })
        
        return template
    
    @staticmethod
    def create_sigmoidoscopy_template() -> Dict[str, Any]:
        """创建乙状结肠镜检查配置模板"""
        template = ScreeningToolConfigTemplates.create_base_template(
            ScreeningToolType.SIGMOIDOSCOPY,
            "乙状结肠镜检查",
            "检查远端结肠和直肠的内镜工具"
        )
        
        # 乙状结肠镜特异性配置
        tool_config = template["screening_tool"]
        
        # 性能参数
        tool_config["performance"].update({
            "specificity": 0.98,
            "sensitivity_by_state": {
                "normal": 0.0,
                "low_risk_adenoma": 0.80,
                "high_risk_adenoma": 0.90,
                "small_serrated": 0.70,
                "large_serrated": 0.85,
                "preclinical_cancer": 0.95,
                "clinical_cancer_stage_i": 0.98,
                "clinical_cancer_stage_ii": 0.99,
                "clinical_cancer_stage_iii": 0.995,
                "clinical_cancer_stage_iv": 1.0
            },
            "operator_dependency": 1.8,
            "location_sensitivity_modifiers": {
                "proximal_colon": 0.0,  # 无法检测近端结肠
                "distal_colon": 1.0,
                "rectum": 1.0
            },
            "age_sensitivity_adjustment": {
                "slope": -0.0005,
                "baseline_age": 50.0,
                "min_sensitivity": 0.8,
                "max_sensitivity": 1.0
            },
            "gender_sensitivity_modifiers": {
                "male": 1.0,
                "female": 0.95
            }
        })
        
        # 成本配置
        tool_config["costs"] = {
            "direct_cost": 400.0,
            "equipment_cost": 100.0,
            "staff_cost": 200.0,
            "facility_cost": 100.0,
            "material_cost": 50.0,
            "indirect_cost": 200.0
        }
        
        # 特性配置
        tool_config["characteristics"].update({
            "invasiveness": "invasive",
            "preparation_required": True,
            "operator_skill_required": "high",
            "turnaround_time_days": 0,
            "requires_sedation": False,
            "can_detect_proximal": False  # 关键特性：无法检测近端结肠
        })
        
        return template

    @staticmethod
    def create_risk_questionnaire_template() -> Dict[str, Any]:
        """创建风险评估问卷配置模板"""
        template = ScreeningToolConfigTemplates.create_base_template(
            ScreeningToolType.RISK_QUESTIONNAIRE,
            "结直肠癌风险评估问卷",
            "基于风险因素的结直肠癌筛查评估工具"
        )

        # 风险问卷特异性性能参数
        template["screening_tool"]["performance"].update({
            "specificity": 0.70,  # 风险问卷特异性相对较低
            "sensitivity_by_state": {
                DiseaseState.NORMAL.value: 0.0,
                DiseaseState.LOW_RISK_ADENOMA.value: 0.05,
                DiseaseState.HIGH_RISK_ADENOMA.value: 0.15,
                DiseaseState.PRECLINICAL_CANCER.value: 0.25,
                DiseaseState.CLINICAL_CANCER_STAGE_I.value: 0.40,
                DiseaseState.CLINICAL_CANCER_STAGE_II.value: 0.50,
                DiseaseState.CLINICAL_CANCER_STAGE_III.value: 0.60,
                DiseaseState.CLINICAL_CANCER_STAGE_IV.value: 0.70
            },
            "risk_thresholds": {
                "low_risk_cutoff": 0.3,
                "high_risk_cutoff": 0.7,
                "recommend_screening": True
            }
        })

        # 风险问卷成本配置
        template["screening_tool"]["costs"] = {
            "direct_cost": 0.0,
            "material_cost": 5.0,
            "processing_cost": 10.0,
            "indirect_cost": 30.0
        }

        # 风险问卷特性
        template["screening_tool"]["characteristics"].update({
            "invasiveness": InvasivenessLevel.NON_INVASIVE.value,
            "preparation_required": False,
            "turnaround_time_days": 0,
            "requires_sedation": False,
            "radiation_exposure": False,
            "sample_collection_required": False,
            "can_detect_proximal": False,
            "can_detect_distal": False,
            "can_detect_rectal": False,
            "requires_risk_profile": True,
            "assessment_type": "risk_based"
        })

        return template

    @staticmethod
    def create_other_tool_template() -> Dict[str, Any]:
        """创建其他工具配置模板"""
        template = ScreeningToolConfigTemplates.create_base_template(
            ScreeningToolType.OTHER,
            "其他筛查工具",
            "通用筛查工具配置模板，可根据具体工具进行定制"
        )

        # 其他工具性能参数（示例值）
        template["screening_tool"]["performance"].update({
            "specificity": 0.85,
            "sensitivity_by_state": {
                DiseaseState.NORMAL.value: 0.0,
                DiseaseState.LOW_RISK_ADENOMA.value: 0.20,
                DiseaseState.HIGH_RISK_ADENOMA.value: 0.40,
                DiseaseState.PRECLINICAL_CANCER.value: 0.70,
                DiseaseState.CLINICAL_CANCER_STAGE_I.value: 0.80,
                DiseaseState.CLINICAL_CANCER_STAGE_II.value: 0.85,
                DiseaseState.CLINICAL_CANCER_STAGE_III.value: 0.90,
                DiseaseState.CLINICAL_CANCER_STAGE_IV.value: 0.95
            }
        })

        # 检测机制配置
        template["screening_tool"]["detection_mechanism"] = {
            "type": "probability_based",
            "base_accuracy": 0.8,
            "variability": 0.1
        }

        # 特殊参数配置
        template["screening_tool"]["special_parameters"] = {
            "gender_factor": {
                "male": 1.0,
                "female": 0.95
            }
        }

        # 其他工具成本配置
        template["screening_tool"]["costs"] = {
            "direct_cost": 100.0,
            "material_cost": 20.0,
            "processing_cost": 30.0,
            "indirect_cost": 80.0
        }

        # 其他工具特性
        template["screening_tool"]["characteristics"].update({
            "invasiveness": InvasivenessLevel.MINIMALLY_INVASIVE.value,
            "preparation_required": False,
            "turnaround_time_days": 1,
            "requires_sedation": False,
            "radiation_exposure": False,
            "sample_collection_required": True,
            "can_detect_proximal": True,
            "can_detect_distal": True,
            "can_detect_rectal": True,
            "is_generic_implementation": True,
            "customizable": True
        })

        return template

    @staticmethod
    def create_custom_template(
        tool_type: ScreeningToolType,
        custom_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建自定义配置模板
        
        Args:
            tool_type: 工具类型
            custom_params: 自定义参数
            
        Returns:
            Dict[str, Any]: 自定义配置模板
        """
        # 获取基础模板
        if tool_type == ScreeningToolType.FIT:
            template = ScreeningToolConfigTemplates.create_fit_template()
        elif tool_type == ScreeningToolType.COLONOSCOPY:
            template = ScreeningToolConfigTemplates.create_colonoscopy_template()
        elif tool_type == ScreeningToolType.SIGMOIDOSCOPY:
            template = ScreeningToolConfigTemplates.create_sigmoidoscopy_template()
        else:
            template = ScreeningToolConfigTemplates.create_base_template(
                tool_type,
                custom_params.get("name", f"{tool_type.display_name}"),
                custom_params.get("description", ""),
                custom_params.get("version", "1.0")
            )
        
        # 应用自定义参数
        def update_nested_dict(target: Dict, source: Dict) -> None:
            """递归更新嵌套字典"""
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    update_nested_dict(target[key], value)
                else:
                    target[key] = value

        # 首先更新screening_tool部分的直接属性
        if "screening_tool" in custom_params:
            update_nested_dict(template["screening_tool"], custom_params["screening_tool"])

        # 然后更新其他顶级属性
        for key, value in custom_params.items():
            if key != "screening_tool":
                if key in template and isinstance(template[key], dict) and isinstance(value, dict):
                    update_nested_dict(template[key], value)
                else:
                    template[key] = value
        
        # 更新元数据
        template["metadata"]["last_modified"] = datetime.now().isoformat()
        template["metadata"]["created_by"] = "自定义模板生成器"
        
        return template
    
    @staticmethod
    def get_available_templates() -> Dict[str, str]:
        """获取可用的模板列表"""
        return {
            "fit": "粪便免疫化学检测 (FIT) 模板",
            "colonoscopy": "结肠镜检查模板",
            "sigmoidoscopy": "乙状结肠镜检查模板",
            "risk_questionnaire": "风险评估问卷模板",
            "other": "其他筛查工具模板",
            "base": "基础模板"
        }
