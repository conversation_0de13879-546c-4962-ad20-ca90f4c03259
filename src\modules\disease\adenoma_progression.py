"""
腺瘤进展建模系统

实现基于年龄的正态分布腺瘤进展时间建模，包括低风险到高风险腺瘤转换
和高风险腺瘤到临床前癌症转换。
"""

import numpy as np
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
import logging
from scipy import stats

from src.core.enums import DiseaseState, Gender

logger = logging.getLogger(__name__)


@dataclass
class ProgressionParameters:
    """进展参数配置"""
    mean_years: float           # 平均进展时间（年）
    std_years: float           # 标准差（年）
    min_years: float           # 最小进展时间（年）
    max_years: float           # 最大进展时间（年）
    age_adjustment_factor: float = 0.0  # 年龄调整因子


@dataclass
class GenderProgressionMultipliers:
    """性别特异性进展倍数"""
    male_multiplier: float
    female_multiplier: float


@dataclass
class GenderSpecificProgressionRates:
    """性别特异性进展率配置"""
    adenoma_initiation_rate: float      # 腺瘤产生率倍数
    low_to_high_progression_rate: float # 低风险到高风险腺瘤进展率倍数
    high_to_preclinical_rate: float     # 高风险腺瘤到临床前癌症进展率倍数
    preclinical_to_clinical_rate: float # 临床前到临床癌症进展率倍数
    cancer_stage_progression_rate: float # 癌症分期进展率倍数


@dataclass
class GenderRiskFactorWeights:
    """性别特异性风险因素权重"""
    smoking_weight: float
    alcohol_weight: float
    obesity_weight: float
    family_history_weight: float
    inflammatory_bowel_disease_weight: float


class AdenomaProgressionModel:
    """腺瘤进展建模类

    使用正态分布建模基于年龄的腺瘤进展时间，支持性别特异性参数。
    """

    def __init__(
        self,
        progression_params: Optional[Dict[str, ProgressionParameters]] = None,
        gender_multipliers: Optional[GenderProgressionMultipliers] = None,
        gender_specific_rates: Optional[Dict[str, GenderSpecificProgressionRates]] = None,
        gender_risk_weights: Optional[Dict[str, GenderRiskFactorWeights]] = None
    ):
        """
        初始化腺瘤进展模型

        Args:
            progression_params: 进展参数配置
            gender_multipliers: 性别特异性倍数
            gender_specific_rates: 性别特异性进展率
            gender_risk_weights: 性别特异性风险因素权重
        """
        self.progression_params = progression_params or self._get_default_progression_params()
        self.gender_multipliers = gender_multipliers or self._get_default_gender_multipliers()
        self.gender_specific_rates = gender_specific_rates or self._get_default_gender_specific_rates()
        self.gender_risk_weights = gender_risk_weights or self._get_default_gender_risk_weights()

        logger.info("腺瘤进展模型初始化完成")

    def _get_default_progression_params(self) -> Dict[str, ProgressionParameters]:
        """获取默认进展参数"""
        return {
            "low_to_high_adenoma": ProgressionParameters(
                mean_years=5.0,
                std_years=2.0,
                min_years=1.0,
                max_years=15.0,
                age_adjustment_factor=-0.02  # 年龄越大进展越快
            ),
            "high_adenoma_to_preclinical": ProgressionParameters(
                mean_years=8.0,
                std_years=3.0,
                min_years=2.0,
                max_years=20.0,
                age_adjustment_factor=-0.03  # 年龄越大进展越快
            )
        }

    def _get_default_gender_multipliers(self) -> GenderProgressionMultipliers:
        """获取默认性别倍数"""
        return GenderProgressionMultipliers(
            male_multiplier=0.9,      # 男性进展稍快（时间倍数小于1）
            female_multiplier=1.0     # 女性进展标准速度
        )

    def _get_default_gender_specific_rates(self) -> Dict[str, GenderSpecificProgressionRates]:
        """获取默认性别特异性进展率"""
        return {
            "male": GenderSpecificProgressionRates(
                adenoma_initiation_rate=1.2,        # 男性腺瘤产生率高20%
                low_to_high_progression_rate=1.1,   # 男性低到高风险进展快10%
                high_to_preclinical_rate=1.15,      # 男性高风险到临床前进展快15%
                preclinical_to_clinical_rate=1.0,   # 临床前到临床进展无性别差异
                cancer_stage_progression_rate=1.05  # 男性癌症分期进展稍快5%
            ),
            "female": GenderSpecificProgressionRates(
                adenoma_initiation_rate=1.0,        # 女性腺瘤产生率基准
                low_to_high_progression_rate=1.0,   # 女性低到高风险进展基准
                high_to_preclinical_rate=1.0,       # 女性高风险到临床前进展基准
                preclinical_to_clinical_rate=1.0,   # 临床前到临床进展基准
                cancer_stage_progression_rate=1.0   # 女性癌症分期进展基准
            )
        }

    def _get_default_gender_risk_weights(self) -> Dict[str, GenderRiskFactorWeights]:
        """获取默认性别特异性风险因素权重"""
        return {
            "male": GenderRiskFactorWeights(
                smoking_weight=1.3,                    # 男性吸烟风险权重更高
                alcohol_weight=1.2,                    # 男性饮酒风险权重更高
                obesity_weight=1.1,                    # 男性肥胖风险权重稍高
                family_history_weight=1.0,             # 家族史风险无性别差异
                inflammatory_bowel_disease_weight=1.0  # 炎症性肠病风险无性别差异
            ),
            "female": GenderRiskFactorWeights(
                smoking_weight=1.0,                    # 女性吸烟风险权重基准
                alcohol_weight=1.0,                    # 女性饮酒风险权重基准
                obesity_weight=1.2,                    # 女性肥胖风险权重更高
                family_history_weight=1.0,             # 家族史风险无性别差异
                inflammatory_bowel_disease_weight=1.1  # 女性炎症性肠病风险稍高
            )
        }

    def calculate_progression_time(
        self,
        transition_type: str,
        age: float,
        gender: Gender
    ) -> float:
        """
        计算进展时间
        
        Args:
            transition_type: 转换类型（"low_to_high_adenoma" 或 "high_adenoma_to_preclinical"）
            age: 当前年龄
            gender: 性别
            
        Returns:
            float: 进展时间（年）
        """
        if transition_type not in self.progression_params:
            raise ValueError(f"未知的转换类型: {transition_type}")
        
        params = self.progression_params[transition_type]
        
        # 1. 基础正态分布抽样
        base_time = np.random.normal(params.mean_years, params.std_years)
        
        # 2. 年龄调整
        age_adjusted_time = base_time + (age - 50.0) * params.age_adjustment_factor
        
        # 3. 性别调整
        if gender == Gender.MALE:
            gender_adjusted_time = age_adjusted_time * self.gender_multipliers.male_multiplier
        else:
            gender_adjusted_time = age_adjusted_time * self.gender_multipliers.female_multiplier
        
        # 4. 应用边界限制
        final_time = np.clip(gender_adjusted_time, params.min_years, params.max_years)
        
        logger.debug(
            f"进展时间计算: 类型={transition_type}, 年龄={age}, 性别={gender.value}, "
            f"基础时间={base_time:.2f}, 年龄调整={age_adjusted_time:.2f}, "
            f"最终时间={final_time:.2f}"
        )
        
        return final_time

    def calculate_gender_adjusted_progression_probability(
        self,
        transition_type: str,
        current_age: float,
        time_in_state: float,
        gender: Gender,
        risk_factors: Optional[Dict[str, float]] = None
    ) -> float:
        """
        计算性别调整的进展概率

        Args:
            transition_type: 转换类型
            current_age: 当前年龄
            time_in_state: 在当前状态的时间（年）
            gender: 性别
            risk_factors: 风险因素字典

        Returns:
            float: 性别调整的进展概率
        """
        # 获取基础进展概率
        base_probability = self.calculate_progression_probability(
            transition_type, current_age, time_in_state, gender
        )

        # 获取性别特异性进展率
        gender_str = gender.value.lower()
        if gender_str not in self.gender_specific_rates:
            return base_probability

        rates = self.gender_specific_rates[gender_str]

        # 根据转换类型应用性别特异性倍数
        rate_multiplier = 1.0
        if "low_to_high" in transition_type:
            rate_multiplier = rates.low_to_high_progression_rate
        elif "high_to_preclinical" in transition_type:
            rate_multiplier = rates.high_to_preclinical_rate
        elif "preclinical_to_clinical" in transition_type:
            rate_multiplier = rates.preclinical_to_clinical_rate
        elif "stage" in transition_type:
            rate_multiplier = rates.cancer_stage_progression_rate

        # 应用风险因素调整
        risk_adjustment = 1.0
        if risk_factors and gender_str in self.gender_risk_weights:
            risk_adjustment = self._calculate_risk_factor_adjustment(
                risk_factors, gender_str
            )

        # 计算最终概率
        adjusted_probability = base_probability * rate_multiplier * risk_adjustment

        # 确保概率在有效范围内
        return max(0.0, min(adjusted_probability, 1.0))

    def _calculate_risk_factor_adjustment(
        self,
        risk_factors: Dict[str, float],
        gender: str
    ) -> float:
        """
        计算风险因素调整倍数

        Args:
            risk_factors: 风险因素字典
            gender: 性别

        Returns:
            float: 风险因素调整倍数
        """
        if gender not in self.gender_risk_weights:
            return 1.0

        weights = self.gender_risk_weights[gender]
        adjustment = 1.0

        # 应用各种风险因素的性别特异性权重
        # 风险因素值在0-1之间，权重表示该因素对该性别的影响程度
        if "smoking" in risk_factors:
            adjustment *= 1.0 + (risk_factors["smoking"] * (weights.smoking_weight - 1.0))

        if "alcohol" in risk_factors:
            adjustment *= 1.0 + (risk_factors["alcohol"] * (weights.alcohol_weight - 1.0))

        if "obesity" in risk_factors:
            adjustment *= 1.0 + (risk_factors["obesity"] * (weights.obesity_weight - 1.0))

        if "family_history" in risk_factors:
            adjustment *= 1.0 + (risk_factors["family_history"] * (weights.family_history_weight - 1.0))

        if "inflammatory_bowel_disease" in risk_factors:
            adjustment *= 1.0 + (risk_factors["inflammatory_bowel_disease"] * (weights.inflammatory_bowel_disease_weight - 1.0))

        return adjustment

    def get_gender_specific_progression_rate(
        self,
        transition_type: str,
        gender: Gender
    ) -> float:
        """
        获取性别特异性进展率

        Args:
            transition_type: 转换类型
            gender: 性别

        Returns:
            float: 性别特异性进展率倍数
        """
        gender_str = gender.value.lower()
        if gender_str not in self.gender_specific_rates:
            return 1.0

        rates = self.gender_specific_rates[gender_str]

        if "low_to_high" in transition_type:
            return rates.low_to_high_progression_rate
        elif "high_to_preclinical" in transition_type:
            return rates.high_to_preclinical_rate
        elif "preclinical_to_clinical" in transition_type:
            return rates.preclinical_to_clinical_rate
        elif "stage" in transition_type:
            return rates.cancer_stage_progression_rate
        else:
            return 1.0

    def calculate_progression_probability(
        self,
        transition_type: str,
        current_age: float,
        time_in_state: float,
        gender: Gender
    ) -> float:
        """
        计算在给定时间内的进展概率
        
        Args:
            transition_type: 转换类型
            current_age: 当前年龄
            time_in_state: 在当前状态的时间（年）
            gender: 性别
            
        Returns:
            float: 进展概率
        """
        if transition_type not in self.progression_params:
            raise ValueError(f"未知的转换类型: {transition_type}")
        
        params = self.progression_params[transition_type]
        
        # 年龄调整的平均进展时间
        age_adjusted_mean = params.mean_years + (current_age - 50.0) * params.age_adjustment_factor
        
        # 性别调整
        if gender == Gender.MALE:
            adjusted_mean = age_adjusted_mean * self.gender_multipliers.male_multiplier
        else:
            adjusted_mean = age_adjusted_mean * self.gender_multipliers.female_multiplier
        
        # 使用正态分布的累积分布函数计算概率
        # P(进展时间 <= time_in_state)
        probability = stats.norm.cdf(
            time_in_state,
            loc=adjusted_mean,
            scale=params.std_years
        )
        
        return probability

    def sample_progression_time(
        self,
        transition_type: str,
        age: float,
        gender: Gender,
        n_samples: int = 1
    ) -> np.ndarray:
        """
        抽样进展时间
        
        Args:
            transition_type: 转换类型
            age: 年龄
            gender: 性别
            n_samples: 抽样数量
            
        Returns:
            np.ndarray: 进展时间数组
        """
        times = []
        for _ in range(n_samples):
            time = self.calculate_progression_time(transition_type, age, gender)
            times.append(time)
        
        return np.array(times)

    def get_progression_distribution(
        self,
        transition_type: str,
        age: float,
        gender: Gender
    ) -> Dict[str, float]:
        """
        获取进展时间分布参数
        
        Args:
            transition_type: 转换类型
            age: 年龄
            gender: 性别
            
        Returns:
            Dict[str, float]: 分布参数
        """
        if transition_type not in self.progression_params:
            raise ValueError(f"未知的转换类型: {transition_type}")
        
        params = self.progression_params[transition_type]
        
        # 年龄调整的平均时间
        age_adjusted_mean = params.mean_years + (age - 50.0) * params.age_adjustment_factor
        
        # 性别调整
        if gender == Gender.MALE:
            adjusted_mean = age_adjusted_mean * self.gender_multipliers.male_multiplier
        else:
            adjusted_mean = age_adjusted_mean * self.gender_multipliers.female_multiplier
        
        return {
            "mean": adjusted_mean,
            "std": params.std_years,
            "min": params.min_years,
            "max": params.max_years
        }

    def validate_transition(self, from_state: DiseaseState, to_state: DiseaseState) -> bool:
        """
        验证状态转换是否有效
        
        Args:
            from_state: 起始状态
            to_state: 目标状态
            
        Returns:
            bool: 是否有效
        """
        valid_transitions = {
            DiseaseState.LOW_RISK_ADENOMA: [DiseaseState.HIGH_RISK_ADENOMA],
            DiseaseState.HIGH_RISK_ADENOMA: [
                DiseaseState.PRECLINICAL_CANCER,
                DiseaseState.CLINICAL_CANCER_STAGE_I
            ]
        }
        
        return to_state in valid_transitions.get(from_state, [])

    def get_next_state(self, current_state: DiseaseState) -> DiseaseState:
        """
        获取下一个疾病状态
        
        Args:
            current_state: 当前状态
            
        Returns:
            DiseaseState: 下一个状态
        """
        if current_state == DiseaseState.LOW_RISK_ADENOMA:
            return DiseaseState.HIGH_RISK_ADENOMA
        elif current_state == DiseaseState.HIGH_RISK_ADENOMA:
            return DiseaseState.PRECLINICAL_CANCER
        else:
            raise ValueError(f"无法从状态 {current_state} 进展")

    def get_transition_type(self, from_state: DiseaseState, to_state: DiseaseState) -> str:
        """
        获取转换类型
        
        Args:
            from_state: 起始状态
            to_state: 目标状态
            
        Returns:
            str: 转换类型
        """
        if (from_state == DiseaseState.LOW_RISK_ADENOMA and 
            to_state == DiseaseState.HIGH_RISK_ADENOMA):
            return "low_to_high_adenoma"
        elif (from_state == DiseaseState.HIGH_RISK_ADENOMA and 
              to_state == DiseaseState.PRECLINICAL_CANCER):
            return "high_adenoma_to_preclinical"
        else:
            raise ValueError(f"无效的转换: {from_state} -> {to_state}")

    def update_progression_parameters(
        self, 
        transition_type: str, 
        new_params: ProgressionParameters
    ) -> None:
        """
        更新进展参数
        
        Args:
            transition_type: 转换类型
            new_params: 新的进展参数
        """
        self.progression_params[transition_type] = new_params
        logger.info(f"进展参数已更新: {transition_type} -> {new_params}")

    def update_gender_multipliers(self, new_multipliers: GenderProgressionMultipliers) -> None:
        """
        更新性别倍数
        
        Args:
            new_multipliers: 新的性别倍数
        """
        self.gender_multipliers = new_multipliers
        logger.info(f"性别倍数已更新: {new_multipliers}")

    def get_model_parameters(self) -> Dict:
        """
        获取模型参数

        Returns:
            Dict: 模型参数字典
        """
        return {
            "progression_params": {
                transition_type: {
                    "mean_years": params.mean_years,
                    "std_years": params.std_years,
                    "min_years": params.min_years,
                    "max_years": params.max_years,
                    "age_adjustment_factor": params.age_adjustment_factor
                }
                for transition_type, params in self.progression_params.items()
            },
            "gender_multipliers": {
                "male_multiplier": self.gender_multipliers.male_multiplier,
                "female_multiplier": self.gender_multipliers.female_multiplier
            },
            "gender_specific_rates": {
                gender: {
                    "adenoma_initiation_rate": rates.adenoma_initiation_rate,
                    "low_to_high_progression_rate": rates.low_to_high_progression_rate,
                    "high_to_preclinical_rate": rates.high_to_preclinical_rate,
                    "preclinical_to_clinical_rate": rates.preclinical_to_clinical_rate,
                    "cancer_stage_progression_rate": rates.cancer_stage_progression_rate
                }
                for gender, rates in self.gender_specific_rates.items()
            },
            "gender_risk_weights": {
                gender: {
                    "smoking_weight": weights.smoking_weight,
                    "alcohol_weight": weights.alcohol_weight,
                    "obesity_weight": weights.obesity_weight,
                    "family_history_weight": weights.family_history_weight,
                    "inflammatory_bowel_disease_weight": weights.inflammatory_bowel_disease_weight
                }
                for gender, weights in self.gender_risk_weights.items()
            }
        }

    def update_gender_specific_rates(
        self,
        gender: str,
        new_rates: GenderSpecificProgressionRates
    ) -> None:
        """
        更新性别特异性进展率

        Args:
            gender: 性别
            new_rates: 新的性别特异性进展率
        """
        self.gender_specific_rates[gender] = new_rates
        logger.info(f"性别特异性进展率已更新: {gender} -> {new_rates}")

    def update_gender_risk_weights(
        self,
        gender: str,
        new_weights: GenderRiskFactorWeights
    ) -> None:
        """
        更新性别特异性风险因素权重

        Args:
            gender: 性别
            new_weights: 新的性别特异性风险因素权重
        """
        self.gender_risk_weights[gender] = new_weights
        logger.info(f"性别特异性风险因素权重已更新: {gender} -> {new_weights}")

    def get_gender_differences_summary(self) -> Dict[str, Dict[str, float]]:
        """
        获取性别差异摘要

        Returns:
            Dict: 性别差异摘要
        """
        summary = {}

        # 比较男性和女性的进展率
        if "male" in self.gender_specific_rates and "female" in self.gender_specific_rates:
            male_rates = self.gender_specific_rates["male"]
            female_rates = self.gender_specific_rates["female"]

            summary["progression_rate_ratios"] = {
                "adenoma_initiation": male_rates.adenoma_initiation_rate / female_rates.adenoma_initiation_rate,
                "low_to_high_progression": male_rates.low_to_high_progression_rate / female_rates.low_to_high_progression_rate,
                "high_to_preclinical": male_rates.high_to_preclinical_rate / female_rates.high_to_preclinical_rate,
                "preclinical_to_clinical": male_rates.preclinical_to_clinical_rate / female_rates.preclinical_to_clinical_rate,
                "cancer_stage_progression": male_rates.cancer_stage_progression_rate / female_rates.cancer_stage_progression_rate
            }

        # 比较风险因素权重
        if "male" in self.gender_risk_weights and "female" in self.gender_risk_weights:
            male_weights = self.gender_risk_weights["male"]
            female_weights = self.gender_risk_weights["female"]

            summary["risk_weight_ratios"] = {
                "smoking": male_weights.smoking_weight / female_weights.smoking_weight,
                "alcohol": male_weights.alcohol_weight / female_weights.alcohol_weight,
                "obesity": male_weights.obesity_weight / female_weights.obesity_weight,
                "family_history": male_weights.family_history_weight / female_weights.family_history_weight,
                "inflammatory_bowel_disease": male_weights.inflammatory_bowel_disease_weight / female_weights.inflammatory_bowel_disease_weight
            }

        return summary
