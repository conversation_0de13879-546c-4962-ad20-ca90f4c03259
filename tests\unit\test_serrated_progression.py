"""
测试锯齿状腺瘤进展模型

测试SerratedProgressionModel类的功能，包括进展时间分布、概率计算和状态转换。
"""

import pytest
import numpy as np
from unittest.mock import patch

from src.core.enums import DiseaseState, AnatomicalLocation
from src.modules.disease.serrated_progression import (
    ProgressionTimeDistribution,
    SerratedProgressionModel,
    SerratedAdenomaCharacteristics,
    SerratedAdenomaFeatureModel
)


class TestProgressionTimeDistribution:
    """测试进展时间分布"""
    
    def test_valid_distribution(self):
        """测试有效的分布创建"""
        dist = ProgressionTimeDistribution(
            mean=3.0,
            std=1.5,
            min_time=0.5,
            max_time=8.0
        )
        
        assert dist.mean == 3.0
        assert dist.std == 1.5
        assert dist.min_time == 0.5
        assert dist.max_time == 8.0
        assert dist.distribution_type == "normal"
    
    def test_invalid_parameters(self):
        """测试无效参数的验证"""
        # 负平均时间
        with pytest.raises(ValueError, match="平均时间必须大于0"):
            ProgressionTimeDistribution(mean=-1.0, std=1.0, min_time=0.0, max_time=5.0)
        
        # 负标准差
        with pytest.raises(ValueError, match="标准差必须大于0"):
            ProgressionTimeDistribution(mean=3.0, std=-1.0, min_time=0.0, max_time=5.0)
        
        # 负最小时间
        with pytest.raises(ValueError, match="最小时间不能为负数"):
            ProgressionTimeDistribution(mean=3.0, std=1.0, min_time=-1.0, max_time=5.0)
        
        # 最大时间小于等于最小时间
        with pytest.raises(ValueError, match="最大时间必须大于最小时间"):
            ProgressionTimeDistribution(mean=3.0, std=1.0, min_time=5.0, max_time=3.0)
    
    def test_time_sampling(self):
        """测试时间采样"""
        dist = ProgressionTimeDistribution(
            mean=3.0,
            std=1.0,
            min_time=1.0,
            max_time=6.0
        )
        
        # 测试多次采样
        samples = [dist.sample_time(random_seed=42) for _ in range(100)]
        
        # 检查采样结果在有效范围内
        assert all(1.0 <= sample <= 6.0 for sample in samples)
        
        # 检查采样结果的统计特性（大致符合预期）
        mean_sample = np.mean(samples)
        assert 2.0 <= mean_sample <= 4.0  # 允许一定偏差
    
    def test_lognormal_distribution(self):
        """测试对数正态分布"""
        dist = ProgressionTimeDistribution(
            mean=2.0,
            std=0.5,
            min_time=0.5,
            max_time=10.0,
            distribution_type="lognormal"
        )
        
        samples = [dist.sample_time(random_seed=42) for _ in range(50)]
        assert all(0.5 <= sample <= 10.0 for sample in samples)
    
    def test_unsupported_distribution(self):
        """测试不支持的分布类型"""
        dist = ProgressionTimeDistribution(
            mean=3.0,
            std=1.0,
            min_time=1.0,
            max_time=6.0,
            distribution_type="unsupported"
        )
        
        with pytest.raises(ValueError, match="不支持的分布类型"):
            dist.sample_time()


class TestSerratedProgressionModel:
    """测试锯齿状腺瘤进展模型"""
    
    def test_initialization(self):
        """测试模型初始化"""
        model = SerratedProgressionModel(random_seed=42)
        
        assert model.random_seed == 42
        assert "normal_to_small_serrated" in model.progression_times
        assert "small_serrated_to_large_serrated" in model.progression_times
        assert "large_serrated_to_preclinical_cancer" in model.progression_times

        assert "normal_to_small_serrated" in model.progression_probabilities
        assert "small_serrated_to_large_serrated" in model.progression_probabilities
        assert "large_serrated_to_preclinical_cancer" in model.progression_probabilities
    
    def test_progression_time_retrieval(self):
        """测试进展时间获取"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 测试有效的状态转换
        time = model.get_progression_time("normal", "small_serrated")
        assert isinstance(time, float)
        assert time > 0
        
        time = model.get_progression_time("small_serrated", "large_serrated")
        assert isinstance(time, float)
        assert time > 0
        
        # 测试无效的状态转换
        with pytest.raises(ValueError, match="未定义的状态转换"):
            model.get_progression_time("invalid_state", "another_invalid_state")
    
    def test_progression_probability_calculation(self):
        """测试进展概率计算"""
        model = SerratedProgressionModel()
        
        # 测试年进展概率
        prob = model.get_progression_probability("normal", "small_serrated", time_step=1.0)
        assert 0 <= prob <= 1
        expected_prob = model.progression_probabilities["normal_to_small_serrated"]
        assert abs(prob - expected_prob) < 1e-10
        
        # 测试半年进展概率
        prob_half_year = model.get_progression_probability("normal", "small_serrated", time_step=0.5)
        assert 0 <= prob_half_year <= 1
        assert prob_half_year < prob  # 半年概率应该小于一年概率
        
        # 测试未定义的转换
        prob_undefined = model.get_progression_probability("invalid", "state", time_step=1.0)
        assert prob_undefined == 0.0
    
    def test_progression_decision(self):
        """测试进展决策"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 测试多次决策的一致性
        decisions = []
        for _ in range(100):
            decision = model.should_progress("normal", "small_serrated", time_step=1.0)
            decisions.append(decision)
        
        # 应该有一些True和一些False（基于概率）
        true_count = sum(decisions)
        assert 0 < true_count < 100  # 不应该全是True或全是False
    
    def test_next_state_determination(self):
        """测试下一状态确定"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 测试正常状态
        next_state = model.get_next_state(DiseaseState.NORMAL, time_step=1.0)
        assert next_state in [DiseaseState.NORMAL, DiseaseState.SMALL_SERRATED]
        
        # 测试小锯齿状腺瘤状态
        next_state = model.get_next_state(DiseaseState.SMALL_SERRATED, time_step=1.0)
        assert next_state in [
            DiseaseState.SMALL_SERRATED,
            DiseaseState.LARGE_SERRATED,
            DiseaseState.NORMAL
        ]
        
        # 测试大锯齿状腺瘤状态
        next_state = model.get_next_state(DiseaseState.LARGE_SERRATED, time_step=1.0)
        assert next_state in [
            DiseaseState.LARGE_SERRATED,
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.NORMAL
        ]
    
    def test_progression_timeline_simulation(self):
        """测试进展时间线模拟"""
        model = SerratedProgressionModel(random_seed=42)
        
        timeline = model.simulate_progression_timeline(
            start_state=DiseaseState.NORMAL,
            max_time=20.0
        )
        
        assert "timeline" in timeline
        assert "final_state" in timeline
        assert "total_time" in timeline
        
        assert isinstance(timeline["timeline"], list)
        assert len(timeline["timeline"]) > 0
        
        # 检查时间线的第一个条目
        first_entry = timeline["timeline"][0]
        assert first_entry["time"] == 0.0
        assert first_entry["state"] == "normal"
        assert first_entry["pathway"] == "serrated_adenoma"
        
        # 检查总时间不超过最大时间（允许小的数值误差）
        assert timeline["total_time"] <= 20.1
    
    def test_progression_statistics(self):
        """测试进展统计信息"""
        model = SerratedProgressionModel()
        
        stats = model.get_progression_statistics()
        
        assert "progression_times" in stats
        assert "progression_probabilities" in stats
        assert "regression_probabilities" in stats
        
        # 检查进展时间统计
        assert "normal_to_small_serrated" in stats["progression_times"]
        time_stats = stats["progression_times"]["normal_to_small_serrated"]
        assert "mean" in time_stats
        assert "std" in time_stats
        assert "min" in time_stats
        assert "max" in time_stats
        
        # 检查概率统计
        assert "normal_to_small_serrated" in stats["progression_probabilities"]
        assert "small_serrated_to_normal" in stats["regression_probabilities"]
    
    def test_parameter_updates(self):
        """测试参数更新"""
        model = SerratedProgressionModel()
        
        # 更新进展时间参数
        updates = {
            "progression_times": {
                "normal_to_small_serrated": {
                    "mean": 4.0,
                    "std": 2.0
                }
            },
            "progression_probabilities": {
                "normal_to_small_serrated": 0.03
            }
        }
        
        model.update_progression_parameters(updates)
        
        # 验证更新
        assert model.progression_times["normal_to_small_serrated"].mean == 4.0
        assert model.progression_times["normal_to_small_serrated"].std == 2.0
        assert model.progression_probabilities["normal_to_small_serrated"] == 0.03
    
    def test_parameter_validation(self):
        """测试参数验证"""
        model = SerratedProgressionModel()
        
        # 有效参数应该通过验证
        assert model.validate_parameters() is True
        
        # 设置无效概率
        model.progression_probabilities["normal_to_small_serrated"] = 1.5  # 超出范围
        assert model.validate_parameters() is False
        
        # 恢复有效概率
        model.progression_probabilities["normal_to_small_serrated"] = 0.02
        assert model.validate_parameters() is True
    
    def test_progression_time_consistency(self):
        """测试进展时间的一致性"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 使用相同种子应该产生相同结果
        time1 = model.get_progression_time("normal", "small_serrated")
        
        model2 = SerratedProgressionModel(random_seed=42)
        time2 = model2.get_progression_time("normal", "small_serrated")
        
        assert abs(time1 - time2) < 1e-10  # 应该完全相同
    
    def test_progression_probability_bounds(self):
        """测试进展概率边界"""
        model = SerratedProgressionModel()
        
        # 测试所有定义的转换概率
        for transition in model.progression_probabilities:
            prob = model.progression_probabilities[transition]
            assert 0 <= prob <= 1, f"概率 {transition} 超出范围: {prob}"
        
        for transition in model.regression_probabilities:
            prob = model.regression_probabilities[transition]
            assert 0 <= prob <= 1, f"概率 {transition} 超出范围: {prob}"


class TestSerratedAdenomaCharacteristics:
    """测试锯齿状腺瘤特征"""

    def test_valid_characteristics(self):
        """测试有效特征创建"""
        characteristics = SerratedAdenomaCharacteristics(
            size_mm=8.5,
            anatomical_location=AnatomicalLocation.PROXIMAL_COLON,
            detection_difficulty=0.6,
            malignant_potential=0.3,
            morphology_score=0.8,
            treatment_response=0.7
        )

        assert characteristics.size_mm == 8.5
        assert characteristics.anatomical_location == AnatomicalLocation.PROXIMAL_COLON
        assert characteristics.detection_difficulty == 0.6
        assert characteristics.malignant_potential == 0.3
        assert characteristics.morphology_score == 0.8
        assert characteristics.treatment_response == 0.7

    def test_invalid_characteristics(self):
        """测试无效特征验证"""
        # 负数大小
        with pytest.raises(ValueError, match="腺瘤大小不能为负数"):
            SerratedAdenomaCharacteristics(
                size_mm=-1.0,
                anatomical_location=AnatomicalLocation.PROXIMAL_COLON,
                detection_difficulty=0.5,
                malignant_potential=0.3,
                morphology_score=0.8,
                treatment_response=0.7
            )

        # 检测难度超出范围
        with pytest.raises(ValueError, match="检测难度系数必须在0-1范围内"):
            SerratedAdenomaCharacteristics(
                size_mm=8.0,
                anatomical_location=AnatomicalLocation.PROXIMAL_COLON,
                detection_difficulty=1.5,
                malignant_potential=0.3,
                morphology_score=0.8,
                treatment_response=0.7
            )


class TestSerratedAdenomaFeatureModel:
    """测试锯齿状腺瘤特征模型"""

    def test_initialization(self):
        """测试模型初始化"""
        model = SerratedAdenomaFeatureModel()

        # 检查位置概率
        assert len(model.location_probabilities) == 3
        assert AnatomicalLocation.PROXIMAL_COLON in model.location_probabilities
        assert model.location_probabilities[AnatomicalLocation.PROXIMAL_COLON] == 0.65

        # 检查筛查特征
        assert "colonoscopy_sensitivity" in model.screening_characteristics
        assert "fit_sensitivity" in model.screening_characteristics

        # 检查大小分布
        assert "small_serrated" in model.size_distribution
        assert "large_serrated" in model.size_distribution

    def test_characteristics_generation(self):
        """测试特征生成"""
        model = SerratedAdenomaFeatureModel()

        # 生成小锯齿状腺瘤特征
        characteristics = model.generate_characteristics(
            DiseaseState.SMALL_SERRATED,
            individual_age=60,
            random_seed=42
        )

        assert isinstance(characteristics, SerratedAdenomaCharacteristics)
        assert characteristics.size_mm > 0
        assert characteristics.anatomical_location in [
            AnatomicalLocation.PROXIMAL_COLON,
            AnatomicalLocation.DISTAL_COLON,
            AnatomicalLocation.RECTUM
        ]
        assert 0 <= characteristics.detection_difficulty <= 1
        assert 0 <= characteristics.malignant_potential <= 1
        assert 0 <= characteristics.morphology_score <= 1
        assert 0 <= characteristics.treatment_response <= 1

    def test_size_sampling(self):
        """测试大小采样"""
        model = SerratedAdenomaFeatureModel()

        # 测试小锯齿状腺瘤大小
        small_sizes = [
            model._sample_size(DiseaseState.SMALL_SERRATED)
            for _ in range(100)
        ]
        assert all(2.0 <= size <= 9.9 for size in small_sizes)

        # 测试大锯齿状腺瘤大小
        large_sizes = [
            model._sample_size(DiseaseState.LARGE_SERRATED)
            for _ in range(100)
        ]
        assert all(10.0 <= size <= 30.0 for size in large_sizes)

    def test_anatomical_location_sampling(self):
        """测试解剖位置采样"""
        model = SerratedAdenomaFeatureModel()

        # 采样多次检查分布
        locations = [model._sample_anatomical_location() for _ in range(1000)]

        # 计算各位置比例
        proximal_count = sum(1 for loc in locations if loc == AnatomicalLocation.PROXIMAL_COLON)
        distal_count = sum(1 for loc in locations if loc == AnatomicalLocation.DISTAL_COLON)
        rectum_count = sum(1 for loc in locations if loc == AnatomicalLocation.RECTUM)

        # 检查比例是否接近预期（允许10%偏差）
        assert 0.55 <= proximal_count / 1000 <= 0.75  # 预期65%
        assert 0.15 <= distal_count / 1000 <= 0.35    # 预期25%
        assert 0.05 <= rectum_count / 1000 <= 0.15    # 预期10%

    def test_detection_difficulty_calculation(self):
        """测试检测难度计算"""
        model = SerratedAdenomaFeatureModel()

        # 小腺瘤在近端结肠应该更难检测
        small_proximal_difficulty = model._calculate_detection_difficulty(
            5.0, AnatomicalLocation.PROXIMAL_COLON
        )

        # 大腺瘤在直肠应该较容易检测
        large_rectum_difficulty = model._calculate_detection_difficulty(
            15.0, AnatomicalLocation.RECTUM
        )

        assert small_proximal_difficulty > large_rectum_difficulty
        assert 0 <= small_proximal_difficulty <= 1
        assert 0 <= large_rectum_difficulty <= 1

    def test_malignant_potential_calculation(self):
        """测试恶变潜能计算"""
        model = SerratedAdenomaFeatureModel()

        # 大腺瘤应该有更高的恶变潜能
        large_potential = model._calculate_malignant_potential(
            15.0, AnatomicalLocation.PROXIMAL_COLON, age=70
        )

        small_potential = model._calculate_malignant_potential(
            5.0, AnatomicalLocation.RECTUM, age=50
        )

        assert large_potential > small_potential
        assert 0 <= large_potential <= 1
        assert 0 <= small_potential <= 1

    def test_screening_sensitivity(self):
        """测试筛查敏感性"""
        model = SerratedAdenomaFeatureModel()

        # 创建测试特征
        characteristics = SerratedAdenomaCharacteristics(
            size_mm=8.0,
            anatomical_location=AnatomicalLocation.PROXIMAL_COLON,
            detection_difficulty=0.6,
            malignant_potential=0.3,
            morphology_score=0.8,
            treatment_response=0.7
        )

        # 测试不同筛查方法的敏感性
        colonoscopy_sensitivity = model.get_screening_sensitivity("colonoscopy", characteristics)
        fit_sensitivity = model.get_screening_sensitivity("fit", characteristics)

        assert 0 <= colonoscopy_sensitivity <= 1
        assert 0 <= fit_sensitivity <= 1
        assert colonoscopy_sensitivity > fit_sensitivity  # 结肠镜应该比FIT敏感性更高

    def test_location_distribution(self):
        """测试位置分布获取"""
        model = SerratedAdenomaFeatureModel()

        distribution = model.get_location_distribution()

        assert len(distribution) == 3
        assert sum(distribution.values()) == 1.0  # 概率和为1
        assert distribution[AnatomicalLocation.PROXIMAL_COLON] == 0.65

    def test_screening_characteristics(self):
        """测试筛查特征获取"""
        model = SerratedAdenomaFeatureModel()

        characteristics = model.get_screening_characteristics()

        assert "colonoscopy_sensitivity" in characteristics
        assert "fit_sensitivity" in characteristics
        assert "sigmoidoscopy_sensitivity" in characteristics
        assert characteristics["colonoscopy_sensitivity"] == 0.75
