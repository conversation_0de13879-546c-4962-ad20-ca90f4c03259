"""
筛查结果统计分析服务单元测试

测试筛查结果统计分析、性能指标计算、趋势分析和质量监控功能。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock

from src.services.screening_analytics import (
    ScreeningAnalytics, QualityAlert, QualityAlertSeverity,
    PerformanceMetrics, ScreeningStatistics
)
from src.modules.screening import (
    DetailedScreeningResult, ScreeningResultType, ScreeningToolType,
    FalseResultType, FalseResultImpact
)


class TestScreeningAnalytics:
    """筛查结果统计分析服务测试类"""

    @pytest.fixture
    def analytics_service(self):
        """创建筛查分析服务"""
        return ScreeningAnalytics()

    @pytest.fixture
    def sample_screening_results(self):
        """创建样本筛查结果"""
        base_date = datetime.now()
        results = []
        
        # 创建100个筛查结果
        for i in range(100):
            result_type = ScreeningResultType.POSITIVE if i < 10 else ScreeningResultType.NEGATIVE
            is_true_positive = True if i < 8 else (False if i < 10 else None)  # 8个真阳性，2个假阳性
            is_true_negative = True if i >= 10 and i < 95 else (False if i >= 95 else None)  # 85个真阴性，5个假阴性
            
            # 假结果影响
            false_impact = None
            if i == 8 or i == 9:  # 假阳性
                false_impact = FalseResultImpact(
                    result_type=FalseResultType.FALSE_POSITIVE,
                    psychological_impact_score=0.6,
                    additional_cost=1200.0,
                    unnecessary_procedures=1,
                    anxiety_duration_days=14
                )
            elif i >= 95:  # 假阴性
                false_impact = FalseResultImpact(
                    result_type=FalseResultType.FALSE_NEGATIVE,
                    psychological_impact_score=0.8,
                    additional_cost=500.0,
                    delay_in_detection_days=180
                )
            
            result = DetailedScreeningResult(
                individual_id=f"individual_{i}",
                tool_type=ScreeningToolType.FIT if i < 50 else ScreeningToolType.COLONOSCOPY,
                result_type=result_type,
                test_date=base_date - timedelta(days=i),
                confidence_score=0.8 + (i % 20) * 0.01,
                quality_score=0.9 + (i % 10) * 0.01,
                is_true_positive=is_true_positive,
                is_true_negative=is_true_negative,
                processing_time=5.0 + i * 0.1,
                cost=50.0 if i < 50 else 1200.0,
                false_result_impact=false_impact
            )
            
            results.append(result)
        
        return results

    def test_analytics_service_initialization(self):
        """测试分析服务初始化"""
        service = ScreeningAnalytics()
        
        assert service.quality_thresholds is not None
        assert 'max_positivity_rate' in service.quality_thresholds
        assert 'min_sensitivity' in service.quality_thresholds

    def test_summary_statistics_calculation(
        self, analytics_service, sample_screening_results
    ):
        """测试汇总统计计算"""
        stats = analytics_service._calculate_summary_stats(sample_screening_results)
        
        assert isinstance(stats, ScreeningStatistics)
        assert stats.total_screenings == 100
        assert stats.positive_rate == 0.1  # 10个阳性结果
        assert stats.negative_rate == 0.9   # 90个阴性结果
        assert stats.average_confidence_score > 0
        assert stats.average_quality_score > 0
        assert stats.total_cost > 0

    def test_performance_metrics_calculation(
        self, analytics_service, sample_screening_results
    ):
        """测试性能指标计算"""
        metrics = analytics_service._calculate_performance_metrics(sample_screening_results)
        
        assert isinstance(metrics, PerformanceMetrics)
        assert metrics.true_positives == 8
        assert metrics.true_negatives == 85
        assert metrics.false_positives == 2
        assert metrics.false_negatives == 5
        assert metrics.total_tests == 100
        
        # 验证计算的指标
        assert metrics.sensitivity == 8 / (8 + 5)  # TP / (TP + FN)
        assert metrics.specificity == 85 / (85 + 2)  # TN / (TN + FP)
        assert metrics.positive_predictive_value == 8 / (8 + 2)  # TP / (TP + FP)

    def test_temporal_trends_analysis(
        self, analytics_service, sample_screening_results
    ):
        """测试时间趋势分析"""
        trends = analytics_service._analyze_temporal_trends(sample_screening_results)
        
        assert 'monthly_volume' in trends
        assert 'monthly_positivity_rate' in trends
        assert 'monthly_quality_score' in trends
        assert 'monthly_cost' in trends
        
        # 验证有月度数据
        assert len(trends['monthly_volume']) > 0

    def test_screening_tools_comparison(
        self, analytics_service, sample_screening_results
    ):
        """测试筛查工具比较"""
        comparison = analytics_service._compare_screening_tools(sample_screening_results)
        
        assert 'fecal_immunochemical_test' in comparison
        assert 'colonoscopy' in comparison
        
        fit_data = comparison['fecal_immunochemical_test']
        assert 'total_tests' in fit_data
        assert 'positivity_rate' in fit_data
        assert 'sensitivity' in fit_data
        assert 'average_cost' in fit_data

    def test_quality_indicators_assessment(
        self, analytics_service, sample_screening_results
    ):
        """测试质量指标评估"""
        quality = analytics_service._assess_quality_indicators(sample_screening_results)
        
        assert 'inadequate_sample_rate' in quality
        assert 'low_quality_rate' in quality
        assert 'low_confidence_rate' in quality
        assert 'average_processing_time' in quality
        assert 'quality_score_distribution' in quality

    def test_cost_analysis(self, analytics_service, sample_screening_results):
        """测试成本分析"""
        cost_analysis = analytics_service._analyze_costs(sample_screening_results)
        
        assert 'total_cost' in cost_analysis
        assert 'average_cost' in cost_analysis
        assert 'cost_by_tool' in cost_analysis
        assert 'false_result_costs' in cost_analysis
        
        # 验证按工具分类的成本
        assert 'fecal_immunochemical_test' in cost_analysis['cost_by_tool']
        assert 'colonoscopy' in cost_analysis['cost_by_tool']

    def test_false_results_analysis(
        self, analytics_service, sample_screening_results
    ):
        """测试假结果分析"""
        false_analysis = analytics_service._analyze_false_results(sample_screening_results)
        
        assert 'total_false_results' in false_analysis
        assert 'false_positive_count' in false_analysis
        assert 'false_negative_count' in false_analysis
        assert 'false_result_rate' in false_analysis
        
        # 验证假阳性和假阴性分析
        assert 'false_positive_analysis' in false_analysis
        assert 'false_negative_analysis' in false_analysis

    def test_quality_monitoring(self, analytics_service, sample_screening_results):
        """测试质量监控"""
        alerts = analytics_service.monitor_screening_quality(sample_screening_results)
        
        # 应该没有严重的质量问题
        assert isinstance(alerts, list)
        
        # 测试高阳性率警报
        high_positive_results = [
            DetailedScreeningResult(
                individual_id=f"hp_{i}",
                tool_type=ScreeningToolType.FIT,
                result_type=ScreeningResultType.POSITIVE,
                test_date=datetime.now(),
                confidence_score=0.8,
                is_true_positive=False  # 假阳性
            )
            for i in range(20)  # 20个阳性结果，阳性率20%
        ]
        
        alerts = analytics_service.monitor_screening_quality(high_positive_results)
        assert len(alerts) > 0
        assert any(alert.alert_type == "HIGH_POSITIVITY_RATE" for alert in alerts)

    def test_quality_dashboard_generation(
        self, analytics_service, sample_screening_results
    ):
        """测试质量监控仪表板生成"""
        dashboard = analytics_service.generate_quality_dashboard(sample_screening_results)
        
        assert 'overview' in dashboard
        assert 'performance_summary' in dashboard
        assert 'quality_alerts' in dashboard
        assert 'trend_indicators' in dashboard
        assert 'recommendations' in dashboard
        
        # 验证概览数据
        overview = dashboard['overview']
        assert overview['total_tests'] == 100
        assert 'positivity_rate' in overview
        assert 'average_quality_score' in overview

    def test_trend_indicators_calculation(
        self, analytics_service, sample_screening_results
    ):
        """测试趋势指标计算"""
        trends = analytics_service._calculate_trend_indicators(sample_screening_results)
        
        assert 'positivity_rate' in trends
        assert 'quality_score' in trends
        
        # 趋势值应该是预定义的字符串
        assert trends['positivity_rate'] in ["上升", "下降", "稳定"]
        assert trends['quality_score'] in ["改善", "恶化", "稳定"]

    def test_quality_recommendations_generation(self, analytics_service):
        """测试质量改进建议生成"""
        # 创建一些警报
        alerts = [
            QualityAlert(
                alert_type="HIGH_POSITIVITY_RATE",
                message="阳性率过高",
                severity=QualityAlertSeverity.WARNING
            ),
            QualityAlert(
                alert_type="LOW_SENSITIVITY",
                message="敏感性过低",
                severity=QualityAlertSeverity.ERROR
            )
        ]
        
        # 创建性能指标
        metrics = PerformanceMetrics(
            sensitivity=0.75,
            specificity=0.85,
            positive_predictive_value=0.6,
            negative_predictive_value=0.95,
            accuracy=0.8,
            true_positives=75,
            true_negatives=85,
            false_positives=15,
            false_negatives=25,
            total_tests=200
        )
        
        # 创建统计数据
        stats = ScreeningStatistics(
            total_screenings=200,
            positive_rate=0.45,
            negative_rate=0.55,
            indeterminate_rate=0.0,
            inadequate_rate=0.04,
            average_confidence_score=0.8,
            average_quality_score=0.85,
            average_cost=300.0,
            total_cost=60000.0
        )
        
        recommendations = analytics_service._generate_quality_recommendations(
            alerts, metrics, stats
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        assert all(isinstance(rec, str) for rec in recommendations)

    def test_generate_screening_report(
        self, analytics_service, sample_screening_results
    ):
        """测试生成筛查结果分析报告"""
        report = analytics_service.generate_screening_report(sample_screening_results)
        
        assert 'report_metadata' in report
        assert 'summary_statistics' in report
        assert 'performance_metrics' in report
        assert 'temporal_trends' in report
        assert 'tool_comparison' in report
        assert 'quality_indicators' in report
        assert 'cost_analysis' in report
        assert 'false_result_analysis' in report
        
        # 验证报告元数据
        metadata = report['report_metadata']
        assert metadata['total_results'] == 100
        assert 'generation_time' in metadata

    def test_time_period_filtering(self, analytics_service, sample_screening_results):
        """测试时间段过滤"""
        # 设置时间段过滤
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        time_period = (start_date, end_date)
        
        report = analytics_service.generate_screening_report(
            sample_screening_results, time_period
        )
        
        # 应该只包含最近30天的结果
        assert report['report_metadata']['total_results'] <= 30

    def test_empty_results_handling(self, analytics_service):
        """测试空结果处理"""
        empty_results = []
        
        # 测试各种方法对空结果的处理
        report = analytics_service.generate_screening_report(empty_results)
        assert "error" in report
        
        alerts = analytics_service.monitor_screening_quality(empty_results)
        assert len(alerts) == 0
        
        dashboard = analytics_service.generate_quality_dashboard(empty_results)
        assert "error" in dashboard

    def test_quality_alert_creation(self):
        """测试质量警报创建"""
        alert = QualityAlert(
            alert_type="TEST_ALERT",
            message="测试警报",
            severity=QualityAlertSeverity.WARNING,
            affected_count=10,
            threshold_value=0.1,
            actual_value=0.15
        )
        
        assert alert.alert_type == "TEST_ALERT"
        assert alert.severity == QualityAlertSeverity.WARNING
        assert alert.affected_count == 10
        assert alert.threshold_value == 0.1
        assert alert.actual_value == 0.15

    def test_distribution_calculation(self, analytics_service):
        """测试数值分布计算"""
        values = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0]
        
        distribution = analytics_service._calculate_distribution(values)
        
        assert 'mean' in distribution
        assert 'median' in distribution
        assert 'std_dev' in distribution
        assert 'min' in distribution
        assert 'max' in distribution
        assert 'q25' in distribution
        assert 'q75' in distribution
        
        assert distribution['mean'] == 5.5
        assert distribution['median'] == 5.5
        assert distribution['min'] == 1.0
        assert distribution['max'] == 10.0
