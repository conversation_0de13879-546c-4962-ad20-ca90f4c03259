"""
成本调整和折现功能

实现成本的通胀调整、折现计算和货币转换功能。
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union
import logging
import yaml
import json
from pathlib import Path
import math

logger = logging.getLogger(__name__)


@dataclass
class InflationData:
    """通胀数据结构"""
    year: int
    inflation_rate: float
    cpi_index: float = 100.0  # 消费者价格指数，基准年为100


@dataclass
class CurrencyRate:
    """汇率数据结构"""
    from_currency: str
    to_currency: str
    exchange_rate: float
    date: str


class CostAdjustmentEngine:
    """成本调整引擎类"""
    
    def __init__(
        self, 
        base_year: int = 2023, 
        discount_rate: float = 0.03,
        base_currency: str = "CNY"
    ):
        """
        初始化成本调整引擎
        
        Args:
            base_year: 基准年份
            discount_rate: 年度折现率（默认3%）
            base_currency: 基础货币单位
        """
        self.base_year = base_year
        self.discount_rate = discount_rate
        self.base_currency = base_currency
        self.inflation_data: Dict[int, InflationData] = {}
        self.currency_rates: Dict[str, CurrencyRate] = {}
        
        # 加载默认通胀数据
        self._load_default_inflation_data()
    
    def _load_default_inflation_data(self) -> None:
        """加载默认通胀数据（中国2010-2030年）"""
        default_inflation_rates = {
            2010: 0.032, 2011: 0.055, 2012: 0.026, 2013: 0.021, 2014: 0.020,
            2015: 0.014, 2016: 0.020, 2017: 0.021, 2018: 0.021, 2019: 0.029,
            2020: 0.025, 2021: 0.009, 2022: 0.020, 2023: 0.002, 2024: 0.030,
            2025: 0.030, 2026: 0.030, 2027: 0.030, 2028: 0.030, 2029: 0.030, 2030: 0.030
        }
        
        cpi_base = 100.0
        for year, rate in default_inflation_rates.items():
            if year == 2023:  # 基准年
                cpi_index = 100.0
            elif year < 2023:
                # 计算历史CPI
                years_diff = 2023 - year
                cpi_index = 100.0
                for y in range(year, 2023):
                    cpi_index /= (1 + default_inflation_rates.get(y + 1, 0.03))
            else:
                # 计算未来CPI
                cpi_index = 100.0
                for y in range(2023, year):
                    cpi_index *= (1 + default_inflation_rates.get(y + 1, 0.03))
            
            self.inflation_data[year] = InflationData(
                year=year,
                inflation_rate=rate,
                cpi_index=cpi_index
            )
    
    def load_inflation_data(self, data_file: str) -> None:
        """
        从文件加载通胀数据
        
        Args:
            data_file: 通胀数据文件路径
        """
        try:
            data_path = Path(data_file)
            
            if data_path.suffix.lower() in ['.yaml', '.yml']:
                with open(data_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
            elif data_path.suffix.lower() == '.json':
                with open(data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                raise ValueError(f"不支持的数据文件格式: {data_path.suffix}")
            
            inflation_data = data.get('inflation_data', {})
            for year_str, year_data in inflation_data.items():
                year = int(year_str)
                self.inflation_data[year] = InflationData(
                    year=year,
                    inflation_rate=year_data.get('inflation_rate', 0.03),
                    cpi_index=year_data.get('cpi_index', 100.0)
                )
            
            logger.info(f"成功加载通胀数据: {data_file}")
            
        except Exception as e:
            logger.error(f"加载通胀数据失败: {e}")
            raise
    
    def adjust_for_inflation(
        self, 
        cost: float, 
        from_year: int, 
        to_year: int
    ) -> float:
        """
        通胀调整
        
        Args:
            cost: 原始成本
            from_year: 起始年份
            to_year: 目标年份
            
        Returns:
            调整后的成本
        """
        if from_year == to_year:
            return cost
        
        # 获取CPI指数
        from_cpi = self.inflation_data.get(from_year, InflationData(from_year, 0.03)).cpi_index
        to_cpi = self.inflation_data.get(to_year, InflationData(to_year, 0.03)).cpi_index
        
        # 如果没有确切的CPI数据，使用累积通胀率计算
        if from_year not in self.inflation_data or to_year not in self.inflation_data:
            return self._calculate_inflation_adjustment_by_rate(cost, from_year, to_year)
        
        # 使用CPI指数调整
        adjustment_factor = to_cpi / from_cpi
        adjusted_cost = cost * adjustment_factor
        
        logger.debug(f"通胀调整: {cost:.2f} ({from_year}) -> {adjusted_cost:.2f} ({to_year})")
        return adjusted_cost
    
    def _calculate_inflation_adjustment_by_rate(
        self, 
        cost: float, 
        from_year: int, 
        to_year: int
    ) -> float:
        """使用通胀率计算调整"""
        cumulative_inflation = 1.0
        start_year = min(from_year, to_year)
        end_year = max(from_year, to_year)
        
        for year in range(start_year, end_year):
            inflation_rate = self.inflation_data.get(year, InflationData(year, 0.03)).inflation_rate
            cumulative_inflation *= (1 + inflation_rate)
        
        if from_year < to_year:
            return cost * cumulative_inflation
        else:
            return cost / cumulative_inflation
    
    def present_value(
        self, 
        future_cost: float, 
        years_in_future: float,
        discount_rate: Optional[float] = None
    ) -> float:
        """
        计算现值
        
        Args:
            future_cost: 未来成本
            years_in_future: 未来年数
            discount_rate: 折现率（可选，默认使用实例设置）
            
        Returns:
            现值
        """
        rate = discount_rate if discount_rate is not None else self.discount_rate
        
        if years_in_future == 0:
            return future_cost
        
        present_value = future_cost / ((1 + rate) ** years_in_future)
        
        logger.debug(f"现值计算: {future_cost:.2f} (未来{years_in_future}年) -> {present_value:.2f}")
        return present_value
    
    def future_value(
        self, 
        present_cost: float, 
        years_to_future: float,
        discount_rate: Optional[float] = None
    ) -> float:
        """
        计算未来值
        
        Args:
            present_cost: 现在成本
            years_to_future: 到未来的年数
            discount_rate: 折现率（可选）
            
        Returns:
            未来值
        """
        rate = discount_rate if discount_rate is not None else self.discount_rate
        
        if years_to_future == 0:
            return present_cost
        
        future_value = present_cost * ((1 + rate) ** years_to_future)
        return future_value
    
    def net_present_value(
        self, 
        cost_stream: List[Tuple[float, int]],
        discount_rate: Optional[float] = None
    ) -> float:
        """
        计算净现值
        
        Args:
            cost_stream: 成本流列表 [(成本, 年份), ...]
            discount_rate: 折现率（可选）
            
        Returns:
            净现值
        """
        rate = discount_rate if discount_rate is not None else self.discount_rate
        npv = 0.0
        
        for cost, year in cost_stream:
            years_from_base = year - self.base_year
            pv = self.present_value(cost, years_from_base, rate)
            npv += pv
        
        logger.debug(f"净现值计算: {len(cost_stream)}项成本流 -> NPV: {npv:.2f}")
        return npv
    
    def annualized_cost(
        self, 
        total_cost: float, 
        years: int,
        discount_rate: Optional[float] = None
    ) -> float:
        """
        计算年化成本
        
        Args:
            total_cost: 总成本
            years: 年数
            discount_rate: 折现率（可选）
            
        Returns:
            年化成本
        """
        rate = discount_rate if discount_rate is not None else self.discount_rate
        
        if years <= 0:
            return total_cost
        
        if rate == 0:
            return total_cost / years

        # 使用年金公式，添加除零保护
        denominator = (1 + rate) ** years - 1
        if abs(denominator) < 1e-10:  # 防止除零错误
            return total_cost / years

        annuity_factor = (rate * (1 + rate) ** years) / denominator
        annualized_cost = total_cost * annuity_factor

        return annualized_cost

    def convert_currency(
        self,
        amount: float,
        from_currency: str,
        to_currency: str,
        exchange_rate: Optional[float] = None
    ) -> float:
        """
        货币转换

        Args:
            amount: 金额
            from_currency: 源货币
            to_currency: 目标货币
            exchange_rate: 汇率（可选，如果不提供则使用内置汇率）

        Returns:
            转换后的金额
        """
        if from_currency == to_currency:
            return amount

        if exchange_rate is not None:
            converted_amount = amount * exchange_rate
        else:
            # 使用内置汇率
            rate_key = f"{from_currency}_{to_currency}"
            if rate_key in self.currency_rates:
                rate = self.currency_rates[rate_key].exchange_rate
                converted_amount = amount * rate
            else:
                # 如果没有直接汇率，尝试通过基础货币转换
                base_rate_key = f"{from_currency}_{self.base_currency}"
                target_rate_key = f"{self.base_currency}_{to_currency}"

                if base_rate_key in self.currency_rates and target_rate_key in self.currency_rates:
                    base_rate = self.currency_rates[base_rate_key].exchange_rate
                    target_rate = self.currency_rates[target_rate_key].exchange_rate
                    converted_amount = amount * base_rate * target_rate
                else:
                    raise ValueError(f"无法找到汇率: {from_currency} -> {to_currency}")

        logger.debug(f"货币转换: {amount:.2f} {from_currency} -> {converted_amount:.2f} {to_currency}")
        return converted_amount

    def add_currency_rate(
        self,
        from_currency: str,
        to_currency: str,
        exchange_rate: float,
        date: str = ""
    ) -> None:
        """
        添加汇率

        Args:
            from_currency: 源货币
            to_currency: 目标货币
            exchange_rate: 汇率
            date: 汇率日期
        """
        rate_key = f"{from_currency}_{to_currency}"
        self.currency_rates[rate_key] = CurrencyRate(
            from_currency=from_currency,
            to_currency=to_currency,
            exchange_rate=exchange_rate,
            date=date
        )

        # 同时添加反向汇率
        reverse_key = f"{to_currency}_{from_currency}"
        self.currency_rates[reverse_key] = CurrencyRate(
            from_currency=to_currency,
            to_currency=from_currency,
            exchange_rate=1.0 / exchange_rate,
            date=date
        )

        logger.info(f"添加汇率: {from_currency} -> {to_currency} = {exchange_rate}")

    def adjust_cost_to_base_year(
        self,
        cost: float,
        cost_year: int,
        target_currency: Optional[str] = None
    ) -> float:
        """
        将成本调整到基准年

        Args:
            cost: 原始成本
            cost_year: 成本年份
            target_currency: 目标货币（可选）

        Returns:
            调整到基准年的成本
        """
        # 通胀调整
        adjusted_cost = self.adjust_for_inflation(cost, cost_year, self.base_year)

        # 货币转换（如果需要）
        if target_currency and target_currency != self.base_currency:
            adjusted_cost = self.convert_currency(
                adjusted_cost,
                self.base_currency,
                target_currency
            )

        return adjusted_cost

    def calculate_real_discount_rate(
        self,
        nominal_rate: float,
        inflation_rate: float
    ) -> float:
        """
        计算实际折现率

        Args:
            nominal_rate: 名义折现率
            inflation_rate: 通胀率

        Returns:
            实际折现率
        """
        real_rate = (nominal_rate - inflation_rate) / (1 + inflation_rate)
        return real_rate

    def sensitivity_analysis_discount_rate(
        self,
        cost_stream: List[Tuple[float, int]],
        rate_range: Tuple[float, float] = (0.01, 0.10),
        steps: int = 10
    ) -> List[Dict]:
        """
        折现率敏感性分析

        Args:
            cost_stream: 成本流
            rate_range: 折现率范围
            steps: 分析步数

        Returns:
            敏感性分析结果
        """
        min_rate, max_rate = rate_range
        rates = [min_rate + (max_rate - min_rate) * i / (steps - 1) for i in range(steps)]

        results = []
        for rate in rates:
            npv = self.net_present_value(cost_stream, rate)
            results.append({
                'discount_rate': rate,
                'net_present_value': npv,
                'rate_percentage': rate * 100
            })

        return results

    def get_inflation_rate(self, year: int) -> float:
        """获取指定年份的通胀率"""
        if year in self.inflation_data:
            return self.inflation_data[year].inflation_rate
        else:
            # 返回默认通胀率
            return 0.03

    def get_cpi_index(self, year: int) -> float:
        """获取指定年份的CPI指数"""
        if year in self.inflation_data:
            return self.inflation_data[year].cpi_index
        else:
            # 基于基准年计算
            base_cpi = self.inflation_data.get(self.base_year, InflationData(self.base_year, 0.03, 100.0)).cpi_index
            years_diff = year - self.base_year

            if years_diff == 0:
                return base_cpi
            elif years_diff > 0:
                # 未来年份
                cpi = base_cpi
                for y in range(self.base_year, year):
                    inflation_rate = self.get_inflation_rate(y + 1)
                    cpi *= (1 + inflation_rate)
                return cpi
            else:
                # 历史年份
                cpi = base_cpi
                for y in range(year, self.base_year):
                    inflation_rate = self.get_inflation_rate(y + 1)
                    cpi /= (1 + inflation_rate)
                return cpi

    def validate_adjustment_parameters(self) -> Dict:
        """
        验证调整参数

        Returns:
            验证结果
        """
        errors = []
        warnings = []

        # 验证折现率
        if self.discount_rate < 0 or self.discount_rate > 0.20:
            warnings.append(f"折现率可能不合理: {self.discount_rate:.1%}")

        # 验证基准年
        if self.base_year < 1990 or self.base_year > 2030:
            warnings.append(f"基准年可能不合理: {self.base_year}")

        # 验证通胀数据
        if not self.inflation_data:
            errors.append("缺少通胀数据")
        else:
            for year, data in self.inflation_data.items():
                if data.inflation_rate < -0.10 or data.inflation_rate > 0.50:
                    warnings.append(f"{year}年通胀率可能不合理: {data.inflation_rate:.1%}")
                if data.cpi_index <= 0:
                    errors.append(f"{year}年CPI指数无效: {data.cpi_index}")

        # 验证汇率数据
        for rate_key, rate_data in self.currency_rates.items():
            if rate_data.exchange_rate <= 0:
                errors.append(f"汇率无效: {rate_key} = {rate_data.exchange_rate}")

        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
