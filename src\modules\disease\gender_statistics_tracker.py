"""
性别差异统计跟踪系统

实现性别特异性疾病进展和发病率的统计跟踪功能，
用于分析和验证性别差异模型的准确性。
"""

import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import logging
from collections import defaultdict
from scipy import stats

from src.core.enums import Gender, DiseaseState

logger = logging.getLogger(__name__)


@dataclass
class GenderStatistics:
    """性别统计信息"""
    gender: str
    sample_count: int = 0
    mean_age_at_onset: float = 0.0
    std_age_at_onset: float = 0.0
    progression_times: List[float] = field(default_factory=list)
    incidence_rates: List[float] = field(default_factory=list)
    risk_factor_impacts: Dict[str, List[float]] = field(default_factory=lambda: defaultdict(list))


@dataclass
class TransitionStatistics:
    """状态转换统计信息"""
    transition_type: str
    male_stats: GenderStatistics = field(default_factory=lambda: GenderStatistics("male"))
    female_stats: GenderStatistics = field(default_factory=lambda: GenderStatistics("female"))
    
    def get_gender_ratio(self) -> float:
        """获取男女比例"""
        if self.female_stats.sample_count == 0:
            return float('inf')
        return self.male_stats.sample_count / self.female_stats.sample_count


class GenderStatisticsTracker:
    """性别差异统计跟踪器
    
    跟踪和分析疾病进展中的性别差异，包括：
    - 腺瘤产生率的性别差异
    - 进展时间的性别差异
    - 癌症发病率的性别差异
    - 风险因素影响的性别差异
    """

    def __init__(self):
        """初始化性别统计跟踪器"""
        self.transition_statistics: Dict[str, TransitionStatistics] = {}
        self.anatomical_location_statistics: Dict[str, Dict[str, GenderStatistics]] = {}
        self.age_group_statistics: Dict[str, Dict[str, GenderStatistics]] = {}
        self.risk_factor_statistics: Dict[str, Dict[str, GenderStatistics]] = {}
        
        # 初始化统计结构
        self._initialize_statistics()
        
        logger.info("性别差异统计跟踪器初始化完成")

    def _initialize_statistics(self) -> None:
        """初始化统计结构"""
        # 初始化转换统计
        transition_types = [
            "adenoma_initiation",
            "low_to_high_adenoma",
            "high_to_preclinical",
            "preclinical_to_clinical",
            "stage_progression"
        ]
        
        for transition in transition_types:
            self.transition_statistics[transition] = TransitionStatistics(transition)
        
        # 初始化解剖位置统计
        locations = ["proximal_colon", "distal_colon", "rectum"]
        for location in locations:
            self.anatomical_location_statistics[location] = {
                "male": GenderStatistics("male"),
                "female": GenderStatistics("female")
            }
        
        # 初始化年龄组统计
        age_groups = ["<50", "50-59", "60-69", "70-79", ">=80"]
        for age_group in age_groups:
            self.age_group_statistics[age_group] = {
                "male": GenderStatistics("male"),
                "female": GenderStatistics("female")
            }

    def record_adenoma_initiation(
        self,
        age: float,
        gender: Gender,
        risk_factors: Optional[Dict[str, float]] = None
    ) -> None:
        """
        记录腺瘤产生事件
        
        Args:
            age: 年龄
            gender: 性别
            risk_factors: 风险因素
        """
        gender_str = gender.value.lower()
        transition_stats = self.transition_statistics["adenoma_initiation"]
        
        if gender_str == "male":
            stats_obj = transition_stats.male_stats
        else:
            stats_obj = transition_stats.female_stats
        
        # 更新统计信息
        stats_obj.sample_count += 1
        stats_obj.progression_times.append(age)
        
        # 更新平均年龄
        stats_obj.mean_age_at_onset = np.mean(stats_obj.progression_times)
        stats_obj.std_age_at_onset = np.std(stats_obj.progression_times, ddof=1) if len(stats_obj.progression_times) > 1 else 0.0
        
        # 记录风险因素影响
        if risk_factors:
            for factor, value in risk_factors.items():
                stats_obj.risk_factor_impacts[factor].append(value)
        
        # 记录年龄组统计
        age_group = self._get_age_group(age)
        self.age_group_statistics[age_group][gender_str].sample_count += 1

    def record_progression_event(
        self,
        transition_type: str,
        age: float,
        gender: Gender,
        progression_time: float,
        anatomical_location: Optional[str] = None
    ) -> None:
        """
        记录疾病进展事件
        
        Args:
            transition_type: 转换类型
            age: 年龄
            gender: 性别
            progression_time: 进展时间
            anatomical_location: 解剖位置
        """
        gender_str = gender.value.lower()
        
        if transition_type not in self.transition_statistics:
            self.transition_statistics[transition_type] = TransitionStatistics(transition_type)
        
        transition_stats = self.transition_statistics[transition_type]
        
        if gender_str == "male":
            stats_obj = transition_stats.male_stats
        else:
            stats_obj = transition_stats.female_stats
        
        # 更新统计信息
        stats_obj.sample_count += 1
        stats_obj.progression_times.append(progression_time)
        
        # 更新平均进展时间
        stats_obj.mean_age_at_onset = np.mean(stats_obj.progression_times)
        stats_obj.std_age_at_onset = np.std(stats_obj.progression_times, ddof=1) if len(stats_obj.progression_times) > 1 else 0.0
        
        # 记录解剖位置统计
        if anatomical_location and anatomical_location in self.anatomical_location_statistics:
            self.anatomical_location_statistics[anatomical_location][gender_str].sample_count += 1
            self.anatomical_location_statistics[anatomical_location][gender_str].progression_times.append(progression_time)

    def record_cancer_incidence(
        self,
        age: float,
        gender: Gender,
        anatomical_location: str,
        incidence_probability: float
    ) -> None:
        """
        记录癌症发病事件
        
        Args:
            age: 年龄
            gender: 性别
            anatomical_location: 解剖位置
            incidence_probability: 发病概率
        """
        gender_str = gender.value.lower()
        
        # 记录解剖位置统计
        if anatomical_location in self.anatomical_location_statistics:
            stats_obj = self.anatomical_location_statistics[anatomical_location][gender_str]
            stats_obj.sample_count += 1
            stats_obj.incidence_rates.append(incidence_probability)
            stats_obj.progression_times.append(age)  # 使用年龄作为发病年龄
        
        # 记录年龄组统计
        age_group = self._get_age_group(age)
        self.age_group_statistics[age_group][gender_str].sample_count += 1
        self.age_group_statistics[age_group][gender_str].incidence_rates.append(incidence_probability)

    def _get_age_group(self, age: float) -> str:
        """获取年龄组"""
        if age < 50:
            return "<50"
        elif age < 60:
            return "50-59"
        elif age < 70:
            return "60-69"
        elif age < 80:
            return "70-79"
        else:
            return ">=80"

    def get_gender_differences_summary(self) -> Dict[str, Any]:
        """
        获取性别差异摘要
        
        Returns:
            Dict: 性别差异摘要统计
        """
        summary = {
            "transition_differences": {},
            "anatomical_location_differences": {},
            "age_group_differences": {},
            "statistical_significance": {}
        }
        
        # 分析转换差异
        for transition_type, stats in self.transition_statistics.items():
            if stats.male_stats.sample_count > 0 and stats.female_stats.sample_count > 0:
                summary["transition_differences"][transition_type] = {
                    "male_female_ratio": stats.get_gender_ratio(),
                    "male_mean_time": stats.male_stats.mean_age_at_onset,
                    "female_mean_time": stats.female_stats.mean_age_at_onset,
                    "time_difference": stats.male_stats.mean_age_at_onset - stats.female_stats.mean_age_at_onset
                }
        
        # 分析解剖位置差异
        for location, gender_stats in self.anatomical_location_statistics.items():
            male_stats = gender_stats["male"]
            female_stats = gender_stats["female"]
            
            if male_stats.sample_count > 0 and female_stats.sample_count > 0:
                summary["anatomical_location_differences"][location] = {
                    "male_female_ratio": male_stats.sample_count / female_stats.sample_count,
                    "male_mean_incidence": np.mean(male_stats.incidence_rates) if male_stats.incidence_rates else 0.0,
                    "female_mean_incidence": np.mean(female_stats.incidence_rates) if female_stats.incidence_rates else 0.0
                }
        
        # 分析年龄组差异
        for age_group, gender_stats in self.age_group_statistics.items():
            male_stats = gender_stats["male"]
            female_stats = gender_stats["female"]
            
            if male_stats.sample_count > 0 and female_stats.sample_count > 0:
                summary["age_group_differences"][age_group] = {
                    "male_female_ratio": male_stats.sample_count / female_stats.sample_count,
                    "male_count": male_stats.sample_count,
                    "female_count": female_stats.sample_count
                }
        
        return summary

    def perform_statistical_tests(self) -> Dict[str, Dict[str, float]]:
        """
        执行统计显著性检验
        
        Returns:
            Dict: 统计检验结果
        """
        test_results = {}
        
        # 对转换时间进行t检验
        for transition_type, transition_stats in self.transition_statistics.items():
            male_times = transition_stats.male_stats.progression_times
            female_times = transition_stats.female_stats.progression_times

            if len(male_times) > 1 and len(female_times) > 1:
                t_stat, p_value = stats.ttest_ind(male_times, female_times)
                test_results[f"{transition_type}_time_difference"] = {
                    "t_statistic": t_stat,
                    "p_value": p_value,
                    "significant": p_value < 0.05
                }
        
        # 对发病率进行检验
        for location, gender_stats in self.anatomical_location_statistics.items():
            male_rates = gender_stats["male"].incidence_rates
            female_rates = gender_stats["female"].incidence_rates
            
            if len(male_rates) > 1 and len(female_rates) > 1:
                t_stat, p_value = stats.ttest_ind(male_rates, female_rates)
                test_results[f"{location}_incidence_difference"] = {
                    "t_statistic": t_stat,
                    "p_value": p_value,
                    "significant": p_value < 0.05
                }
        
        return test_results

    def export_statistics(self) -> Dict[str, Any]:
        """
        导出所有统计信息
        
        Returns:
            Dict: 完整的统计信息
        """
        return {
            "transition_statistics": {
                transition_type: {
                    "male": {
                        "sample_count": stats.male_stats.sample_count,
                        "mean_age_at_onset": stats.male_stats.mean_age_at_onset,
                        "std_age_at_onset": stats.male_stats.std_age_at_onset,
                        "progression_times": stats.male_stats.progression_times
                    },
                    "female": {
                        "sample_count": stats.female_stats.sample_count,
                        "mean_age_at_onset": stats.female_stats.mean_age_at_onset,
                        "std_age_at_onset": stats.female_stats.std_age_at_onset,
                        "progression_times": stats.female_stats.progression_times
                    }
                }
                for transition_type, stats in self.transition_statistics.items()
            },
            "anatomical_location_statistics": self.anatomical_location_statistics,
            "age_group_statistics": self.age_group_statistics,
            "summary": self.get_gender_differences_summary(),
            "statistical_tests": self.perform_statistical_tests()
        }
