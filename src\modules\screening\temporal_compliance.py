"""
时间趋势依从性建模

实现依从性的时间趋势分析，包括首次vs重复筛查差异、
时间衰减模型和筛查历史对未来依从性的影响。
"""

import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum

from .compliance_model import ScreeningEvent, ComplianceModel
from .enums import ScreeningToolType, ScreeningResult
from ...core.individual import Individual


class CompliancePattern(Enum):
    """依从性模式枚举"""
    
    CONSISTENT = "consistent"           # 持续依从
    DECLINING = "declining"             # 逐渐下降
    INTERMITTENT = "intermittent"       # 间歇性依从
    RECOVERING = "recovering"           # 恢复性依从
    NON_COMPLIANT = "non_compliant"     # 不依从


@dataclass
class TemporalComplianceParameters:
    """时间趋势依从性参数"""
    
    first_time_multiplier: float = 0.8      # 首次筛查倍数
    repeat_multiplier: float = 1.2          # 重复筛查倍数
    time_decay_rate: float = 0.05           # 年度衰减率
    recovery_rate: float = 0.1              # 依从性恢复率
    max_decay_years: int = 10               # 最大衰减年限
    interruption_penalty: float = 0.2      # 中断惩罚系数
    consistency_bonus: float = 0.1         # 持续性奖励系数
    
    def __post_init__(self):
        """验证参数有效性"""
        if not (0.0 <= self.time_decay_rate <= 1.0):
            raise ValueError("时间衰减率必须在0-1之间")
        if not (0.0 <= self.recovery_rate <= 1.0):
            raise ValueError("恢复率必须在0-1之间")
        if self.max_decay_years <= 0:
            raise ValueError("最大衰减年限必须大于0")


class TemporalComplianceModel:
    """
    时间趋势依从性模型
    
    分析和预测个体依从性的时间变化模式，
    包括首次vs重复筛查、时间衰减和恢复模式。
    """
    
    def __init__(self, parameters: TemporalComplianceParameters):
        """
        初始化时间趋势依从性模型
        
        Args:
            parameters: 时间趋势参数配置
        """
        self.parameters = parameters
        self._compliance_cache = {}  # 缓存计算结果
    
    def calculate_temporal_factor(
        self, 
        individual: Individual,
        screening_history: List[ScreeningEvent],
        current_date: Optional[datetime] = None
    ) -> float:
        """
        计算时间趋势因子
        
        Args:
            individual: 个体对象
            screening_history: 筛查历史记录
            current_date: 当前日期，默认为现在
            
        Returns:
            时间趋势调整因子
        """
        if current_date is None:
            current_date = datetime.now()
        
        # 过滤该个体的筛查历史
        individual_history = [
            event for event in screening_history 
            if event.individual_id == individual.individual_id
        ]
        
        if not individual_history:
            # 首次筛查
            return self.parameters.first_time_multiplier
        
        # 分析筛查模式
        compliance_pattern = self._analyze_compliance_pattern(individual_history)
        
        # 计算基础时间因子
        base_temporal_factor = self._calculate_base_temporal_factor(
            individual_history, current_date
        )
        
        # 应用模式调整
        pattern_adjustment = self._get_pattern_adjustment(compliance_pattern)
        
        # 计算最终时间因子
        final_factor = base_temporal_factor * pattern_adjustment
        
        return max(final_factor, 0.1)  # 最小值为0.1
    
    def _analyze_compliance_pattern(
        self, 
        screening_history: List[ScreeningEvent]
    ) -> CompliancePattern:
        """
        分析个体的依从性模式
        
        Args:
            screening_history: 筛查历史记录
            
        Returns:
            依从性模式
        """
        if len(screening_history) < 2:
            return CompliancePattern.CONSISTENT
        
        # 按时间排序
        sorted_history = sorted(screening_history, key=lambda x: x.date)
        
        # 计算筛查间隔
        intervals = []
        for i in range(1, len(sorted_history)):
            interval_days = (sorted_history[i].date - sorted_history[i-1].date).days
            intervals.append(interval_days)
        
        # 分析间隔模式
        avg_interval = sum(intervals) / len(intervals)
        interval_variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
        
        # 检查最近的依从性趋势
        recent_intervals = intervals[-3:] if len(intervals) >= 3 else intervals
        
        if interval_variance < (avg_interval * 0.2) ** 2:
            # 间隔稳定
            return CompliancePattern.CONSISTENT
        elif len(recent_intervals) >= 2 and recent_intervals[-1] > recent_intervals[-2] * 1.5:
            # 最近间隔增长
            return CompliancePattern.DECLINING
        elif len(recent_intervals) >= 2 and recent_intervals[-1] < recent_intervals[-2] * 0.7:
            # 最近间隔缩短
            return CompliancePattern.RECOVERING
        elif max(intervals) > avg_interval * 2:
            # 有长期中断
            return CompliancePattern.INTERMITTENT
        else:
            return CompliancePattern.CONSISTENT
    
    def _calculate_base_temporal_factor(
        self, 
        screening_history: List[ScreeningEvent],
        current_date: datetime
    ) -> float:
        """
        计算基础时间因子
        
        Args:
            screening_history: 筛查历史记录
            current_date: 当前日期
            
        Returns:
            基础时间因子
        """
        # 获取最近的筛查事件
        last_screening = max(screening_history, key=lambda x: x.date)
        time_since_last = (current_date - last_screening.date).days / 365.25
        
        # 限制最大衰减时间
        effective_time = min(time_since_last, self.parameters.max_decay_years)
        
        # 计算时间衰减
        decay_factor = (1 - self.parameters.time_decay_rate) ** effective_time
        
        # 应用重复筛查倍数
        base_factor = self.parameters.repeat_multiplier * decay_factor
        
        return base_factor
    
    def _get_pattern_adjustment(self, pattern: CompliancePattern) -> float:
        """
        根据依从性模式获取调整因子
        
        Args:
            pattern: 依从性模式
            
        Returns:
            模式调整因子
        """
        adjustments = {
            CompliancePattern.CONSISTENT: 1.0 + self.parameters.consistency_bonus,
            CompliancePattern.DECLINING: 1.0 - self.parameters.interruption_penalty,
            CompliancePattern.INTERMITTENT: 1.0 - self.parameters.interruption_penalty * 0.5,
            CompliancePattern.RECOVERING: 1.0 + self.parameters.recovery_rate,
            CompliancePattern.NON_COMPLIANT: 1.0 - self.parameters.interruption_penalty * 1.5
        }
        
        return adjustments.get(pattern, 1.0)
    
    def predict_future_compliance(
        self,
        individual: Individual,
        screening_history: List[ScreeningEvent],
        prediction_years: int = 5
    ) -> Dict[int, float]:
        """
        预测未来几年的依从性趋势
        
        Args:
            individual: 个体对象
            screening_history: 筛查历史记录
            prediction_years: 预测年数
            
        Returns:
            未来各年的依从性预测字典
        """
        predictions = {}
        current_date = datetime.now()
        
        for year in range(1, prediction_years + 1):
            future_date = current_date + timedelta(days=365 * year)
            
            # 计算该年的时间因子
            temporal_factor = self.calculate_temporal_factor(
                individual, screening_history, future_date
            )
            
            predictions[year] = temporal_factor
        
        return predictions
    
    def calculate_screening_interval_recommendation(
        self,
        individual: Individual,
        screening_history: List[ScreeningEvent],
        tool_type: ScreeningToolType
    ) -> Dict[str, Any]:
        """
        基于依从性模式推荐筛查间隔
        
        Args:
            individual: 个体对象
            screening_history: 筛查历史记录
            tool_type: 筛查工具类型
            
        Returns:
            筛查间隔推荐字典
        """
        # 分析当前依从性模式
        individual_history = [
            event for event in screening_history 
            if event.individual_id == individual.individual_id
        ]
        
        pattern = self._analyze_compliance_pattern(individual_history)
        
        # 基础推荐间隔（月）
        base_intervals = {
            ScreeningToolType.FIT: 12,
            ScreeningToolType.COLONOSCOPY: 120,  # 10年
            ScreeningToolType.SIGMOIDOSCOPY: 60,  # 5年
            ScreeningToolType.CTCOLONOGRAPHY: 60  # 5年
        }
        
        base_interval = base_intervals.get(tool_type, 12)
        
        # 根据依从性模式调整间隔
        pattern_adjustments = {
            CompliancePattern.CONSISTENT: 1.0,
            CompliancePattern.DECLINING: 0.8,      # 缩短间隔
            CompliancePattern.INTERMITTENT: 0.7,   # 更短间隔
            CompliancePattern.RECOVERING: 0.9,     # 稍微缩短
            CompliancePattern.NON_COMPLIANT: 0.6   # 显著缩短
        }
        
        adjustment = pattern_adjustments.get(pattern, 1.0)
        recommended_interval = int(base_interval * adjustment)
        
        return {
            'recommended_interval_months': recommended_interval,
            'compliance_pattern': pattern.value,
            'confidence_level': self._calculate_recommendation_confidence(individual_history),
            'rationale': self._get_interval_rationale(pattern, adjustment)
        }
    
    def _calculate_recommendation_confidence(
        self, 
        screening_history: List[ScreeningEvent]
    ) -> float:
        """
        计算推荐的置信度
        
        Args:
            screening_history: 筛查历史记录
            
        Returns:
            置信度 (0-1)
        """
        # 基于历史记录数量和一致性计算置信度
        history_count = len(screening_history)
        
        if history_count == 0:
            return 0.3  # 低置信度
        elif history_count < 3:
            return 0.6  # 中等置信度
        else:
            return 0.9  # 高置信度
    
    def _get_interval_rationale(
        self, 
        pattern: CompliancePattern, 
        adjustment: float
    ) -> str:
        """
        获取间隔调整的理由说明
        
        Args:
            pattern: 依从性模式
            adjustment: 调整因子
            
        Returns:
            理由说明
        """
        rationales = {
            CompliancePattern.CONSISTENT: "依从性良好，维持标准间隔",
            CompliancePattern.DECLINING: "依从性下降，建议缩短间隔以提高参与率",
            CompliancePattern.INTERMITTENT: "间歇性依从，需要更频繁的提醒和检查",
            CompliancePattern.RECOVERING: "依从性恢复中，适度缩短间隔以巩固习惯",
            CompliancePattern.NON_COMPLIANT: "依从性差，需要显著缩短间隔并加强干预"
        }
        
        return rationales.get(pattern, "基于历史模式的标准调整")
    
    def generate_compliance_trend_report(
        self,
        individual: Individual,
        screening_history: List[ScreeningEvent]
    ) -> Dict[str, Any]:
        """
        生成个体依从性趋势报告
        
        Args:
            individual: 个体对象
            screening_history: 筛查历史记录
            
        Returns:
            趋势报告字典
        """
        individual_history = [
            event for event in screening_history 
            if event.individual_id == individual.individual_id
        ]
        
        if not individual_history:
            return {
                'individual_id': individual.individual_id,
                'status': 'no_history',
                'message': '无筛查历史记录'
            }
        
        # 分析依从性模式
        pattern = self._analyze_compliance_pattern(individual_history)
        
        # 计算当前时间因子
        current_factor = self.calculate_temporal_factor(individual, screening_history)
        
        # 预测未来趋势
        future_predictions = self.predict_future_compliance(
            individual, screening_history, 3
        )
        
        # 计算筛查间隔统计
        sorted_history = sorted(individual_history, key=lambda x: x.date)
        intervals = []
        for i in range(1, len(sorted_history)):
            interval_days = (sorted_history[i].date - sorted_history[i-1].date).days
            intervals.append(interval_days)
        
        report = {
            'individual_id': individual.individual_id,
            'compliance_pattern': pattern.value,
            'current_temporal_factor': current_factor,
            'screening_count': len(individual_history),
            'date_range': {
                'first_screening': sorted_history[0].date.isoformat(),
                'last_screening': sorted_history[-1].date.isoformat()
            },
            'interval_statistics': {
                'average_interval_days': sum(intervals) / len(intervals) if intervals else 0,
                'min_interval_days': min(intervals) if intervals else 0,
                'max_interval_days': max(intervals) if intervals else 0
            },
            'future_predictions': future_predictions,
            'recommendations': self._generate_compliance_recommendations(pattern, current_factor)
        }
        
        return report
    
    def _generate_compliance_recommendations(
        self, 
        pattern: CompliancePattern, 
        current_factor: float
    ) -> List[str]:
        """
        生成依从性改善建议
        
        Args:
            pattern: 依从性模式
            current_factor: 当前时间因子
            
        Returns:
            建议列表
        """
        recommendations = []
        
        if pattern == CompliancePattern.DECLINING:
            recommendations.extend([
                "加强患者教育，强调筛查的重要性",
                "考虑提供更便捷的筛查选择",
                "建立定期提醒机制"
            ])
        elif pattern == CompliancePattern.INTERMITTENT:
            recommendations.extend([
                "分析中断原因，针对性解决障碍",
                "提供个性化的筛查计划",
                "考虑家庭支持系统的参与"
            ])
        elif pattern == CompliancePattern.NON_COMPLIANT:
            recommendations.extend([
                "进行深度访谈了解不依从原因",
                "考虑替代筛查方法",
                "提供经济支持或便民服务"
            ])
        elif pattern == CompliancePattern.CONSISTENT:
            recommendations.append("维持当前良好的筛查习惯")
        
        if current_factor < 0.5:
            recommendations.append("当前依从性风险较高，需要重点关注")
        
        return recommendations
