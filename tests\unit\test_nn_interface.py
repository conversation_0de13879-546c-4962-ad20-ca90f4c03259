"""
神经网络校准接口单元测试

测试参数向量化、校准目标管理、校准结果处理和验证功能。
"""

import pytest
import numpy as np
import tempfile
import json
from datetime import datetime
from pathlib import Path

from src.modules.config.parameter_manager import ParameterManager
from src.modules.calibration.nn_interface import (
    NeuralNetworkCalibrator,
    CalibrationTarget,
    CalibrationResult,
    CalibrationStatus
)


class TestCalibrationTarget:
    """测试校准目标"""

    def test_calibration_target_creation(self):
        """测试校准目标创建"""
        target = CalibrationTarget(
            name="test_target",
            target_value=0.5,
            weight=1.5,
            tolerance=0.05,
            description="测试目标"
        )
        
        assert target.name == "test_target"
        assert target.target_value == 0.5
        assert target.weight == 1.5
        assert target.tolerance == 0.05
        assert target.description == "测试目标"


class TestCalibrationResult:
    """测试校准结果"""

    def test_calibration_result_creation(self):
        """测试校准结果创建"""
        parameter_vector = np.array([1.0, 2.0, 3.0])
        target_errors = {"target1": 0.01, "target2": 0.02}
        
        result = CalibrationResult(
            iteration=10,
            parameter_vector=parameter_vector,
            loss_value=0.05,
            target_errors=target_errors,
            convergence_status="running"
        )
        
        assert result.iteration == 10
        assert np.array_equal(result.parameter_vector, parameter_vector)
        assert result.loss_value == 0.05
        assert result.target_errors == target_errors
        assert result.convergence_status == "running"
        assert isinstance(result.timestamp, datetime)


class TestNeuralNetworkCalibrator:
    """测试神经网络校准器"""

    def test_calibrator_initialization(self):
        """测试校准器初始化"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)

        # 验证初始化
        assert calibrator.parameter_manager == param_manager
        assert len(calibrator.calibration_targets) > 0
        assert len(calibrator.parameter_mapping) > 0
        assert len(calibrator.vector_mapping) > 0
        assert isinstance(calibrator.calibration_status, CalibrationStatus)

        # 验证新的校准目标结构
        target_names = list(calibrator.calibration_targets.keys())

        # 检查男性低风险腺瘤目标
        male_low_risk_targets = [name for name in target_names if name.startswith("male_low_risk_adenoma")]
        assert len(male_low_risk_targets) == 4  # 4个年龄段

        # 检查女性低风险腺瘤目标
        female_low_risk_targets = [name for name in target_names if name.startswith("female_low_risk_adenoma")]
        assert len(female_low_risk_targets) == 4  # 4个年龄段

        # 检查男性高风险腺瘤目标
        male_high_risk_targets = [name for name in target_names if name.startswith("male_high_risk_adenoma")]
        assert len(male_high_risk_targets) == 4  # 4个年龄段

        # 检查女性高风险腺瘤目标
        female_high_risk_targets = [name for name in target_names if name.startswith("female_high_risk_adenoma")]
        assert len(female_high_risk_targets) == 4  # 4个年龄段

        # 检查男性癌症发病率目标
        male_cancer_targets = [name for name in target_names if name.startswith("male_cancer_incidence")]
        assert len(male_cancer_targets) == 4  # 4个年龄段

        # 检查女性癌症发病率目标
        female_cancer_targets = [name for name in target_names if name.startswith("female_cancer_incidence")]
        assert len(female_cancer_targets) == 4  # 4个年龄段

        # 检查男性癌症部位占比目标
        male_location_targets = [name for name in target_names if name.startswith("male_cancer_location")]
        assert len(male_location_targets) == 3  # 3个部位

        # 检查女性癌症部位占比目标
        female_location_targets = [name for name in target_names if name.startswith("female_cancer_location")]
        assert len(female_location_targets) == 3  # 3个部位

        # 总计应该有30个校准目标 (4+4+4+4+4+4+3+3)
        assert len(calibrator.calibration_targets) == 30

    def test_parameter_mapping_initialization(self):
        """测试参数映射初始化"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 获取可训练参数
        trainable_params = param_manager.get_trainable_parameters()
        
        # 验证映射长度
        assert len(calibrator.parameter_mapping) == len(trainable_params)
        assert len(calibrator.vector_mapping) == len(trainable_params)
        
        # 验证映射一致性
        for param_name, vector_index in calibrator.parameter_mapping.items():
            assert calibrator.vector_mapping[vector_index] == param_name

    def test_parameter_vectorization(self):
        """测试参数向量化"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 测试默认向量化
        vector = calibrator.vectorize_parameters()
        
        assert isinstance(vector, np.ndarray)
        assert len(vector) == len(calibrator.parameter_mapping)
        assert all(np.isfinite(vector))  # 所有值都应该是有限的

    def test_parameter_devectorization(self):
        """测试参数反向量化"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 创建测试向量
        vector_size = len(calibrator.parameter_mapping)
        test_vector = np.random.rand(vector_size)
        
        # 反向量化
        param_dict = calibrator.devectorize_parameters(test_vector)
        
        assert isinstance(param_dict, dict)
        assert len(param_dict) == vector_size
        
        # 验证所有参数都在字典中
        for param_name in calibrator.parameter_mapping.keys():
            assert param_name in param_dict

    def test_vectorization_devectorization_consistency(self):
        """测试向量化和反向量化的一致性"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 获取原始参数
        original_params = param_manager.get_trainable_parameters()
        
        # 向量化然后反向量化
        vector = calibrator.vectorize_parameters(original_params)
        recovered_params = calibrator.devectorize_parameters(vector)
        
        # 验证一致性
        for param_name, original_value in original_params.items():
            assert abs(recovered_params[param_name] - original_value) < 1e-10

    def test_parameter_update_from_vector(self):
        """测试从向量更新参数"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 创建新的参数向量
        vector_size = len(calibrator.parameter_mapping)
        new_vector = np.random.rand(vector_size) * 0.5 + 0.5  # 0.5-1.0范围内的值
        
        # 更新参数
        calibrator.update_parameters_from_vector(new_vector)
        
        # 验证参数已更新
        updated_params = param_manager.get_trainable_parameters()
        recovered_params = calibrator.devectorize_parameters(new_vector)
        
        for param_name in calibrator.parameter_mapping.keys():
            # 考虑约束可能导致的值调整
            assert param_name in updated_params

    def test_calibration_target_management(self):
        """测试校准目标管理"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 添加新目标
        new_target = CalibrationTarget(
            name="new_target",
            target_value=0.8,
            weight=2.0,
            tolerance=0.1
        )
        
        initial_count = len(calibrator.calibration_targets)
        calibrator.add_calibration_target(new_target)
        
        assert len(calibrator.calibration_targets) == initial_count + 1
        assert "new_target" in calibrator.calibration_targets
        assert calibrator.calibration_targets["new_target"].target_value == 0.8
        
        # 移除目标
        calibrator.remove_calibration_target("new_target")
        assert len(calibrator.calibration_targets) == initial_count
        assert "new_target" not in calibrator.calibration_targets

    def test_objective_function_setting(self):
        """测试目标函数设置"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        def test_objective(params):
            return np.sum(params ** 2)
        
        calibrator.set_objective_function(test_objective)
        assert calibrator.objective_function == test_objective

    def test_constraint_function_management(self):
        """测试约束函数管理"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        def test_constraint(params):
            return np.sum(params) - 1.0
        
        initial_count = len(calibrator.constraint_functions)
        calibrator.add_constraint_function(test_constraint)
        
        assert len(calibrator.constraint_functions) == initial_count + 1
        assert test_constraint in calibrator.constraint_functions

    def test_calibration_lifecycle(self):
        """测试校准生命周期"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 开始校准
        max_iterations = 100
        calibrator.start_calibration(max_iterations)
        
        assert calibrator.calibration_status.is_running is True
        assert calibrator.calibration_status.total_iterations == max_iterations
        assert calibrator.calibration_status.start_time is not None
        
        # 模拟校准结果
        vector = calibrator.vectorize_parameters()
        result = CalibrationResult(
            iteration=1,
            parameter_vector=vector,
            loss_value=0.1,
            target_errors={"target1": 0.05},
            convergence_status="running"
        )
        
        calibrator.update_calibration_result(result)
        
        assert len(calibrator.calibration_history) == 1
        assert calibrator.calibration_status.current_iteration == 1
        assert calibrator.calibration_status.best_loss == 0.1
        
        # 停止校准
        calibrator.stop_calibration()
        
        assert calibrator.calibration_status.is_running is False
        assert calibrator.calibration_status.end_time is not None

    def test_calibration_convergence(self):
        """测试校准收敛"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        calibrator.start_calibration(100)
        
        # 模拟收敛结果
        vector = calibrator.vectorize_parameters()
        converged_result = CalibrationResult(
            iteration=50,
            parameter_vector=vector,
            loss_value=0.001,
            target_errors={"target1": 0.001},
            convergence_status="converged"
        )
        
        calibrator.update_calibration_result(converged_result)
        
        assert calibrator.calibration_status.convergence_achieved is True
        assert calibrator.calibration_status.is_running is False

    def test_calibration_validation(self):
        """测试校准验证"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 没有校准结果时的验证
        validation = calibrator.validate_calibration_result()
        assert validation["status"] == "no_results"
        
        # 添加校准结果
        vector = calibrator.vectorize_parameters()
        result = CalibrationResult(
            iteration=1,
            parameter_vector=vector,
            loss_value=0.05,
            target_errors={
                target_name: 0.01  # 小于默认容差
                for target_name in calibrator.calibration_targets.keys()
            },
            convergence_status="converged"
        )
        
        calibrator.calibration_history.append(result)
        calibrator.calibration_status.convergence_achieved = True
        
        validation = calibrator.validate_calibration_result()
        
        assert "status" in validation
        assert "all_targets_met" in validation
        assert "parameter_validity" in validation
        assert "best_loss" in validation
        assert "target_achievements" in validation

    def test_calibration_summary(self):
        """测试校准摘要"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 没有校准时的摘要
        summary = calibrator.get_calibration_summary()
        assert summary["status"] == "no_calibration_performed"
        
        # 添加校准结果
        calibrator.start_calibration(100)
        
        vector = calibrator.vectorize_parameters()
        result = CalibrationResult(
            iteration=1,
            parameter_vector=vector,
            loss_value=0.05,
            target_errors={},
            convergence_status="running"
        )
        
        calibrator.update_calibration_result(result)
        calibrator.stop_calibration()
        
        summary = calibrator.get_calibration_summary()
        
        assert "total_iterations" in summary
        assert "best_loss" in summary
        assert "convergence_achieved" in summary
        assert "calibration_targets_count" in summary
        assert "trainable_parameters_count" in summary
        assert "validation_result" in summary
        assert "duration_seconds" in summary

    def test_calibration_export(self):
        """测试校准结果导出"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 添加校准结果
        vector = calibrator.vectorize_parameters()
        result = CalibrationResult(
            iteration=1,
            parameter_vector=vector,
            loss_value=0.05,
            target_errors={"target1": 0.01},
            convergence_status="converged"
        )
        
        calibrator.calibration_history.append(result)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            # 导出结果
            calibrator.export_calibration_results(temp_file)
            
            # 验证导出文件
            assert Path(temp_file).exists()
            
            with open(temp_file, 'r', encoding='utf-8') as f:
                exported_data = json.load(f)
            
            assert "calibration_status" in exported_data
            assert "calibration_targets" in exported_data
            assert "calibration_history" in exported_data
            assert "final_parameters" in exported_data
            assert "validation_result" in exported_data
            
        finally:
            # 清理临时文件
            Path(temp_file).unlink(missing_ok=True)

    def test_calibration_reset(self):
        """测试校准重置"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 添加一些校准历史
        vector = calibrator.vectorize_parameters()
        result = CalibrationResult(
            iteration=1,
            parameter_vector=vector,
            loss_value=0.05,
            target_errors={},
            convergence_status="running"
        )
        
        calibrator.calibration_history.append(result)
        calibrator.calibration_status.is_running = True
        
        # 重置
        calibrator.reset_calibration()
        
        assert len(calibrator.calibration_history) == 0
        assert calibrator.calibration_status.is_running is False
        assert calibrator.calibration_status.current_iteration == 0

    def test_invalid_vector_size(self):
        """测试无效向量大小"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 错误大小的向量
        wrong_size_vector = np.array([1.0, 2.0])  # 太小
        
        with pytest.raises(ValueError):
            calibrator.devectorize_parameters(wrong_size_vector)

    def test_custom_calibration_config(self):
        """测试自定义校准配置"""
        param_manager = ParameterManager()
        
        custom_config = {
            "calibration_targets": [
                {
                    "name": "custom_target",
                    "target_value": 0.9,
                    "weight": 3.0,
                    "tolerance": 0.02,
                    "description": "自定义目标"
                }
            ]
        }
        
        calibrator = NeuralNetworkCalibrator(param_manager, custom_config)
        
        assert "custom_target" in calibrator.calibration_targets
        assert calibrator.calibration_targets["custom_target"].target_value == 0.9
        assert calibrator.calibration_targets["custom_target"].weight == 3.0


class TestIntegrationScenarios:
    """测试集成场景"""

    def test_complete_calibration_workflow(self):
        """测试完整的校准工作流"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 1. 设置目标函数
        def objective_function(param_vector):
            params = calibrator.devectorize_parameters(param_vector)
            # 简单的二次损失函数
            return sum((v - 1.0) ** 2 for v in params.values())
        
        calibrator.set_objective_function(objective_function)
        
        # 2. 开始校准
        calibrator.start_calibration(max_iterations=10)
        
        # 3. 模拟几次迭代
        for i in range(5):
            vector = calibrator.vectorize_parameters()
            # 添加一些噪声
            vector += np.random.normal(0, 0.01, size=vector.shape)
            
            loss = objective_function(vector)
            
            result = CalibrationResult(
                iteration=i + 1,
                parameter_vector=vector,
                loss_value=loss,
                target_errors={name: abs(loss - target.target_value) 
                              for name, target in calibrator.calibration_targets.items()},
                convergence_status="running" if i < 4 else "converged"
            )
            
            calibrator.update_calibration_result(result)
        
        # 4. 验证结果
        validation = calibrator.validate_calibration_result()
        summary = calibrator.get_calibration_summary()
        
        assert len(calibrator.calibration_history) == 5
        assert calibrator.calibration_status.convergence_achieved is True
        assert validation["best_loss"] >= 0
        assert summary["total_iterations"] == 5

    def test_parameter_constraint_handling(self):
        """测试参数约束处理"""
        param_manager = ParameterManager()
        calibrator = NeuralNetworkCalibrator(param_manager)
        
        # 创建违反约束的参数向量
        vector = calibrator.vectorize_parameters()
        vector.fill(1000.0)  # 设置为很大的值，可能违反约束
        
        # 更新参数（应该自动处理约束）
        calibrator.update_parameters_from_vector(vector)
        
        # 验证所有参数仍然有效
        assert param_manager.validate_all_parameters() is True


if __name__ == "__main__":
    pytest.main([__file__])
