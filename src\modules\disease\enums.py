"""
疾病模块专用枚举定义

扩展疾病状态枚举，添加详细癌症分期、解剖位置和腺瘤分类功能。
"""

from enum import Enum
from typing import Set, Dict, List, Tuple
from dataclasses import dataclass


# CancerStage现在在core.enums中定义，避免重复
from ...core.enums import CancerStage


class AnatomicalLocation(Enum):
    """解剖位置枚举（近端结肠、远端结肠、直肠）"""
    PROXIMAL_COLON = "proximal_colon"    # 近端结肠
    DISTAL_COLON = "distal_colon"        # 远端结肠
    RECTUM = "rectum"                    # 直肠

    @classmethod
    def get_distribution_probabilities(cls) -> Dict["AnatomicalLocation", float]:
        """获取解剖位置分配概率"""
        return {
            cls.PROXIMAL_COLON: 0.40,  # 40%
            cls.DISTAL_COLON: 0.35,    # 35%
            cls.RECTUM: 0.25           # 25%
        }

    @classmethod
    def get_screening_sensitivity_modifiers(cls) -> Dict["AnatomicalLocation", float]:
        """获取位置特异性筛查敏感性调整因子"""
        return {
            cls.PROXIMAL_COLON: 0.8,   # 筛查敏感性较低
            cls.DISTAL_COLON: 1.0,     # 标准筛查敏感性
            cls.RECTUM: 1.2            # 筛查敏感性较高
        }

    @classmethod
    def get_progression_rate_modifiers(cls) -> Dict["AnatomicalLocation", float]:
        """获取位置特异性进展率调整因子"""
        return {
            cls.PROXIMAL_COLON: 1.1,   # 进展稍快
            cls.DISTAL_COLON: 1.0,     # 标准进展率
            cls.RECTUM: 0.9            # 进展稍慢
        }

    @classmethod
    def get_cancer_stage_distributions(cls) -> Dict["AnatomicalLocation", List[float]]:
        """获取位置特异性癌症分期分布（I-IV期）"""
        return {
            cls.PROXIMAL_COLON: [0.25, 0.35, 0.25, 0.15],  # I-IV期分布
            cls.DISTAL_COLON: [0.30, 0.40, 0.20, 0.10],
            cls.RECTUM: [0.35, 0.35, 0.20, 0.10]
        }


class AdenomaClassification:
    """精确的腺瘤分类标准
    
    低风险腺瘤：≤9mm，且无绒毛无高级别上皮内瘤变
    高风险腺瘤：≥10mm或含绒毛或高级别上皮内瘤变
    """

    # 分类阈值常量
    SIZE_THRESHOLD_MM = 10.0

    @staticmethod
    def classify_adenoma(
        size_mm: float, 
        has_villous: bool, 
        has_high_grade_dysplasia: bool
    ) -> str:
        """
        根据最新医学标准分类腺瘤
        
        Args:
            size_mm: 腺瘤大小（毫米）
            has_villous: 是否含绒毛成分
            has_high_grade_dysplasia: 是否有高级别上皮内瘤变
            
        Returns:
            str: "low_risk_adenoma" 或 "high_risk_adenoma"
        """
        if (size_mm >= AdenomaClassification.SIZE_THRESHOLD_MM or 
            has_villous or 
            has_high_grade_dysplasia):
            return "high_risk_adenoma"
        else:
            return "low_risk_adenoma"

    @staticmethod
    def get_classification_criteria() -> Dict[str, str]:
        """获取腺瘤分类标准说明"""
        return {
            "low_risk_adenoma": f"≤{AdenomaClassification.SIZE_THRESHOLD_MM-1}mm，且无绒毛无高级别上皮内瘤变",
            "high_risk_adenoma": f"≥{AdenomaClassification.SIZE_THRESHOLD_MM}mm或含绒毛或高级别上皮内瘤变"
        }


class DiseaseStateTransition:
    """疾病状态转换规则和验证"""

    @staticmethod
    def get_valid_transitions() -> Dict[str, List[str]]:
        """获取有效的疾病状态转换路径（支持双通路）"""
        return {
            # 正常状态可进入两种通路
            "normal": ["low_risk_adenoma", "small_serrated", "death_other"],

            # 腺瘤-癌变通路
            "low_risk_adenoma": ["high_risk_adenoma", "normal", "death_other"],
            "high_risk_adenoma": ["preclinical_cancer", "low_risk_adenoma", "death_other"],

            # 锯齿状腺瘤通路
            "small_serrated": ["large_serrated", "normal", "death_other"],
            "large_serrated": ["preclinical_cancer", "normal", "death_other"],

            # 共同的癌症进展路径
            "preclinical_cancer": ["clinical_cancer_stage_i", "death_other"],
            "clinical_cancer_stage_i": ["clinical_cancer_stage_ii", "death_cancer"],
            "clinical_cancer_stage_ii": ["clinical_cancer_stage_iii", "death_cancer"],
            "clinical_cancer_stage_iii": ["clinical_cancer_stage_iv", "death_cancer"],
            "clinical_cancer_stage_iv": ["death_cancer"],

            # 终态
            "death_cancer": [],
            "death_other": []
        }

    @staticmethod
    def is_valid_transition(from_state: str, to_state: str) -> bool:
        """验证状态转换是否有效"""
        valid_transitions = DiseaseStateTransition.get_valid_transitions()
        return to_state in valid_transitions.get(from_state, [])

    @staticmethod
    def get_adenoma_pathway() -> List[str]:
        """获取腺瘤-癌变通路的标准转换路径"""
        return [
            "normal",
            "low_risk_adenoma",
            "high_risk_adenoma",
            "preclinical_cancer",
            "clinical_cancer_stage_i",
            "clinical_cancer_stage_ii",
            "clinical_cancer_stage_iii",
            "clinical_cancer_stage_iv"
        ]

    @staticmethod
    def get_serrated_pathway() -> List[str]:
        """获取锯齿状腺瘤通路的标准转换路径"""
        return [
            "normal",
            "small_serrated",
            "large_serrated",
            "preclinical_cancer",
            "clinical_cancer_stage_i",
            "clinical_cancer_stage_ii",
            "clinical_cancer_stage_iii",
            "clinical_cancer_stage_iv"
        ]

    @staticmethod
    def get_transition_pathway() -> List[str]:
        """获取腺瘤-癌变通路的标准转换路径（向后兼容）"""
        return DiseaseStateTransition.get_adenoma_pathway()

    @staticmethod
    def is_serrated_state(state: str) -> bool:
        """判断是否为锯齿状腺瘤状态"""
        return state in ["small_serrated", "large_serrated"]

    @staticmethod
    def is_adenoma_state(state: str) -> bool:
        """判断是否为传统腺瘤状态"""
        return state in ["low_risk_adenoma", "high_risk_adenoma"]

    @staticmethod
    def get_pathway_type(state: str) -> str:
        """根据状态确定通路类型"""
        if DiseaseStateTransition.is_serrated_state(state):
            return "serrated_adenoma"
        elif DiseaseStateTransition.is_adenoma_state(state):
            return "adenoma_carcinoma"
        elif state in ["normal", "preclinical_cancer"] or state.startswith("clinical_cancer"):
            return "common"  # 共同状态
        else:
            return "unknown"


class StateTransitionHistory:
    """状态转换历史跟踪功能"""
    
    def __init__(self):
        self.transitions: List[Tuple[str, str, float]] = []  # (from_state, to_state, time)
        
    def add_transition(self, from_state: str, to_state: str, time: float) -> None:
        """添加状态转换记录"""
        if DiseaseStateTransition.is_valid_transition(from_state, to_state):
            self.transitions.append((from_state, to_state, time))
        else:
            raise ValueError(f"无效的状态转换: {from_state} -> {to_state}")
    
    def get_transition_history(self) -> List[Tuple[str, str, float]]:
        """获取完整转换历史"""
        return self.transitions.copy()
    
    def get_current_state(self) -> str:
        """获取当前状态"""
        if not self.transitions:
            return "normal"
        return self.transitions[-1][1]
    
    def get_time_in_state(self, state: str) -> float:
        """计算在特定状态的停留时间"""
        total_time = 0.0
        current_state = None
        state_start_time = 0.0
        
        for from_state, to_state, time in self.transitions:
            if current_state == state:
                total_time += time - state_start_time
            
            if to_state == state:
                state_start_time = time
            
            current_state = to_state
            
        return total_time
    
    def clear_history(self) -> None:
        """清空转换历史"""
        self.transitions.clear()


@dataclass
class SerratedAdenomaFeatures:
    """锯齿状腺瘤特异性属性"""
    size_mm: float                    # 腺瘤大小（毫米）
    location_preference: float        # 近端结肠偏好（0-1）
    detection_difficulty: float       # 检测难度系数（0-1，越高越难检测）
    malignant_potential: float        # 恶变潜能评分（0-1）
    morphology_score: float           # 形态学评分（0-1）

    def __post_init__(self):
        """验证属性值范围"""
        if not 0 <= self.location_preference <= 1:
            raise ValueError("location_preference 必须在 0-1 范围内")
        if not 0 <= self.detection_difficulty <= 1:
            raise ValueError("detection_difficulty 必须在 0-1 范围内")
        if not 0 <= self.malignant_potential <= 1:
            raise ValueError("malignant_potential 必须在 0-1 范围内")
        if not 0 <= self.morphology_score <= 1:
            raise ValueError("morphology_score 必须在 0-1 范围内")
        if self.size_mm < 0:
            raise ValueError("size_mm 必须为非负数")


class SerratedAdenomaState(Enum):
    """锯齿状腺瘤状态枚举"""
    SMALL_SERRATED_ADENOMA = "small_serrated"
    LARGE_SERRATED_ADENOMA = "large_serrated"

    @classmethod
    def get_all_states(cls) -> Set["SerratedAdenomaState"]:
        """获取所有锯齿状腺瘤状态"""
        return {cls.SMALL_SERRATED_ADENOMA, cls.LARGE_SERRATED_ADENOMA}

    def get_size_threshold(self) -> float:
        """获取状态对应的大小阈值（毫米）"""
        if self == self.SMALL_SERRATED_ADENOMA:
            return 10.0  # 小于10mm
        elif self == self.LARGE_SERRATED_ADENOMA:
            return 10.0  # 大于等于10mm
        return 0.0

    def is_large(self) -> bool:
        """判断是否为大锯齿状腺瘤"""
        return self == self.LARGE_SERRATED_ADENOMA


class PathwayTransitionGraph:
    """疾病通路状态转换图"""

    @staticmethod
    def get_serrated_transition_graph() -> Dict[str, Dict[str, str]]:
        """获取锯齿状腺瘤通路转换图"""
        return {
            "normal": {
                "small_serrated": "正常 → 小锯齿状腺瘤",
                "death_other": "正常 → 其他原因死亡"
            },
            "small_serrated": {
                "large_serrated": "小锯齿状腺瘤 → 大锯齿状腺瘤",
                "normal": "小锯齿状腺瘤 → 正常（治疗）",
                "death_other": "小锯齿状腺瘤 → 其他原因死亡"
            },
            "large_serrated": {
                "preclinical_cancer": "大锯齿状腺瘤 → 临床前癌症",
                "normal": "大锯齿状腺瘤 → 正常（治疗）",
                "death_other": "大锯齿状腺瘤 → 其他原因死亡"
            },
            "preclinical_cancer": {
                "clinical_cancer_stage_i": "临床前癌症 → 临床癌症I期",
                "death_other": "临床前癌症 → 其他原因死亡"
            }
        }

    @staticmethod
    def get_adenoma_transition_graph() -> Dict[str, Dict[str, str]]:
        """获取腺瘤-癌变通路转换图"""
        return {
            "normal": {
                "low_risk_adenoma": "正常 → 低风险腺瘤",
                "death_other": "正常 → 其他原因死亡"
            },
            "low_risk_adenoma": {
                "high_risk_adenoma": "低风险腺瘤 → 高风险腺瘤",
                "normal": "低风险腺瘤 → 正常（治疗）",
                "death_other": "低风险腺瘤 → 其他原因死亡"
            },
            "high_risk_adenoma": {
                "preclinical_cancer": "高风险腺瘤 → 临床前癌症",
                "low_risk_adenoma": "高风险腺瘤 → 低风险腺瘤（部分治疗）",
                "death_other": "高风险腺瘤 → 其他原因死亡"
            },
            "preclinical_cancer": {
                "clinical_cancer_stage_i": "临床前癌症 → 临床癌症I期",
                "death_other": "临床前癌症 → 其他原因死亡"
            }
        }

    @staticmethod
    def print_pathway_graph(pathway_type: str = "serrated") -> None:
        """打印通路转换图"""
        if pathway_type == "serrated":
            graph = PathwayTransitionGraph.get_serrated_transition_graph()
            print("锯齿状腺瘤通路状态转换图:")
        else:
            graph = PathwayTransitionGraph.get_adenoma_transition_graph()
            print("腺瘤-癌变通路状态转换图:")

        for state, transitions in graph.items():
            print(f"\n{state}:")
            for target, description in transitions.items():
                print(f"  → {description}")
