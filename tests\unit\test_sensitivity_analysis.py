"""
敏感性分析系统单元测试
"""

import pytest
import numpy as np
from unittest.mock import patch

from src.modules.economics.sensitivity_analysis import (
    SensitivityAnalyzer,
    ParameterDistribution,
    ParameterType,
    DistributionType,
    SensitivityResult
)


def simple_cost_model(params: dict) -> float:
    """简单的成本模型用于测试"""
    base_cost = params.get('base_cost', 1000.0)
    multiplier = params.get('multiplier', 1.0)
    additional_cost = params.get('additional_cost', 0.0)
    
    return base_cost * multiplier + additional_cost


def complex_cost_model(params: dict) -> float:
    """复杂的成本模型用于测试"""
    screening_cost = params.get('screening_cost', 100.0)
    treatment_cost = params.get('treatment_cost', 5000.0)
    probability = params.get('probability', 0.1)
    discount_rate = params.get('discount_rate', 0.03)
    years = params.get('years', 10)
    
    # 简化的成本效果模型
    annual_cost = screening_cost + treatment_cost * probability
    total_cost = 0.0
    
    for year in range(int(years)):
        discounted_cost = annual_cost / ((1 + discount_rate) ** year)
        total_cost += discounted_cost
    
    return total_cost


class TestParameterDistribution:
    """参数分布测试"""
    
    def test_uniform_distribution_sampling(self):
        """测试均匀分布采样"""
        dist = ParameterDistribution(
            name="test_param",
            parameter_type=ParameterType.COST,
            distribution_type=DistributionType.UNIFORM,
            base_value=1000.0,
            parameters={'low': 800.0, 'high': 1200.0}
        )
        
        samples = dist.sample(100)
        assert len(samples) == 100
        assert np.all(samples >= 800.0)
        assert np.all(samples <= 1200.0)
    
    def test_normal_distribution_sampling(self):
        """测试正态分布采样"""
        dist = ParameterDistribution(
            name="test_param",
            parameter_type=ParameterType.COST,
            distribution_type=DistributionType.NORMAL,
            base_value=1000.0,
            parameters={'mean': 1000.0, 'std': 100.0}
        )
        
        samples = dist.sample(1000)
        assert len(samples) == 1000
        # 检查样本均值和标准差是否接近预期
        assert abs(np.mean(samples) - 1000.0) < 50.0
        assert abs(np.std(samples) - 100.0) < 20.0
    
    def test_probability_bounds(self):
        """测试概率参数边界约束"""
        dist = ParameterDistribution(
            name="probability",
            parameter_type=ParameterType.PROBABILITY,
            distribution_type=DistributionType.NORMAL,
            base_value=0.5,
            parameters={'mean': 0.5, 'std': 0.3}  # 大标准差可能产生超出[0,1]的值
        )
        
        samples = dist.sample(1000)
        assert np.all(samples >= 0.0)
        assert np.all(samples <= 1.0)
    
    def test_cost_bounds(self):
        """测试成本参数边界约束"""
        dist = ParameterDistribution(
            name="cost",
            parameter_type=ParameterType.COST,
            distribution_type=DistributionType.NORMAL,
            base_value=100.0,
            parameters={'mean': 100.0, 'std': 200.0}  # 大标准差可能产生负值
        )
        
        samples = dist.sample(1000)
        assert np.all(samples >= 0.0)


class TestSensitivityAnalyzer:
    """敏感性分析器测试"""
    
    @pytest.fixture
    def analyzer(self):
        """创建敏感性分析器实例"""
        return SensitivityAnalyzer(simple_cost_model)
    
    @pytest.fixture
    def complex_analyzer(self):
        """创建复杂模型的敏感性分析器实例"""
        analyzer = SensitivityAnalyzer(complex_cost_model)
        # 只添加基础参数，不创建分布
        analyzer.add_parameter('screening_cost', 100.0, ParameterType.COST)
        analyzer.add_parameter('treatment_cost', 5000.0, ParameterType.COST)
        analyzer.add_parameter('probability', 0.1, ParameterType.PROBABILITY)
        analyzer.add_parameter('discount_rate', 0.03, ParameterType.RATE)
        analyzer.add_parameter('years', 10, ParameterType.DURATION)
        return analyzer
    
    def test_initialization(self, analyzer):
        """测试初始化"""
        assert analyzer.model_function == simple_cost_model
        assert len(analyzer.parameter_distributions) == 0
        assert len(analyzer.base_parameters) == 0
    
    def test_add_parameter(self, analyzer):
        """测试添加参数"""
        # 测试添加带分布的参数
        analyzer.add_parameter(
            'base_cost',
            1000.0,
            ParameterType.COST,
            DistributionType.UNIFORM,
            {'low': 800.0, 'high': 1200.0}
        )

        assert 'base_cost' in analyzer.base_parameters
        assert analyzer.base_parameters['base_cost'] == 1000.0
        assert 'base_cost' in analyzer.parameter_distributions

        dist = analyzer.parameter_distributions['base_cost']
        assert dist.parameter_type == ParameterType.COST
        assert dist.distribution_type == DistributionType.UNIFORM

        # 测试添加仅基础参数（无分布）
        analyzer.add_parameter('simple_param', 500.0, ParameterType.COST)
        assert 'simple_param' in analyzer.base_parameters
        assert 'simple_param' not in analyzer.parameter_distributions
    
    def test_one_way_sensitivity_analysis(self, analyzer):
        """测试单因素敏感性分析"""
        analyzer.add_parameter('base_cost', 1000.0)
        analyzer.add_parameter('multiplier', 1.0)
        
        result = analyzer.one_way_sensitivity_analysis(
            'multiplier', 
            variation_range=(-0.2, 0.2), 
            steps=5
        )
        
        assert isinstance(result, SensitivityResult)
        assert result.parameter_name == 'multiplier'
        assert len(result.parameter_values) == 5
        assert len(result.outcome_values) == 5
        assert result.base_outcome > 0
        
        # 验证结果的单调性（multiplier增加，结果应该增加）
        for i in range(1, len(result.outcome_values)):
            assert result.outcome_values[i] >= result.outcome_values[i-1]
    
    def test_one_way_sensitivity_analysis_absolute(self, analyzer):
        """测试绝对变化的单因素敏感性分析"""
        analyzer.add_parameter('base_cost', 1000.0)
        analyzer.add_parameter('additional_cost', 0.0)
        
        result = analyzer.one_way_sensitivity_analysis(
            'additional_cost',
            variation_range=(0.0, 500.0),
            steps=6,
            relative=False
        )
        
        # 验证参数值范围
        assert min(result.parameter_values) == 0.0
        assert max(result.parameter_values) == 500.0
        
        # 验证结果的单调性
        for i in range(1, len(result.outcome_values)):
            assert result.outcome_values[i] >= result.outcome_values[i-1]
    
    def test_one_way_sensitivity_unknown_parameter(self, analyzer):
        """测试未知参数的单因素敏感性分析"""
        with pytest.raises(ValueError, match="未定义的参数"):
            analyzer.one_way_sensitivity_analysis('unknown_param')
    
    def test_multi_way_sensitivity_analysis(self, analyzer):
        """测试多因素敏感性分析"""
        analyzer.add_parameter('base_cost', 1000.0)
        analyzer.add_parameter('multiplier', 1.0)
        
        result = analyzer.multi_way_sensitivity_analysis(
            ['base_cost', 'multiplier'],
            steps=3
        )
        
        assert 'parameter_names' in result
        assert 'parameter_grids' in result
        assert 'results' in result
        assert 'base_outcome' in result
        
        # 验证结果数量 (3x3 = 9个组合)
        assert len(result['results']) == 9
        
        # 验证每个结果包含所有参数和结果
        for res in result['results']:
            assert 'base_cost' in res
            assert 'multiplier' in res
            assert 'outcome' in res
    
    def test_monte_carlo_simulation(self, complex_analyzer):
        """测试蒙特卡洛模拟"""
        # 添加参数分布
        complex_analyzer.add_parameter(
            'screening_cost', 100.0, ParameterType.COST,
            DistributionType.UNIFORM, {'low': 80.0, 'high': 120.0}
        )
        complex_analyzer.add_parameter(
            'treatment_cost', 5000.0, ParameterType.COST,
            DistributionType.NORMAL, {'mean': 5000.0, 'std': 500.0}
        )
        
        result = complex_analyzer.monte_carlo_simulation(
            n_simulations=100,
            seed=42  # 固定种子确保可重复性
        )
        
        assert 'n_simulations' in result
        assert result['n_simulations'] == 100
        assert 'outcomes' in result
        assert len(result['outcomes']) == 100
        assert 'statistics' in result
        assert 'correlations' in result
        
        # 验证统计信息
        stats = result['statistics']
        assert 'mean' in stats
        assert 'std' in stats
        assert 'confidence_interval' in stats
        assert len(stats['confidence_interval']) == 2
        
        # 验证相关性分析
        correlations = result['correlations']
        assert 'screening_cost' in correlations
        assert 'treatment_cost' in correlations
    
    def test_monte_carlo_no_distributions(self, analyzer):
        """测试无分布定义的蒙特卡洛模拟"""
        analyzer.add_parameter('base_cost', 1000.0)  # 只添加基础参数，无分布
        
        with pytest.raises(ValueError, match="未定义参数分布"):
            analyzer.monte_carlo_simulation(['base_cost'])
    
    def test_tornado_analysis(self, complex_analyzer):
        """测试龙卷风图分析"""
        result = complex_analyzer.tornado_analysis()
        
        assert 'tornado_data' in result
        assert 'base_outcome' in result
        assert 'variation_range' in result
        
        tornado_data = result['tornado_data']
        assert len(tornado_data) == len(complex_analyzer.base_parameters)
        
        # 验证数据按影响范围排序
        ranges = [abs(entry['range']) for entry in tornado_data]
        assert ranges == sorted(ranges, reverse=True)
        
        # 验证每个条目包含必要信息
        for entry in tornado_data:
            assert 'parameter' in entry
            assert 'range' in entry
            assert 'sensitivity_coefficient' in entry
            assert 'elasticity' in entry
    
    def test_generate_sensitivity_report(self, complex_analyzer):
        """测试生成敏感性分析报告"""
        # 添加一些分布用于蒙特卡洛分析
        complex_analyzer.add_parameter(
            'screening_cost', 100.0, ParameterType.COST,
            DistributionType.UNIFORM, {'low': 80.0, 'high': 120.0}
        )
        
        report = complex_analyzer.generate_sensitivity_report(
            include_monte_carlo=True,
            n_simulations=50  # 减少模拟次数以加快测试
        )
        
        assert 'analysis_date' in report
        assert 'base_parameters' in report
        assert 'base_outcome' in report
        assert 'one_way_analysis' in report
        assert 'tornado_analysis' in report
        assert 'monte_carlo_analysis' in report
        
        # 验证单因素分析结果
        one_way = report['one_way_analysis']
        assert len(one_way) > 0
        
        for param_name, analysis in one_way.items():
            assert 'sensitivity_coefficient' in analysis
            assert 'elasticity' in analysis
            assert 'parameter_range' in analysis
            assert 'outcome_range' in analysis
    
    def test_clear_parameters(self, analyzer):
        """测试清除参数"""
        # 添加带分布的参数
        analyzer.add_parameter('base_cost', 1000.0, ParameterType.COST, DistributionType.UNIFORM)
        analyzer.add_parameter('multiplier', 1.0, ParameterType.MULTIPLIER, DistributionType.NORMAL)

        assert len(analyzer.base_parameters) == 2
        assert len(analyzer.parameter_distributions) == 2

        analyzer.clear_parameters()

        assert len(analyzer.base_parameters) == 0
        assert len(analyzer.parameter_distributions) == 0
    
    def test_get_parameter_summary(self, complex_analyzer):
        """测试获取参数摘要"""
        # 添加一个带分布的参数（覆盖已有的screening_cost）
        complex_analyzer.add_parameter(
            'new_param_with_dist', 200.0, ParameterType.COST,
            DistributionType.UNIFORM, {'low': 180.0, 'high': 220.0}
        )

        summary = complex_analyzer.get_parameter_summary()

        assert 'total_parameters' in summary
        assert 'parameters_with_distributions' in summary
        assert 'parameter_details' in summary

        assert summary['total_parameters'] == 6  # 5个原有参数 + 1个新参数
        assert summary['parameters_with_distributions'] == 1  # 1个有分布
        
        # 验证参数详情
        details = summary['parameter_details']
        assert 'new_param_with_dist' in details

        new_param_detail = details['new_param_with_dist']
        assert new_param_detail['has_distribution'] is True
        assert new_param_detail['parameter_type'] == 'cost'
        assert new_param_detail['distribution_type'] == 'uniform'

        # 验证原有参数没有分布
        assert 'screening_cost' in details
        screening_detail = details['screening_cost']
        assert screening_detail['has_distribution'] is False
    
    def test_sensitivity_result_tornado_data(self):
        """测试敏感性结果的龙卷风图数据"""
        result = SensitivityResult(
            parameter_name='test_param',
            parameter_values=[0.8, 1.0, 1.2],
            outcome_values=[800.0, 1000.0, 1200.0],
            base_outcome=1000.0,
            sensitivity_coefficient=1000.0,
            elasticity=1.0
        )
        
        tornado_data = result.get_tornado_data()
        
        assert tornado_data['parameter'] == 'test_param'
        assert tornado_data['low_value'] == 0.8
        assert tornado_data['high_value'] == 1.2
        assert tornado_data['low_outcome'] == 800.0
        assert tornado_data['high_outcome'] == 1200.0
        assert tornado_data['range'] == 400.0
        assert tornado_data['base_outcome'] == 1000.0
