"""
筛查资格检查器单元测试

测试筛查资格判断逻辑，包括初筛和诊断性肠镜筛查的资格检查。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.core.individual import Individual
from src.core.enums import DiseaseState, Gender
from src.modules.screening import (
    ScreeningEligibilityChecker, EligibilityResult, EligibilityReason,
    ScreeningInterval, ScreeningToolType, ScreeningFrequency
)
from src.modules.screening.compliance_model import ComplianceModel


class TestScreeningEligibilityChecker:
    """筛查资格检查器测试类"""
    
    @pytest.fixture
    def individual(self):
        """创建测试个体"""
        individual = Individual(
            birth_year=1970,  # 当前约54岁
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.NORMAL
        )
        return individual
    
    @pytest.fixture
    def screening_interval(self):
        """创建测试筛查间隔"""
        return ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL,
            sequence_order=1
        )
    
    @pytest.fixture
    def eligibility_checker(self):
        """创建资格检查器"""
        return ScreeningEligibilityChecker()
    
    @patch('src.modules.screening.compliance_model.ComplianceModel.simulate_compliance_decision')
    def test_primary_screening_eligible_normal_case(self, mock_compliance, individual, screening_interval, eligibility_checker):
        """测试正常情况下的初筛资格检查"""
        # 模拟依从决策为True
        mock_compliance.return_value = True

        current_time = datetime.now()

        result = eligibility_checker.check_primary_screening_eligibility(
            individual, screening_interval, current_time
        )

        assert result.is_eligible
        assert result.reason == EligibilityReason.ELIGIBLE
        assert result.details['current_age'] == int(individual.get_current_age())
    
    @patch('src.modules.screening.compliance_model.ComplianceModel.simulate_compliance_decision')
    def test_primary_screening_age_too_young(self, mock_compliance, individual, screening_interval, eligibility_checker):
        """测试年龄过小的情况"""
        # 模拟依从决策为True（虽然年龄不符合不会用到）
        mock_compliance.return_value = True

        # 修改个体年龄为45岁
        individual.birth_year = datetime.now().year - 45
        current_time = datetime.now()

        result = eligibility_checker.check_primary_screening_eligibility(
            individual, screening_interval, current_time
        )

        assert not result.is_eligible
        assert result.reason == EligibilityReason.AGE_TOO_YOUNG
        assert result.next_eligible_date is not None
    
    @patch('src.modules.screening.compliance_model.ComplianceModel.simulate_compliance_decision')
    def test_primary_screening_age_too_old(self, mock_compliance, individual, screening_interval, eligibility_checker):
        """测试年龄过大的情况"""
        # 模拟依从决策为True（虽然年龄不符合不会用到）
        mock_compliance.return_value = True

        # 修改个体年龄为80岁
        individual.birth_year = datetime.now().year - 80
        current_time = datetime.now()

        result = eligibility_checker.check_primary_screening_eligibility(
            individual, screening_interval, current_time
        )

        assert not result.is_eligible
        assert result.reason == EligibilityReason.AGE_TOO_OLD
    
    def test_primary_screening_recent_colonoscopy(self, individual, screening_interval, eligibility_checker):
        """测试最近有诊断性肠镜检查的情况"""
        current_time = datetime.now()
        
        # 添加最近的诊断性肠镜记录
        recent_colonoscopy = {
            'procedure_type': 'diagnostic_colonoscopy',
            'date': current_time - timedelta(days=365 * 5)  # 5年前
        }
        individual.diagnostic_history = [recent_colonoscopy]
        
        result = eligibility_checker.check_primary_screening_eligibility(
            individual, screening_interval, current_time, year_after_colonoscopy=8.0
        )
        
        assert not result.is_eligible
        assert result.reason == EligibilityReason.RECENT_COLONOSCOPY
        assert result.next_eligible_date is not None
    
    def test_primary_screening_recent_screening(self, individual, screening_interval, eligibility_checker):
        """测试最近有筛查的情况"""
        current_time = datetime.now()
        
        # 添加最近的筛查记录
        recent_screening = {
            'tool_type': ScreeningToolType.FIT,
            'test_date': current_time - timedelta(days=180),  # 6个月前
            'result_type': 'negative'
        }
        individual.screening_history = [recent_screening]
        
        result = eligibility_checker.check_primary_screening_eligibility(
            individual, screening_interval, current_time
        )
        
        assert not result.is_eligible
        assert result.reason == EligibilityReason.RECENT_SCREENING
        assert result.next_eligible_date is not None
    
    @patch('src.modules.screening.compliance_model.ComplianceModel.simulate_compliance_decision')
    def test_primary_screening_non_compliant(self, mock_compliance, individual, screening_interval, eligibility_checker):
        """测试不依从的情况"""
        # 模拟不依从决策
        mock_compliance.return_value = False
        
        current_time = datetime.now()
        
        result = eligibility_checker.check_primary_screening_eligibility(
            individual, screening_interval, current_time
        )
        
        assert not result.is_eligible
        assert result.reason == EligibilityReason.NON_COMPLIANT
    
    def test_diagnostic_colonoscopy_eligible(self, individual, eligibility_checker):
        """测试诊断性肠镜筛查资格 - 符合条件"""
        current_time = datetime.now()
        
        # 添加阳性筛查记录
        positive_screening = {
            'tool_type': ScreeningToolType.FIT,
            'test_date': current_time - timedelta(days=30),  # 1个月前
            'result_type': 'positive'
        }
        individual.screening_history = [positive_screening]
        
        with patch('src.modules.screening.compliance_model.ComplianceModel.simulate_diagnostic_compliance_decision') as mock_diagnostic:
            mock_diagnostic.return_value = True
            
            result = eligibility_checker.check_diagnostic_colonoscopy_eligibility(
                individual, current_time
            )
            
            assert result.is_eligible
            assert result.reason == EligibilityReason.ELIGIBLE
    
    def test_diagnostic_colonoscopy_no_positive_screening(self, individual, eligibility_checker):
        """测试诊断性肠镜筛查资格 - 无阳性筛查"""
        current_time = datetime.now()
        
        # 只有阴性筛查记录
        negative_screening = {
            'tool_type': ScreeningToolType.FIT,
            'test_date': current_time - timedelta(days=30),
            'result_type': 'negative'
        }
        individual.screening_history = [negative_screening]
        
        result = eligibility_checker.check_diagnostic_colonoscopy_eligibility(
            individual, current_time
        )
        
        assert not result.is_eligible
        assert result.reason == EligibilityReason.NO_POSITIVE_SCREENING
    
    @patch('src.modules.screening.compliance_model.ComplianceModel.simulate_diagnostic_compliance_decision')
    def test_diagnostic_colonoscopy_non_compliant(self, mock_diagnostic, individual, eligibility_checker):
        """测试诊断性肠镜筛查资格 - 不依从"""
        current_time = datetime.now()
        
        # 添加阳性筛查记录
        positive_screening = {
            'tool_type': ScreeningToolType.FIT,
            'test_date': current_time - timedelta(days=30),
            'result_type': 'positive'
        }
        individual.screening_history = [positive_screening]
        
        # 模拟诊断不依从
        mock_diagnostic.return_value = False
        
        result = eligibility_checker.check_diagnostic_colonoscopy_eligibility(
            individual, current_time
        )
        
        assert not result.is_eligible
        assert result.reason == EligibilityReason.DIAGNOSTIC_NON_COMPLIANT
    
    def test_get_last_colonoscopy_date_from_diagnostic_history(self, individual, eligibility_checker):
        """测试从诊断历史获取最后肠镜日期"""
        test_date = datetime(2023, 1, 15)
        
        # 添加诊断历史
        individual.diagnostic_history = [
            {
                'procedure_type': 'diagnostic_colonoscopy',
                'date': test_date
            }
        ]
        
        last_date = eligibility_checker._get_last_colonoscopy_date(individual)
        assert last_date == test_date
    
    def test_get_last_colonoscopy_date_from_screening_history(self, individual, eligibility_checker):
        """测试从筛查历史获取最后肠镜日期"""
        test_date = datetime(2023, 6, 20)
        
        # 添加筛查历史
        individual.screening_history = [
            {
                'tool_type': ScreeningToolType.COLONOSCOPY,
                'test_date': test_date,
                'result_type': 'negative'
            }
        ]
        
        last_date = eligibility_checker._get_last_colonoscopy_date(individual)
        assert last_date == test_date
    
    def test_get_last_screening_date(self, individual, eligibility_checker):
        """测试获取最后筛查日期"""
        test_date = datetime(2023, 8, 10)
        
        # 添加筛查历史
        individual.screening_history = [
            {
                'tool_type': ScreeningToolType.FIT,
                'test_date': test_date,
                'result_type': 'negative'
            },
            {
                'tool_type': ScreeningToolType.COLONOSCOPY,
                'test_date': datetime(2023, 1, 1),
                'result_type': 'negative'
            }
        ]
        
        last_fit_date = eligibility_checker._get_last_screening_date(individual, ScreeningToolType.FIT)
        assert last_fit_date == test_date
        
        last_colo_date = eligibility_checker._get_last_screening_date(individual, ScreeningToolType.COLONOSCOPY)
        assert last_colo_date == datetime(2023, 1, 1)
    
    def test_has_recent_positive_screening(self, individual, eligibility_checker):
        """测试检查近期阳性筛查"""
        current_time = datetime.now()

        # 添加近期阳性筛查
        individual.screening_history = [
            {
                'tool_type': ScreeningToolType.FIT,
                'test_date': current_time - timedelta(days=30),
                'result_type': 'positive'
            },
            {
                'tool_type': ScreeningToolType.FIT,
                'test_date': current_time - timedelta(days=400),  # 超过1年
                'result_type': 'positive'
            }
        ]

        has_recent = eligibility_checker._has_recent_positive_screening(individual, current_time)
        assert has_recent

        # 测试只有旧的阳性筛查
        individual.screening_history = [
            {
                'tool_type': ScreeningToolType.FIT,
                'test_date': current_time - timedelta(days=400),
                'result_type': 'positive'
            }
        ]

        has_recent = eligibility_checker._has_recent_positive_screening(individual, current_time)
        assert not has_recent

    def test_colonoscopy_screening_no_diagnostic_needed(self, individual, eligibility_checker):
        """测试肠镜筛查阳性时不需要诊断性肠镜"""
        current_time = datetime.now()

        # 添加肠镜筛查阳性记录
        individual.screening_history = [
            {
                'tool_type': ScreeningToolType.COLONOSCOPY,
                'test_date': current_time - timedelta(days=30),
                'result_type': 'positive'
            }
        ]

        # 肠镜筛查阳性不需要诊断性肠镜，因为肠镜本身就是诊断性的
        result = eligibility_checker.check_diagnostic_colonoscopy_eligibility(
            individual, current_time
        )

        # 虽然有阳性筛查，但因为是肠镜筛查，所以不需要额外的诊断性肠镜
        assert not result.is_eligible
        assert result.reason == EligibilityReason.NO_POSITIVE_SCREENING
        assert result.details['has_positive_screening']
        assert 'note' in result.details
        assert '肠镜筛查本身就是诊断性的' in result.details['note']
