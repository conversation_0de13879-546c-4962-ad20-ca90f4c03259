"""
参数化配置系统

实现统一的参数管理，支持深度神经网络校准、参数验证、版本控制和历史跟踪。
提供参数导入/导出接口，支持动态参数调整。
"""

import yaml
import json
import pandas as pd
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from datetime import datetime
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class ParameterConstraint:
    """参数约束定义"""
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    allowed_values: Optional[List[Any]] = None
    data_type: str = "float"
    description: str = ""


@dataclass
class ParameterDefinition:
    """参数定义"""
    name: str
    value: Any
    trainable: bool = True
    constraint: Optional[ParameterConstraint] = None
    category: str = "general"
    description: str = ""
    unit: str = ""

    def __post_init__(self):
        """参数验证"""
        if self.constraint:
            self._validate_value(self.value)

    def _validate_value(self, value: Any) -> bool:
        """验证参数值"""
        if self.constraint is None:
            return True

        constraint = self.constraint

        # 检查数据类型
        if constraint.data_type == "float":
            if not isinstance(value, (int, float)):
                raise ValueError(f"参数 {self.name} 必须是数值类型")
            value = float(value)
        elif constraint.data_type == "int":
            if not isinstance(value, int):
                raise ValueError(f"参数 {self.name} 必须是整数类型")
        elif constraint.data_type == "bool":
            if not isinstance(value, bool):
                raise ValueError(f"参数 {self.name} 必须是布尔类型")
        elif constraint.data_type == "str":
            if not isinstance(value, str):
                raise ValueError(f"参数 {self.name} 必须是字符串类型")

        # 检查数值范围
        if constraint.min_value is not None and value < constraint.min_value:
            raise ValueError(
                f"参数 {self.name} 值 {value} 小于最小值 {constraint.min_value}"
            )

        if constraint.max_value is not None and value > constraint.max_value:
            raise ValueError(
                f"参数 {self.name} 值 {value} 大于最大值 {constraint.max_value}"
            )

        # 检查允许值
        if (constraint.allowed_values is not None and
                value not in constraint.allowed_values):
            raise ValueError(
                f"参数 {self.name} 值 {value} 不在允许值列表中: "
                f"{constraint.allowed_values}"
            )

        return True

    def update_value(self, new_value: Any) -> None:
        """更新参数值"""
        self._validate_value(new_value)
        self.value = new_value


@dataclass
class ParameterVersion:
    """参数版本信息"""
    version_id: str
    timestamp: datetime
    parameters: Dict[str, Any]
    description: str = ""
    hash_value: str = ""

    def __post_init__(self):
        """计算参数哈希值"""
        if not self.hash_value:
            param_str = json.dumps(self.parameters, sort_keys=True)
            self.hash_value = hashlib.md5(param_str.encode()).hexdigest()


class ParameterManager:
    """参数管理器

    统一管理所有疾病模型参数，支持：
    - 参数定义和约束验证
    - 参数版本控制和历史跟踪
    - 参数导入/导出（YAML/JSON）
    - 动态参数调整
    - 神经网络校准接口
    """

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化参数管理器

        Args:
            config_file: 配置文件路径
        """
        self.parameters: Dict[str, ParameterDefinition] = {}
        self.parameter_history: List[ParameterVersion] = []
        self.current_version: Optional[str] = None
        self.config_file = config_file

        # 初始化默认参数
        self._initialize_default_parameters()

        # 如果提供了配置文件，则加载
        if config_file and Path(config_file).exists():
            self.load_from_file(config_file)

        # 创建初始版本
        self._create_version("initial", "初始参数版本")

        logger.info("参数管理器初始化完成")

    def _initialize_default_parameters(self) -> None:
        """初始化默认参数"""
        # 腺瘤分类参数
        self.add_parameter(
            "adenoma_size_threshold",
            10.0,
            trainable=False,
            constraint=ParameterConstraint(
                min_value=5.0, max_value=20.0, data_type="float"
            ),
            category="adenoma_classification",
            description="腺瘤大小阈值（mm）",
            unit="mm"
        )

        self.add_parameter(
            "villous_risk_multiplier",
            1.5,
            trainable=True,
            constraint=ParameterConstraint(
                min_value=1.2, max_value=2.0, data_type="float"
            ),
            category="adenoma_classification",
            description="绒毛性腺瘤风险倍数"
        )

        self.add_parameter(
            "high_grade_dysplasia_multiplier",
            2.0,
            trainable=True,
            constraint=ParameterConstraint(
                min_value=1.5, max_value=3.0, data_type="float"
            ),
            category="adenoma_classification",
            description="高级别上皮内瘤变风险倍数"
        )

        # 腺瘤产生参数
        self.add_parameter(
            "adenoma_initiation_inflection_point",
            50.0,
            trainable=True,
            constraint=ParameterConstraint(
                min_value=45.0, max_value=55.0, data_type="float"
            ),
            category="adenoma_initiation",
            description="腺瘤产生乙状函数拐点年龄",
            unit="years"
        )

        self.add_parameter(
            "adenoma_initiation_steepness",
            10.0,
            trainable=True,
            constraint=ParameterConstraint(
                min_value=5.0, max_value=20.0, data_type="float"
            ),
            category="adenoma_initiation",
            description="腺瘤产生乙状函数陡峭度"
        )

        # 性别特异性参数
        self.add_parameter(
            "male_adenoma_initiation_multiplier",
            1.2,
            trainable=True,
            constraint=ParameterConstraint(
                min_value=1.0, max_value=1.5, data_type="float"
            ),
            category="gender_specific",
            description="男性腺瘤产生率倍数"
        )

        self.add_parameter(
            "female_adenoma_initiation_multiplier",
            1.0,
            trainable=False,
            constraint=ParameterConstraint(
                min_value=0.8, max_value=1.2, data_type="float"
            ),
            category="gender_specific",
            description="女性腺瘤产生率倍数（基准）"
        )

        # 癌症发病参数
        for gender in ["male", "female"]:
            for location in ["proximal_colon", "distal_colon", "rectum"]:
                # 基础发病率
                base_rate = 0.0001 if gender == "male" else 0.00008
                if location == "distal_colon":
                    base_rate *= 0.8
                elif location == "rectum":
                    base_rate *= 0.6

                self.add_parameter(
                    f"{gender}_{location}_base_incidence_rate",
                    base_rate,
                    trainable=True,
                    constraint=ParameterConstraint(
                        min_value=0.00001, max_value=0.001, data_type="float"
                    ),
                    category="cancer_incidence",
                    description=f"{gender}性{location}基础发病率",
                    unit="per person per year"
                )

                # 峰值年龄
                if location == "proximal_colon":
                    peak_age = 65.0
                elif location == "distal_colon":
                    peak_age = 62.0
                else:
                    peak_age = 60.0

                self.add_parameter(
                    f"{gender}_{location}_peak_age",
                    peak_age,
                    trainable=True,
                    constraint=ParameterConstraint(
                        min_value=55.0, max_value=75.0, data_type="float"
                    ),
                    category="cancer_incidence",
                    description=f"{gender}性{location}发病峰值年龄",
                    unit="years"
                )

                # 年龄分布宽度
                if location == "proximal_colon":
                    age_spread = 10.0
                elif location == "distal_colon":
                    age_spread = 12.0
                else:
                    age_spread = 8.0

                self.add_parameter(
                    f"{gender}_{location}_age_spread",
                    age_spread,
                    trainable=True,
                    constraint=ParameterConstraint(
                        min_value=5.0, max_value=20.0, data_type="float"
                    ),
                    category="cancer_incidence",
                    description=f"{gender}性{location}年龄分布宽度",
                    unit="years"
                )

    def add_parameter(
        self,
        name: str,
        value: Any,
        trainable: bool = True,
        constraint: Optional[ParameterConstraint] = None,
        category: str = "general",
        description: str = "",
        unit: str = ""
    ) -> None:
        """
        添加参数

        Args:
            name: 参数名称
            value: 参数值
            trainable: 是否可训练
            constraint: 参数约束
            category: 参数类别
            description: 参数描述
            unit: 参数单位
        """
        param_def = ParameterDefinition(
            name=name,
            value=value,
            trainable=trainable,
            constraint=constraint,
            category=category,
            description=description,
            unit=unit
        )

        self.parameters[name] = param_def
        logger.debug(f"添加参数: {name} = {value}")

    def get_parameter(self, name: str) -> Any:
        """
        获取参数值

        Args:
            name: 参数名称

        Returns:
            Any: 参数值
        """
        if name not in self.parameters:
            raise KeyError(f"参数 {name} 不存在")

        return self.parameters[name].value

    def set_parameter(self, name: str, value: Any) -> None:
        """
        设置参数值

        Args:
            name: 参数名称
            value: 新的参数值
        """
        if name not in self.parameters:
            raise KeyError(f"参数 {name} 不存在")

        old_value = self.parameters[name].value
        self.parameters[name].update_value(value)

        logger.info(f"参数 {name} 从 {old_value} 更新为 {value}")

    def get_parameters_by_category(self, category: str) -> Dict[str, Any]:
        """
        按类别获取参数

        Args:
            category: 参数类别

        Returns:
            Dict: 参数字典
        """
        return {
            name: param.value
            for name, param in self.parameters.items()
            if param.category == category
        }

    def get_trainable_parameters(self) -> Dict[str, Any]:
        """
        获取可训练参数

        Returns:
            Dict: 可训练参数字典
        """
        return {
            name: param.value
            for name, param in self.parameters.items()
            if param.trainable
        }

    def update_parameters(self, parameter_updates: Dict[str, Any]) -> None:
        """
        批量更新参数

        Args:
            parameter_updates: 参数更新字典
        """
        for name, value in parameter_updates.items():
            self.set_parameter(name, value)

    def validate_all_parameters(self) -> bool:
        """
        验证所有参数

        Returns:
            bool: 验证是否通过
        """
        try:
            for name, param in self.parameters.items():
                param._validate_value(param.value)
            return True
        except ValueError as e:
            logger.error(f"参数验证失败: {e}")
            return False

    def _create_version(self, version_id: str, description: str = "") -> None:
        """创建参数版本"""
        current_params = {name: param.value for name, param in self.parameters.items()}

        version = ParameterVersion(
            version_id=version_id,
            timestamp=datetime.now(),
            parameters=current_params,
            description=description
        )

        self.parameter_history.append(version)
        self.current_version = version_id

        logger.info(f"创建参数版本: {version_id}")

    def save_version(self, version_id: str, description: str = "") -> None:
        """
        保存当前参数为新版本

        Args:
            version_id: 版本ID
            description: 版本描述
        """
        self._create_version(version_id, description)

    def load_version(self, version_id: str) -> None:
        """
        加载指定版本的参数

        Args:
            version_id: 版本ID
        """
        version = None
        for v in self.parameter_history:
            if v.version_id == version_id:
                version = v
                break

        if version is None:
            raise ValueError(f"版本 {version_id} 不存在")

        # 更新参数值
        for name, value in version.parameters.items():
            if name in self.parameters:
                self.parameters[name].value = value

        self.current_version = version_id
        logger.info(f"加载参数版本: {version_id}")

    def get_version_history(self) -> List[Dict[str, Any]]:
        """
        获取版本历史

        Returns:
            List: 版本历史列表
        """
        return [
            {
                "version_id": v.version_id,
                "timestamp": v.timestamp.isoformat(),
                "description": v.description,
                "hash_value": v.hash_value
            }
            for v in self.parameter_history
        ]

    def export_parameters(self, format: str = "yaml") -> Union[str, pd.DataFrame]:
        """
        导出参数配置

        Args:
            format: 导出格式（yaml/json/excel/csv）

        Returns:
            Union[str, pd.DataFrame]: 导出的配置字符串或DataFrame
        """
        if format.lower() in ["excel", "csv"]:
            # 为Excel和CSV格式创建DataFrame
            return self._export_to_dataframe()

        # 为YAML和JSON格式创建字典结构
        export_data = {
            "metadata": {
                "version": self.current_version,
                "timestamp": datetime.now().isoformat(),
                "total_parameters": len(self.parameters)
            },
            "parameters": {}
        }

        # 按类别组织参数
        categories = set(param.category for param in self.parameters.values())
        for category in categories:
            export_data["parameters"][category] = {}
            for name, param in self.parameters.items():
                if param.category == category:
                    param_data = {
                        "value": param.value,
                        "trainable": param.trainable,
                        "description": param.description,
                        "unit": param.unit
                    }

                    if param.constraint:
                        param_data["constraint"] = asdict(param.constraint)

                    export_data["parameters"][category][name] = param_data

        if format.lower() == "yaml":
            return yaml.dump(export_data, default_flow_style=False, allow_unicode=True)
        elif format.lower() == "json":
            return json.dumps(export_data, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

    def _export_to_dataframe(self) -> pd.DataFrame:
        """
        将参数导出为DataFrame格式

        Returns:
            pd.DataFrame: 参数DataFrame
        """
        rows = []

        for name, param in self.parameters.items():
            row = {
                "parameter_name": name,
                "category": param.category,
                "value": param.value,
                "trainable": param.trainable,
                "description": param.description,
                "unit": param.unit,
                "data_type": (param.constraint.data_type
                              if param.constraint else "float"),
                "min_value": (param.constraint.min_value
                              if param.constraint else None),
                "max_value": (param.constraint.max_value
                              if param.constraint else None),
                "allowed_values": (str(param.constraint.allowed_values)
                                   if param.constraint and
                                   param.constraint.allowed_values else None),
                "constraint_description": (param.constraint.description
                                           if param.constraint else "")
            }
            rows.append(row)

        df = pd.DataFrame(rows)

        # 添加元数据作为第一行（注释行）
        metadata_row = {
            "parameter_name": f"# 导出时间: {datetime.now().isoformat()}",
            "category": f"# 版本: {self.current_version}",
            "value": f"# 总参数数: {len(self.parameters)}",
            "trainable": "# 元数据行",
            "description": "# 请勿修改此行",
            "unit": "",
            "data_type": "",
            "min_value": None,
            "max_value": None,
            "allowed_values": None,
            "constraint_description": ""
        }

        # 将元数据行插入到DataFrame开头
        metadata_df = pd.DataFrame([metadata_row])
        if len(df) > 0:
            df = pd.concat([metadata_df, df], ignore_index=True)
        else:
            df = metadata_df

        return df

    def save_to_file(self, file_path: str, format: str = "yaml") -> None:
        """
        保存参数到文件

        Args:
            file_path: 文件路径
            format: 文件格式（yaml/json/excel/csv）
        """
        file_path = Path(file_path)

        if format.lower() == "excel":
            df = self.export_parameters("excel")
            df.to_excel(file_path, index=False, engine='openpyxl')
        elif format.lower() == "csv":
            df = self.export_parameters("csv")
            df.to_csv(file_path, index=False, encoding='utf-8-sig')  # 使用utf-8-sig支持中文
        else:
            content = self.export_parameters(format)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

        logger.info(f"参数已保存到文件: {file_path}")

    def load_from_file(self, file_path: str) -> None:
        """
        从文件加载参数

        Args:
            file_path: 文件路径
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")

        if file_path.suffix.lower() in ['.xlsx', '.xls']:
            self._load_from_excel(file_path)
        elif file_path.suffix.lower() == '.csv':
            self._load_from_csv(file_path)
        else:
            self._load_from_text_file(file_path)

        logger.info(f"从文件加载参数: {file_path}")

    def _load_from_text_file(self, file_path: Path) -> None:
        """从文本文件（YAML/JSON）加载参数"""
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            elif file_path.suffix.lower() == '.json':
                data = json.load(f)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")

        # 加载参数
        if "parameters" in data:
            for category, params in data["parameters"].items():
                for name, param_data in params.items():
                    if name in self.parameters:
                        # 更新现有参数
                        self.parameters[name].value = param_data["value"]
                        if "trainable" in param_data:
                            self.parameters[name].trainable = param_data["trainable"]
                    else:
                        # 添加新参数
                        constraint = None
                        if "constraint" in param_data:
                            constraint = ParameterConstraint(**param_data["constraint"])

                        self.add_parameter(
                            name=name,
                            value=param_data["value"],
                            trainable=param_data.get("trainable", True),
                            constraint=constraint,
                            category=category,
                            description=param_data.get("description", ""),
                            unit=param_data.get("unit", "")
                        )

    def _load_from_excel(self, file_path: Path) -> None:
        """从Excel文件加载参数"""
        try:
            df = pd.read_excel(file_path, engine='openpyxl')
            self._load_from_dataframe(df)
        except Exception as e:
            raise ValueError(f"读取Excel文件失败: {e}")

    def _load_from_csv(self, file_path: Path) -> None:
        """从CSV文件加载参数"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            self._load_from_dataframe(df)
        except Exception as e:
            raise ValueError(f"读取CSV文件失败: {e}")

    def _load_from_dataframe(self, df: pd.DataFrame) -> None:
        """从DataFrame加载参数"""
        # 跳过元数据行（第一行以#开头）
        if len(df) > 0 and str(df.iloc[0]['parameter_name']).startswith('#'):
            df = df.iloc[1:].reset_index(drop=True)

        for _, row in df.iterrows():
            name = row['parameter_name']
            category = row['category']
            value = row['value']
            trainable = bool(row['trainable']) if pd.notna(row['trainable']) else True
            description = (str(row['description'])
                           if pd.notna(row['description']) else "")
            unit = str(row['unit']) if pd.notna(row['unit']) else ""

            # 处理约束
            constraint = None
            if pd.notna(row.get('data_type')):
                constraint_kwargs = {
                    'data_type': str(row['data_type'])
                }

                if pd.notna(row.get('min_value')):
                    constraint_kwargs['min_value'] = float(row['min_value'])

                if pd.notna(row.get('max_value')):
                    constraint_kwargs['max_value'] = float(row['max_value'])

                if (pd.notna(row.get('allowed_values')) and
                        str(row['allowed_values']) != 'None'):
                    # 解析允许值列表
                    try:
                        allowed_values = eval(str(row['allowed_values']))
                        if isinstance(allowed_values, list):
                            constraint_kwargs['allowed_values'] = allowed_values
                    except (ValueError, SyntaxError, NameError):
                        pass  # 忽略解析错误

                if pd.notna(row.get('constraint_description')):
                    constraint_kwargs['description'] = (
                        str(row['constraint_description'])
                    )

                constraint = ParameterConstraint(**constraint_kwargs)

            # 转换值的数据类型
            if constraint and constraint.data_type == 'int':
                value = int(float(value))
            elif constraint and constraint.data_type == 'bool':
                value = (bool(value) if isinstance(value, bool)
                         else str(value).lower() in ['true', '1', 'yes'])
            elif constraint and constraint.data_type == 'float':
                value = float(value)
            else:
                # 尝试自动推断类型
                try:
                    if '.' in str(value):
                        value = float(value)
                    else:
                        value = int(value)
                except (ValueError, TypeError):
                    value = str(value)

            if name in self.parameters:
                # 更新现有参数
                self.parameters[name].value = value
                self.parameters[name].trainable = trainable
            else:
                # 添加新参数
                self.add_parameter(
                    name=name,
                    value=value,
                    trainable=trainable,
                    constraint=constraint,
                    category=category,
                    description=description,
                    unit=unit
                )

    def get_parameter_summary(self) -> Dict[str, Any]:
        """
        获取参数摘要

        Returns:
            Dict: 参数摘要
        """
        categories = {}
        trainable_count = 0

        for param in self.parameters.values():
            if param.category not in categories:
                categories[param.category] = {"count": 0, "trainable": 0}

            categories[param.category]["count"] += 1
            if param.trainable:
                categories[param.category]["trainable"] += 1
                trainable_count += 1

        return {
            "total_parameters": len(self.parameters),
            "trainable_parameters": trainable_count,
            "categories": categories,
            "current_version": self.current_version,
            "version_count": len(self.parameter_history)
        }
