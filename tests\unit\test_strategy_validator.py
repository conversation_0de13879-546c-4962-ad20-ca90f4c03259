"""
筛查策略配置验证系统测试

测试策略验证器的各种验证功能。
"""

import pytest
from unittest.mock import Mock

from src.modules.screening.strategy_validator import (
    StrategyValidator, ValidationResult, ValidationSeverity
)
from src.modules.screening.strategy import (
    ScreeningStrategy, ScreeningInterval, TargetPopulation, ScreeningFrequency
)
from src.modules.screening.enums import ScreeningToolType


class TestStrategyValidator:
    """测试策略验证器"""
    
    @pytest.fixture
    def validator(self):
        """创建验证器实例"""
        return StrategyValidator()
    
    @pytest.fixture
    def valid_strategy(self):
        """创建有效策略"""
        strategy = ScreeningStrategy(
            name="有效测试策略",
            description="这是一个用于测试的有效筛查策略，包含详细的描述信息",
            version="1.0",
            author="测试作者"
        )
        
        # 添加有效的筛查间隔
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL
        )
        strategy.add_interval(interval)
        
        return strategy
    
    @pytest.fixture
    def invalid_strategy(self):
        """创建无效策略"""
        strategy = ScreeningStrategy(
            name="",  # 无效名称
            description="",  # 无效描述
            version="",
            author=""
        )
        # 没有筛查间隔
        return strategy
    
    def test_validator_initialization(self, validator):
        """测试验证器初始化"""
        assert validator is not None
        assert len(validator.validation_rules) > 0
        assert 'basic_info' in validator.validation_rules
        assert 'intervals' in validator.validation_rules
    
    def test_validate_valid_strategy_basic(self, validator, valid_strategy):
        """测试验证有效策略（基础级别）"""
        results = validator.validate_strategy(valid_strategy, validation_level="basic")
        
        # 基础验证应该通过
        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) == 0
    
    def test_validate_valid_strategy_comprehensive(self, validator, valid_strategy):
        """测试验证有效策略（全面级别）"""
        results = validator.validate_strategy(valid_strategy, validation_level="comprehensive")
        
        # 可能有警告或信息，但不应该有错误
        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) == 0
    
    def test_validate_invalid_strategy(self, validator, invalid_strategy):
        """测试验证无效策略"""
        results = validator.validate_strategy(invalid_strategy)
        
        # 应该有错误
        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) > 0
        
        # 检查特定错误
        error_messages = [r.message for r in errors]
        assert any("策略名称不能为空" in msg for msg in error_messages)
        assert any("策略必须包含至少一个筛查间隔" in msg for msg in error_messages)
    
    def test_validate_basic_info(self, validator):
        """测试基本信息验证"""
        # 测试无效名称
        strategy = ScreeningStrategy(name="")
        results = validator._validate_basic_info(strategy)
        
        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) > 0
        assert any("策略名称不能为空" in r.message for r in errors)
        
        # 测试有效名称但缺少其他信息
        strategy.name = "测试策略"
        results = validator._validate_basic_info(strategy)
        
        # 应该有警告但没有错误
        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(errors) == 0
        assert len(warnings) > 0
    
    def test_validate_intervals_empty(self, validator):
        """测试验证空间隔列表"""
        strategy = ScreeningStrategy(name="测试策略")
        results = validator._validate_intervals(strategy)
        
        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) > 0
        assert any("策略必须包含至少一个筛查间隔" in r.message for r in errors)
    
    def test_validate_single_interval_invalid_ages(self, validator):
        """测试验证无效年龄范围的间隔"""
        # 创建一个有效的间隔，然后手动设置无效值
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL
        )
        # 手动设置无效年龄
        interval.start_age = 80
        interval.end_age = 50

        results = validator._validate_single_interval(interval, "test_interval")

        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) > 0
        assert any("开始年龄" in r.message and "结束年龄" in r.message for r in errors)
    
    def test_validate_tool_specific_config_fit(self, validator):
        """测试FIT工具特定配置验证"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.CUSTOM,
            custom_frequency_years=5.0  # FIT频率过长
        )
        
        results = validator._validate_tool_specific_config(interval, "test_interval")
        
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(warnings) > 0
        assert any("FIT筛查频率" in r.message and "过长" in r.message for r in warnings)
    
    def test_validate_tool_specific_config_colonoscopy(self, validator):
        """测试结肠镜工具特定配置验证"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=30,  # 开始年龄过早
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL  # 频率过短
        )
        
        results = validator._validate_tool_specific_config(interval, "test_interval")
        
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(warnings) >= 2  # 应该有年龄和频率两个警告
    
    def test_check_interval_overlaps(self, validator):
        """测试检查间隔重叠"""
        interval1 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=70
        )
        
        interval2 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=60,  # 与interval1重叠
            end_age=80
        )
        
        results = validator._check_interval_overlaps([interval1, interval2])
        
        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) > 0
        assert any("重叠" in r.message for r in errors)
    
    def test_check_tool_combinations_no_primary(self, validator):
        """测试检查工具组合（缺少初筛工具）"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=50,
            end_age=75
        )
        
        results = validator._check_tool_combinations([interval])
        
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(warnings) > 0
        assert any("初筛工具" in r.message for r in warnings)
    
    def test_validate_target_population_invalid_age(self, validator):
        """测试验证目标人群无效年龄"""
        strategy = ScreeningStrategy(name="测试策略")
        # 创建新的目标人群对象以避免验证
        from src.modules.screening.strategy import TargetPopulation
        strategy.target_population = TargetPopulation()
        strategy.target_population.age_range = (80, 50)  # 无效年龄范围

        results = validator._validate_target_population(strategy)

        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) > 0
        assert any("年龄" in r.message for r in errors)
    
    def test_validate_target_population_invalid_risk_level(self, validator):
        """测试验证目标人群无效风险水平"""
        strategy = ScreeningStrategy(name="测试策略")
        strategy.target_population.risk_level = "invalid_risk"
        
        results = validator._validate_target_population(strategy)
        
        errors = [r for r in results if r.severity == ValidationSeverity.ERROR]
        assert len(errors) > 0
        assert any("无效的风险水平" in r.message for r in errors)
    
    def test_validate_target_population_small_size(self, validator):
        """测试验证目标人群规模过小"""
        strategy = ScreeningStrategy(name="测试策略")
        strategy.target_population.population_size = 50  # 规模过小
        
        results = validator._validate_target_population(strategy)
        
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(warnings) > 0
        assert any("规模过小" in r.message for r in warnings)
    
    def test_validate_cost_effectiveness_low_budget(self, validator):
        """测试验证成本效益（预算过低）"""
        strategy = ScreeningStrategy(name="测试策略")
        strategy.budget_constraint = 500.0  # 预算过低
        
        results = validator._validate_cost_effectiveness(strategy)
        
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(warnings) > 0
        assert any("预算约束过低" in r.message for r in warnings)
    
    def test_validate_cost_effectiveness_low_threshold(self, validator):
        """测试验证成本效益（阈值过低）"""
        strategy = ScreeningStrategy(name="测试策略")
        strategy.cost_effectiveness_threshold = 5000.0  # 阈值过低
        
        results = validator._validate_cost_effectiveness(strategy)
        
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(warnings) > 0
        assert any("成本效益阈值过低" in r.message for r in warnings)
    
    def test_validate_clinical_consistency_high_risk(self, validator):
        """测试验证临床一致性（高风险人群）"""
        strategy = ScreeningStrategy(name="测试策略")
        strategy.target_population.risk_level = "high"
        
        # 添加低频率筛查间隔
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.CUSTOM,
            custom_frequency_years=5.0  # 高风险人群频率过低
        )
        strategy.add_interval(interval)
        
        results = validator._validate_clinical_consistency(strategy)
        
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(warnings) > 0
        assert any("高风险人群" in r.message and "频率" in r.message for r in warnings)
    
    def test_validate_implementation_feasibility_complex_tools(self, validator):
        """测试验证实施可行性（复杂工具过多）"""
        strategy = ScreeningStrategy(name="测试策略")
        
        # 添加多个复杂工具
        tools = [
            ScreeningToolType.COLONOSCOPY,
            ScreeningToolType.CTCOLONOGRAPHY,
            ScreeningToolType.CAPSULE_ENDOSCOPY
        ]
        
        for i, tool in enumerate(tools):
            interval = ScreeningInterval(
                tool_type=tool,
                start_age=50 + i * 5,
                end_age=75 + i * 5
            )
            strategy.add_interval(interval)
        
        results = validator._validate_implementation_feasibility(strategy)
        
        info_results = [r for r in results if r.severity == ValidationSeverity.INFO]
        assert len(info_results) > 0
        assert any("复杂筛查工具" in r.message for r in info_results)
    
    def test_validate_implementation_feasibility_high_frequency(self, validator):
        """测试验证实施可行性（高频率过多）"""
        strategy = ScreeningStrategy(name="测试策略")
        
        # 添加多个高频率间隔
        for i in range(3):
            interval = ScreeningInterval(
                tool_type=ScreeningToolType.FIT,
                start_age=50 + i * 10,
                end_age=60 + i * 10,
                frequency=ScreeningFrequency.CUSTOM,
                custom_frequency_years=0.5  # 高频率
            )
            strategy.add_interval(interval)
        
        results = validator._validate_implementation_feasibility(strategy)
        
        warnings = [r for r in results if r.severity == ValidationSeverity.WARNING]
        assert len(warnings) > 0
        assert any("高频率" in r.message and "依从性" in r.message for r in warnings)
    
    def test_get_validation_summary(self, validator):
        """测试获取验证结果摘要"""
        results = [
            ValidationResult(ValidationSeverity.ERROR, "错误1", "field1"),
            ValidationResult(ValidationSeverity.ERROR, "错误2", "field2"),
            ValidationResult(ValidationSeverity.WARNING, "警告1", "field1"),
            ValidationResult(ValidationSeverity.INFO, "信息1", "field3")
        ]
        
        summary = validator.get_validation_summary(results)
        
        assert summary["total_issues"] == 4
        assert summary["errors"] == 2
        assert summary["warnings"] == 1
        assert summary["info"] == 1
        assert summary["is_valid"] is False  # 有错误
        assert "issues_by_category" in summary
    
    def test_validation_levels(self, validator, valid_strategy):
        """测试不同验证级别"""
        # 基础级别
        basic_results = validator.validate_strategy(valid_strategy, "basic")
        
        # 标准级别
        standard_results = validator.validate_strategy(valid_strategy, "standard")
        
        # 全面级别
        comprehensive_results = validator.validate_strategy(valid_strategy, "comprehensive")
        
        # 全面级别应该检查更多项目
        assert len(comprehensive_results) >= len(standard_results)
        assert len(standard_results) >= len(basic_results)
    
    def test_validation_result_sorting(self, validator, invalid_strategy):
        """测试验证结果排序"""
        results = validator.validate_strategy(invalid_strategy)
        
        # 结果应该按严重程度排序（错误在前）
        if len(results) > 1:
            for i in range(len(results) - 1):
                current_severity = results[i].severity.value
                next_severity = results[i + 1].severity.value
                # 错误 < 警告 < 信息（按字母顺序）
                assert current_severity <= next_severity
