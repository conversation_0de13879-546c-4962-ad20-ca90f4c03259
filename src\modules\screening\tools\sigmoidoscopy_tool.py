"""
乙状结肠镜检查工具实现

实现乙状结肠镜筛查工具的特异性检测逻辑和性能参数。
"""

import random
from typing import Dict, Any, Optional
import logging

from src.core.enums import DiseaseState, AnatomicalLocation
from src.core.individual import Individual
from ..enums import ScreeningToolType, ScreeningResult, InvasivenessLevel, OperatorSkillLevel
from ..screening_tool import ScreeningTool, ScreeningPerformance, ScreeningCharacteristics


logger = logging.getLogger(__name__)


class SigmoidoscopyTool(ScreeningTool):
    """乙状结肠镜检查工具实现"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化乙状结肠镜工具
        
        Args:
            config: 配置参数字典
        """
        # 默认乙状结肠镜性能参数
        default_performance = ScreeningPerformance(
            sensitivity_by_state={
                DiseaseState.NORMAL: 0.0,
                DiseaseState.LOW_RISK_ADENOMA: 0.80,
                DiseaseState.HIGH_RISK_ADENOMA: 0.90,
                DiseaseState.SMALL_SERRATED: 0.70,  # 锯齿状腺瘤检测较困难
                DiseaseState.LARGE_SERRATED: 0.85,
                DiseaseState.PRECLINICAL_CANCER: 0.95,
                DiseaseState.CLINICAL_CANCER_STAGE_I: 0.98,
                DiseaseState.CLINICAL_CANCER_STAGE_II: 0.99,
                DiseaseState.CLINICAL_CANCER_STAGE_III: 0.995,
                DiseaseState.CLINICAL_CANCER_STAGE_IV: 1.0,
                DiseaseState.CLINICAL_CANCER: 0.98,  # 向后兼容
            },
            specificity=0.98,
            detection_threshold=None,
            # operator_dependency=1.8,   # 中高度依赖操作者技能（暂时注销）
            location_sensitivity_modifiers={
                AnatomicalLocation.PROXIMAL_COLON: 0.0,   # 无法检测近端结肠
                AnatomicalLocation.DISTAL_COLON: 1.0,     # 标准检测
                AnatomicalLocation.RECTUM: 1.0            # 标准检测
            }
            # age_sensitivity_adjustment={  # 暂时注销
            #     "slope": -0.0005,      # 年龄增加敏感性略降
            #     "baseline_age": 50.0,
            #     "min_sensitivity": 0.8,
            #     "max_sensitivity": 1.0
            # },
            # gender_sensitivity_modifiers={  # 暂时注销
            #     "male": 1.0,
            #     "female": 0.95  # 女性检查稍困难
            # }
        )
        
        # 默认乙状结肠镜特性
        default_characteristics = ScreeningCharacteristics(
            invasiveness=InvasivenessLevel.INVASIVE,
            operator_skill_required=OperatorSkillLevel.HIGH,
            preparation_required=True,  # 需要灌肠准备
            turnaround_time_days=0,     # 即时结果
            can_detect_proximal=False,  # 无法检测近端结肠
            can_detect_distal=True,
            can_detect_rectal=True,
            requires_sedation=False,    # 通常不需要镇静
            radiation_exposure=False,
            sample_collection_required=False
        )
        
        # 合并用户配置
        if config:
            if "sensitivity_by_state" in config:
                default_performance.sensitivity_by_state.update(
                    config["sensitivity_by_state"]
                )
            if "specificity" in config:
                default_performance.specificity = config["specificity"]
        
        super().__init__(
            tool_type=ScreeningToolType.SIGMOIDOSCOPY,
            performance=default_performance,
            characteristics=default_characteristics,
            config=config
        )
        
        # 乙状结肠镜特异性配置
        self.insertion_depth_cm = config.get("insertion_depth_cm", 60.0) if config else 60.0
        self.distal_adenoma_detection_rate = config.get("distal_adenoma_detection_rate", 0.20) if config else 0.20
    
    def perform_screening(
        self,
        individual: Individual,
        **kwargs
    ) -> ScreeningResult:
        """
        执行乙状结肠镜筛查检查
        
        Args:
            individual: 被筛查个体
            **kwargs: 其他参数
            
        Returns:
            ScreeningResult: 筛查结果
        """
        try:
            # 获取解剖位置
            anatomical_location = kwargs.get("anatomical_location")
            
            # 检查是否在检测范围内
            if not self._is_location_detectable(anatomical_location):
                # 近端结肠病变无法检测
                return ScreeningResult.NEGATIVE
            
            # 检查是否成功完成检查
            if not self._is_procedure_successful(individual, **kwargs):
                return ScreeningResult.INADEQUATE
            
            # 获取个体当前疾病状态
            current_state = individual.current_disease_state
            
            # 计算检测概率
            detection_probability = self.calculate_detection_probability(
                individual=individual,
                current_state=current_state,
                anatomical_location=anatomical_location
            )
            
            # 应用插入深度影响
            depth_factor = self._calculate_depth_factor(anatomical_location)
            adjusted_detection_probability = detection_probability * depth_factor
            
            # 计算特异性
            specificity = self.calculate_specificity(individual)
            
            # 模拟筛查结果
            if current_state == DiseaseState.NORMAL:
                # 正常状态：基于特异性判断是否假阳性
                if random.random() > specificity:
                    result = ScreeningResult.POSITIVE  # 假阳性
                else:
                    result = ScreeningResult.NEGATIVE  # 真阴性
            else:
                # 疾病状态：基于敏感性判断是否检出
                if random.random() < adjusted_detection_probability:
                    result = ScreeningResult.POSITIVE  # 真阳性
                else:
                    result = ScreeningResult.NEGATIVE  # 假阴性
            
            # 记录筛查日志
            logger.debug(
                f"乙状结肠镜筛查结果 - 个体ID: {getattr(individual, 'id', 'unknown')}, "
                f"状态: {current_state}, 位置: {anatomical_location}, 结果: {result}, "
                f"检测概率: {adjusted_detection_probability:.3f}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"乙状结肠镜筛查执行失败: {e}")
            return ScreeningResult.FAILED
    
    def _is_location_detectable(
        self,
        anatomical_location: Optional[AnatomicalLocation]
    ) -> bool:
        """
        判断解剖位置是否在乙状结肠镜检测范围内
        
        Args:
            anatomical_location: 解剖位置
            
        Returns:
            bool: 是否可检测
        """
        if anatomical_location is None:
            return True  # 假设在检测范围内
        
        # 乙状结肠镜只能检测远端结肠和直肠
        detectable_locations = {
            AnatomicalLocation.DISTAL_COLON,
            AnatomicalLocation.RECTUM
        }
        
        return anatomical_location in detectable_locations
    
    def _is_procedure_successful(
        self,
        individual: Individual,
        **kwargs
    ) -> bool:
        """
        判断乙状结肠镜检查是否成功完成
        
        Args:
            individual: 个体对象
            **kwargs: 其他参数
            
        Returns:
            bool: 是否成功完成
        """
        # 基础成功率（比全结肠镜高）
        base_success_rate = 0.98
        
        # 年龄调整
        age_factor = 1.0
        if individual.get_current_age() > 80:
            age_factor = 0.95
        
        # 性别调整
        gender_factor = 0.97 if individual.gender == "female" else 1.0
        
        # 肠道准备质量（要求较低）
        bowel_prep_quality = kwargs.get("bowel_prep_quality", 0.95)
        
        # 操作者经验
        operator_experience = kwargs.get("operator_experience", 1.0)
        
        # 计算最终成功率
        final_success_rate = (
            base_success_rate * 
            age_factor * 
            gender_factor * 
            bowel_prep_quality * 
            operator_experience
        )
        
        return random.random() < final_success_rate
    
    def _calculate_depth_factor(
        self,
        anatomical_location: Optional[AnatomicalLocation]
    ) -> float:
        """
        计算插入深度对检测的影响
        
        Args:
            anatomical_location: 解剖位置
            
        Returns:
            float: 深度影响因子
        """
        if anatomical_location == AnatomicalLocation.RECTUM:
            return 1.0  # 直肠检测最佳
        elif anatomical_location == AnatomicalLocation.DISTAL_COLON:
            # 远端结肠检测受插入深度影响
            if self.insertion_depth_cm >= 60:
                return 1.0
            elif self.insertion_depth_cm >= 40:
                return 0.9
            else:
                return 0.7
        else:
            return 0.0  # 近端结肠无法检测
    
    def calculate_proximal_cancer_miss_rate(self) -> float:
        """
        计算近端结肠癌症漏诊率
        
        Returns:
            float: 漏诊率
        """
        # 乙状结肠镜无法检测近端结肠
        # 基于流行病学数据，约40%的结直肠癌位于近端结肠
        proximal_cancer_proportion = 0.40
        return proximal_cancer_proportion
    
    def get_coverage_metrics(self) -> Dict[str, Any]:
        """获取检测覆盖范围指标"""
        return {
            "insertion_depth_cm": self.insertion_depth_cm,
            "can_detect_proximal": self.characteristics.can_detect_proximal,
            "can_detect_distal": self.characteristics.can_detect_distal,
            "can_detect_rectal": self.characteristics.can_detect_rectal,
            "proximal_cancer_miss_rate": self.calculate_proximal_cancer_miss_rate(),
            "distal_adenoma_detection_rate": self.distal_adenoma_detection_rate
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取乙状结肠镜工具性能指标"""
        return {
            "tool_type": "SIGMOIDOSCOPY",
            "specificity": self.performance.specificity,
            "sensitivity_cancer_distal": self.performance.sensitivity_by_state.get(
                DiseaseState.CLINICAL_CANCER_STAGE_I, 0.0
            ),
            "sensitivity_high_risk_adenoma": self.performance.sensitivity_by_state.get(
                DiseaseState.HIGH_RISK_ADENOMA, 0.0
            ),
            "turnaround_time_days": self.characteristics.turnaround_time_days,
            "invasiveness": self.characteristics.invasiveness.value,
            "preparation_required": self.characteristics.preparation_required,
            "requires_sedation": self.characteristics.requires_sedation,
            "insertion_depth_cm": self.insertion_depth_cm,
            "proximal_cancer_miss_rate": self.calculate_proximal_cancer_miss_rate()
        }
    
    def __str__(self) -> str:
        return f"乙状结肠镜工具 (特异性: {self.performance.specificity:.1%}, 插入深度: {self.insertion_depth_cm}cm)"
