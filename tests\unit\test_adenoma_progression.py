"""
测试腺瘤进展建模系统

测试基于年龄的正态分布腺瘤进展时间建模。
"""

import pytest
import numpy as np
from unittest.mock import patch

from src.core.enums import DiseaseState, Gender
from src.modules.disease.adenoma_progression import (
    AdenomaProgressionModel,
    ProgressionParameters,
    GenderProgressionMultipliers
)


class TestProgressionParameters:
    """测试进展参数"""

    def test_progression_parameters_creation(self):
        """测试进展参数创建"""
        params = ProgressionParameters(
            mean_years=5.0,
            std_years=2.0,
            min_years=1.0,
            max_years=15.0,
            age_adjustment_factor=-0.02
        )
        
        assert params.mean_years == 5.0
        assert params.std_years == 2.0
        assert params.min_years == 1.0
        assert params.max_years == 15.0
        assert params.age_adjustment_factor == -0.02


class TestGenderProgressionMultipliers:
    """测试性别进展倍数"""

    def test_gender_multipliers_creation(self):
        """测试性别倍数创建"""
        multipliers = GenderProgressionMultipliers(
            male_multiplier=0.9,
            female_multiplier=1.0
        )
        
        assert multipliers.male_multiplier == 0.9
        assert multipliers.female_multiplier == 1.0


class TestAdenomaProgressionModel:
    """测试腺瘤进展建模类"""

    def setup_method(self):
        """设置测试环境"""
        self.model = AdenomaProgressionModel()

    def test_model_initialization(self):
        """测试模型初始化"""
        assert self.model.progression_params is not None
        assert self.model.gender_multipliers is not None

    def test_default_progression_parameters(self):
        """测试默认进展参数"""
        params = self.model.progression_params
        
        assert "low_to_high_adenoma" in params
        assert "high_adenoma_to_preclinical" in params
        
        low_to_high = params["low_to_high_adenoma"]
        assert low_to_high.mean_years == 5.0
        assert low_to_high.std_years == 2.0
        assert low_to_high.min_years == 1.0
        assert low_to_high.max_years == 15.0
        
        high_to_preclinical = params["high_adenoma_to_preclinical"]
        assert high_to_preclinical.mean_years == 8.0
        assert high_to_preclinical.std_years == 3.0

    def test_default_gender_multipliers(self):
        """测试默认性别倍数"""
        multipliers = self.model.gender_multipliers
        
        assert multipliers.male_multiplier == 0.9
        assert multipliers.female_multiplier == 1.0

    def test_custom_parameters_initialization(self):
        """测试自定义参数初始化"""
        custom_params = {
            "low_to_high_adenoma": ProgressionParameters(
                mean_years=4.0,
                std_years=1.5,
                min_years=0.5,
                max_years=12.0,
                age_adjustment_factor=-0.01
            )
        }
        
        custom_gender = GenderProgressionMultipliers(
            male_multiplier=0.8,
            female_multiplier=1.1
        )
        
        model = AdenomaProgressionModel(
            progression_params=custom_params,
            gender_multipliers=custom_gender
        )
        
        assert model.progression_params["low_to_high_adenoma"].mean_years == 4.0
        assert model.gender_multipliers.male_multiplier == 0.8

    @patch('numpy.random.normal')
    def test_progression_time_calculation(self, mock_normal):
        """测试进展时间计算"""
        # 模拟正态分布抽样
        mock_normal.return_value = 5.0
        
        time = self.model.calculate_progression_time(
            transition_type="low_to_high_adenoma",
            age=55.0,
            gender=Gender.MALE
        )
        
        # 验证调用了正态分布
        mock_normal.assert_called_once_with(5.0, 2.0)
        
        # 验证年龄调整：5.0 + (55-50) * (-0.02) = 4.9
        # 验证性别调整：4.9 * 0.9 = 4.41
        expected_time = 4.41
        assert abs(time - expected_time) < 0.01

    def test_progression_time_boundary_limits(self):
        """测试进展时间边界限制"""
        # 测试多次以确保边界限制生效
        for _ in range(100):
            time = self.model.calculate_progression_time(
                transition_type="low_to_high_adenoma",
                age=50.0,
                gender=Gender.MALE
            )
            
            # 时间应该在边界范围内
            assert 1.0 <= time <= 15.0

    def test_invalid_transition_type(self):
        """测试无效转换类型"""
        with pytest.raises(ValueError, match="未知的转换类型"):
            self.model.calculate_progression_time(
                transition_type="invalid_transition",
                age=50.0,
                gender=Gender.MALE
            )

    def test_progression_probability_calculation(self):
        """测试进展概率计算"""
        prob = self.model.calculate_progression_probability(
            transition_type="low_to_high_adenoma",
            current_age=55.0,
            time_in_state=3.0,
            gender=Gender.MALE
        )
        
        # 概率应该在0-1范围内
        assert 0 <= prob <= 1
        assert isinstance(prob, float)

    def test_progression_probability_increases_with_time(self):
        """测试进展概率随时间增加"""
        prob_1_year = self.model.calculate_progression_probability(
            transition_type="low_to_high_adenoma",
            current_age=55.0,
            time_in_state=1.0,
            gender=Gender.MALE
        )
        
        prob_5_years = self.model.calculate_progression_probability(
            transition_type="low_to_high_adenoma",
            current_age=55.0,
            time_in_state=5.0,
            gender=Gender.MALE
        )
        
        # 时间越长，进展概率应该越高
        assert prob_5_years > prob_1_year

    def test_gender_difference_in_progression(self):
        """测试性别差异"""
        male_time = self.model.calculate_progression_time(
            transition_type="low_to_high_adenoma",
            age=50.0,
            gender=Gender.MALE
        )
        
        female_time = self.model.calculate_progression_time(
            transition_type="low_to_high_adenoma",
            age=50.0,
            gender=Gender.FEMALE
        )
        
        # 由于随机性，我们测试多次取平均值
        male_times = []
        female_times = []

        # 使用更大的样本量以减少随机波动
        for _ in range(1000):
            male_times.append(self.model.calculate_progression_time(
                "low_to_high_adenoma", 50.0, Gender.MALE
            ))
            female_times.append(self.model.calculate_progression_time(
                "low_to_high_adenoma", 50.0, Gender.FEMALE
            ))

        # 男性平均进展时间应该更短（倍数0.9 < 1.0）
        # 使用相对差异而不是绝对比较，允许一定的统计波动
        male_mean = np.mean(male_times)
        female_mean = np.mean(female_times)
        relative_difference = (female_mean - male_mean) / female_mean

        # 期望男性时间比女性短约10%，允许5%的误差
        assert relative_difference > 0.05, f"男性平均时间{male_mean:.3f}应该明显短于女性{female_mean:.3f}"

    def test_age_effect_on_progression(self):
        """测试年龄对进展的影响"""
        young_times = []
        old_times = []

        # 使用更大的样本量
        for _ in range(1000):
            young_times.append(self.model.calculate_progression_time(
                "low_to_high_adenoma", 40.0, Gender.MALE
            ))
            old_times.append(self.model.calculate_progression_time(
                "low_to_high_adenoma", 70.0, Gender.MALE
            ))

        # 年龄越大，进展时间应该越短（负的年龄调整因子）
        young_mean = np.mean(young_times)
        old_mean = np.mean(old_times)
        relative_difference = (young_mean - old_mean) / young_mean

        # 期望老年人进展时间明显更短，允许一定误差
        assert relative_difference > 0.02, f"老年人平均时间{old_mean:.3f}应该短于年轻人{young_mean:.3f}"

    def test_sample_progression_time(self):
        """测试进展时间抽样"""
        times = self.model.sample_progression_time(
            transition_type="low_to_high_adenoma",
            age=55.0,
            gender=Gender.MALE,
            n_samples=10
        )
        
        assert len(times) == 10
        assert isinstance(times, np.ndarray)
        
        # 所有时间都应该在合理范围内
        for time in times:
            assert 1.0 <= time <= 15.0

    def test_get_progression_distribution(self):
        """测试获取进展分布参数"""
        dist = self.model.get_progression_distribution(
            transition_type="low_to_high_adenoma",
            age=55.0,
            gender=Gender.MALE
        )
        
        assert "mean" in dist
        assert "std" in dist
        assert "min" in dist
        assert "max" in dist
        
        # 验证年龄和性别调整
        # 基础均值5.0，年龄调整：5.0 + (55-50)*(-0.02) = 4.9
        # 性别调整：4.9 * 0.9 = 4.41
        expected_mean = 4.41
        assert abs(dist["mean"] - expected_mean) < 0.01

    def test_validate_transition(self):
        """测试转换验证"""
        # 有效转换
        assert self.model.validate_transition(
            DiseaseState.LOW_RISK_ADENOMA,
            DiseaseState.HIGH_RISK_ADENOMA
        )
        
        assert self.model.validate_transition(
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.PRECLINICAL_CANCER
        )
        
        # 无效转换
        assert not self.model.validate_transition(
            DiseaseState.NORMAL,
            DiseaseState.HIGH_RISK_ADENOMA
        )

    def test_get_next_state(self):
        """测试获取下一状态"""
        next_state = self.model.get_next_state(DiseaseState.LOW_RISK_ADENOMA)
        assert next_state == DiseaseState.HIGH_RISK_ADENOMA
        
        next_state = self.model.get_next_state(DiseaseState.HIGH_RISK_ADENOMA)
        assert next_state == DiseaseState.PRECLINICAL_CANCER
        
        # 无效状态
        with pytest.raises(ValueError, match="无法从状态"):
            self.model.get_next_state(DiseaseState.NORMAL)

    def test_get_transition_type(self):
        """测试获取转换类型"""
        transition_type = self.model.get_transition_type(
            DiseaseState.LOW_RISK_ADENOMA,
            DiseaseState.HIGH_RISK_ADENOMA
        )
        assert transition_type == "low_to_high_adenoma"
        
        transition_type = self.model.get_transition_type(
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.PRECLINICAL_CANCER
        )
        assert transition_type == "high_adenoma_to_preclinical"
        
        # 无效转换
        with pytest.raises(ValueError, match="无效的转换"):
            self.model.get_transition_type(
                DiseaseState.NORMAL,
                DiseaseState.HIGH_RISK_ADENOMA
            )

    def test_parameter_updates(self):
        """测试参数更新"""
        new_params = ProgressionParameters(
            mean_years=6.0,
            std_years=2.5,
            min_years=0.5,
            max_years=18.0,
            age_adjustment_factor=-0.025
        )
        
        self.model.update_progression_parameters("low_to_high_adenoma", new_params)
        
        updated_params = self.model.progression_params["low_to_high_adenoma"]
        assert updated_params.mean_years == 6.0
        assert updated_params.std_years == 2.5

    def test_gender_multiplier_updates(self):
        """测试性别倍数更新"""
        new_multipliers = GenderProgressionMultipliers(
            male_multiplier=0.85,
            female_multiplier=1.05
        )
        
        self.model.update_gender_multipliers(new_multipliers)
        
        assert self.model.gender_multipliers.male_multiplier == 0.85
        assert self.model.gender_multipliers.female_multiplier == 1.05

    def test_get_model_parameters(self):
        """测试获取模型参数"""
        params = self.model.get_model_parameters()
        
        assert "progression_params" in params
        assert "gender_multipliers" in params
        
        progression_params = params["progression_params"]
        assert "low_to_high_adenoma" in progression_params
        assert "high_adenoma_to_preclinical" in progression_params
        
        low_to_high = progression_params["low_to_high_adenoma"]
        assert "mean_years" in low_to_high
        assert "std_years" in low_to_high
        assert "min_years" in low_to_high
        assert "max_years" in low_to_high
        assert "age_adjustment_factor" in low_to_high

    def test_progression_consistency(self):
        """测试进展计算一致性"""
        # 使用固定种子确保可重现性
        np.random.seed(42)
        
        time1 = self.model.calculate_progression_time(
            "low_to_high_adenoma", 55.0, Gender.MALE
        )
        
        np.random.seed(42)
        
        time2 = self.model.calculate_progression_time(
            "low_to_high_adenoma", 55.0, Gender.MALE
        )
        
        assert time1 == time2

    def test_edge_cases(self):
        """测试边界情况"""
        # 极端年龄
        time_young = self.model.calculate_progression_time(
            "low_to_high_adenoma", 20.0, Gender.MALE
        )
        assert 1.0 <= time_young <= 15.0
        
        time_old = self.model.calculate_progression_time(
            "low_to_high_adenoma", 90.0, Gender.MALE
        )
        assert 1.0 <= time_old <= 15.0
        
        # 极端时间在状态
        prob_zero = self.model.calculate_progression_probability(
            "low_to_high_adenoma", 55.0, 0.0, Gender.MALE
        )
        assert 0 <= prob_zero <= 1
        
        prob_large = self.model.calculate_progression_probability(
            "low_to_high_adenoma", 55.0, 100.0, Gender.MALE
        )
        assert 0 <= prob_large <= 1
