"""
筛查效果评估指标计算

实现筛查效果评估指标的计算，包括检出率、发病率降低、死亡率降低、
生存获益评估和成本效果比计算。
"""

import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import statistics

from src.core.individual import Individual
from src.core.enums import DiseaseState
from src.core.population import Population
from src.modules.screening.result_processor import ScreeningResult, ScreeningResultType
from src.modules.screening.enums import ScreeningToolType

logger = logging.getLogger(__name__)


@dataclass
class DetectionMetrics:
    """检出指标数据结构"""
    adenoma_detection_rate: float  # 腺瘤检出率 (每1000次筛查)
    preclinical_cancer_detection_rate: float  # 临床前癌症检出率
    clinical_cancer_detection_rate: float  # 临床癌症检出率
    early_stage_cancer_rate: float  # 早期癌症检出比例
    total_screenings: int
    total_adenomas_detected: int
    total_cancers_detected: int
    early_stage_cancers_detected: int


@dataclass
class IncidenceReductionMetrics:
    """发病率降低指标数据结构"""
    cancer_incidence_reduction: float  # 癌症发病率降低百分比
    advanced_cancer_reduction: float  # 晚期癌症发病率降低百分比
    baseline_incidence_rate: float  # 基线发病率 (每10万人年)
    screening_incidence_rate: float  # 筛查后发病率
    prevented_cancer_cases: int  # 预防的癌症病例数


@dataclass
class MortalityReductionMetrics:
    """死亡率降低指标数据结构"""
    cancer_mortality_reduction: float  # 癌症死亡率降低百分比
    overall_mortality_reduction: float  # 总体死亡率降低百分比
    baseline_mortality_rate: float  # 基线死亡率 (每10万人年)
    screening_mortality_rate: float  # 筛查后死亡率
    prevented_deaths: int  # 预防的死亡数


@dataclass
class SurvivalBenefitMetrics:
    """生存获益指标数据结构"""
    life_years_gained: float  # 寿命年挽回
    quality_adjusted_life_years: float  # 质量调整寿命年 (QALY)
    disability_adjusted_life_years: float  # 伤残调整寿命年 (DALY)
    average_life_extension: float  # 平均寿命延长 (年)
    survival_rate_improvement: float  # 生存率改善百分比


@dataclass
class CostEffectivenessMetrics:
    """成本效果指标数据结构"""
    cost_per_life_year_gained: float  # 每挽回寿命年成本
    cost_per_qaly: float  # 每QALY成本
    cost_per_cancer_prevented: float  # 每预防癌症病例成本
    cost_per_death_prevented: float  # 每预防死亡成本
    incremental_cost_effectiveness_ratio: float  # 增量成本效果比 (ICER)
    total_screening_costs: float  # 总筛查成本
    total_treatment_cost_savings: float  # 总治疗成本节省


class EffectivenessMetrics:
    """筛查效果评估指标计算器"""
    
    def __init__(self, population: Population):
        """
        初始化效果评估指标计算器
        
        Args:
            population: 人群对象
        """
        self.population = population
        self.screening_results: List[ScreeningResult] = []
        self.baseline_rates = self._load_baseline_rates()
        
        logger.info(f"初始化筛查效果评估指标计算器，人群规模: {len(population.individuals)}")

    def _load_baseline_rates(self) -> Dict[str, float]:
        """加载基线发病率和死亡率数据"""
        # 这里应该从配置文件或数据库加载真实的基线数据
        # 目前使用示例数据
        return {
            'cancer_incidence_per_100k': 50.0,  # 每10万人年癌症发病率
            'cancer_mortality_per_100k': 20.0,  # 每10万人年癌症死亡率
            'overall_mortality_per_100k': 800.0,  # 每10万人年总体死亡率
            'adenoma_prevalence': 0.25,  # 腺瘤患病率
            'cancer_5year_survival': 0.65  # 癌症5年生存率
        }

    def add_screening_results(self, results: List[ScreeningResult]) -> None:
        """添加筛查结果数据"""
        self.screening_results.extend(results)
        logger.info(f"添加 {len(results)} 个筛查结果，总计 {len(self.screening_results)} 个")

    def calculate_detection_metrics(self) -> DetectionMetrics:
        """
        计算检出相关指标
        
        Returns:
            检出指标对象
        """
        if not self.screening_results:
            return DetectionMetrics(0, 0, 0, 0, 0, 0, 0, 0)
        
        total_screenings = len(self.screening_results)
        
        # 统计检出的病变
        adenomas_detected = 0
        preclinical_cancers_detected = 0
        clinical_cancers_detected = 0
        early_stage_cancers = 0
        
        for result in self.screening_results:
            if result.is_true_positive:
                true_state = result.additional_data.get('true_disease_state')
                
                if true_state in ['low_risk_adenoma', 'high_risk_adenoma', 
                                'small_serrated', 'large_serrated']:
                    adenomas_detected += 1
                elif true_state == 'preclinical_cancer':
                    preclinical_cancers_detected += 1
                    early_stage_cancers += 1  # 临床前癌症算作早期
                elif true_state and true_state.startswith('clinical_cancer'):
                    clinical_cancers_detected += 1
                    # 根据分期判断是否为早期
                    if 'stage_i' in true_state or 'stage_ii' in true_state:
                        early_stage_cancers += 1
        
        total_cancers = preclinical_cancers_detected + clinical_cancers_detected
        
        # 计算检出率 (每1000次筛查)
        adenoma_rate = (adenomas_detected / total_screenings) * 1000 if total_screenings > 0 else 0
        preclinical_cancer_rate = (preclinical_cancers_detected / total_screenings) * 1000 if total_screenings > 0 else 0
        clinical_cancer_rate = (clinical_cancers_detected / total_screenings) * 1000 if total_screenings > 0 else 0
        early_stage_rate = (early_stage_cancers / total_cancers) if total_cancers > 0 else 0
        
        return DetectionMetrics(
            adenoma_detection_rate=adenoma_rate,
            preclinical_cancer_detection_rate=preclinical_cancer_rate,
            clinical_cancer_detection_rate=clinical_cancer_rate,
            early_stage_cancer_rate=early_stage_rate,
            total_screenings=total_screenings,
            total_adenomas_detected=adenomas_detected,
            total_cancers_detected=total_cancers,
            early_stage_cancers_detected=early_stage_cancers
        )

    def calculate_incidence_reduction(
        self, 
        follow_up_years: int = 10
    ) -> IncidenceReductionMetrics:
        """
        计算癌症发病率降低指标
        
        Args:
            follow_up_years: 随访年数
            
        Returns:
            发病率降低指标对象
        """
        baseline_incidence = self.baseline_rates['cancer_incidence_per_100k']
        
        # 计算筛查人群的癌症发病率
        population_size = len(self.population.individuals)
        
        # 统计筛查后仍发生的癌症
        cancers_after_screening = self._count_cancers_after_screening()
        
        # 计算筛查后发病率 (每10万人年)
        person_years = population_size * follow_up_years
        screening_incidence = (cancers_after_screening / person_years) * 100000
        
        # 计算发病率降低
        incidence_reduction = ((baseline_incidence - screening_incidence) / baseline_incidence) if baseline_incidence > 0 else 0
        
        # 估算预防的癌症病例数
        expected_cases = (baseline_incidence / 100000) * person_years
        prevented_cases = int(expected_cases - cancers_after_screening)
        
        # 计算晚期癌症降低 (假设筛查主要预防晚期癌症)
        advanced_cancer_reduction = incidence_reduction * 1.5  # 晚期癌症降低更明显
        
        return IncidenceReductionMetrics(
            cancer_incidence_reduction=incidence_reduction,
            advanced_cancer_reduction=min(1.0, advanced_cancer_reduction),
            baseline_incidence_rate=baseline_incidence,
            screening_incidence_rate=screening_incidence,
            prevented_cancer_cases=prevented_cases
        )

    def calculate_mortality_reduction(
        self, 
        follow_up_years: int = 10
    ) -> MortalityReductionMetrics:
        """
        计算死亡率降低指标
        
        Args:
            follow_up_years: 随访年数
            
        Returns:
            死亡率降低指标对象
        """
        baseline_mortality = self.baseline_rates['cancer_mortality_per_100k']
        
        # 计算筛查人群的癌症死亡率
        population_size = len(self.population.individuals)
        
        # 统计筛查后的癌症死亡
        cancer_deaths_after_screening = self._count_cancer_deaths_after_screening()
        
        # 计算筛查后死亡率 (每10万人年)
        person_years = population_size * follow_up_years
        screening_mortality = (cancer_deaths_after_screening / person_years) * 100000
        
        # 计算死亡率降低
        mortality_reduction = ((baseline_mortality - screening_mortality) / baseline_mortality) if baseline_mortality > 0 else 0
        
        # 估算预防的死亡数
        expected_deaths = (baseline_mortality / 100000) * person_years
        prevented_deaths = int(expected_deaths - cancer_deaths_after_screening)
        
        # 总体死亡率降低 (癌症死亡率降低对总体死亡率的影响)
        overall_mortality_reduction = mortality_reduction * (baseline_mortality / self.baseline_rates['overall_mortality_per_100k'])
        
        return MortalityReductionMetrics(
            cancer_mortality_reduction=mortality_reduction,
            overall_mortality_reduction=overall_mortality_reduction,
            baseline_mortality_rate=baseline_mortality,
            screening_mortality_rate=screening_mortality,
            prevented_deaths=prevented_deaths
        )

    def calculate_survival_benefit(self) -> SurvivalBenefitMetrics:
        """
        计算生存获益指标
        
        Returns:
            生存获益指标对象
        """
        # 计算寿命年挽回
        life_years_gained = 0.0
        qaly_gained = 0.0
        
        # 遍历筛查结果，计算每个真阳性检出的生存获益
        for result in self.screening_results:
            if result.is_true_positive:
                true_state = result.additional_data.get('true_disease_state')
                
                # 根据检出的疾病状态估算生存获益
                if true_state in ['low_risk_adenoma', 'high_risk_adenoma']:
                    # 腺瘤摘除的生存获益
                    life_years_gained += 0.5
                    qaly_gained += 0.4
                elif true_state in ['small_serrated', 'large_serrated']:
                    # 锯齿状腺瘤摘除的生存获益
                    life_years_gained += 0.7
                    qaly_gained += 0.6
                elif true_state == 'preclinical_cancer':
                    # 临床前癌症早期发现的显著生存获益
                    life_years_gained += 5.0
                    qaly_gained += 4.0
                elif true_state and true_state.startswith('clinical_cancer'):
                    # 临床癌症早期发现的生存获益
                    if 'stage_i' in true_state:
                        life_years_gained += 3.0
                        qaly_gained += 2.5
                    elif 'stage_ii' in true_state:
                        life_years_gained += 2.0
                        qaly_gained += 1.6
                    else:
                        life_years_gained += 1.0
                        qaly_gained += 0.8
        
        # 计算平均寿命延长
        screened_population = len(self.population.individuals)
        average_life_extension = life_years_gained / screened_population if screened_population > 0 else 0
        
        # 计算DALY (简化计算)
        daly_averted = qaly_gained * 0.9  # DALY通常略低于QALY
        
        # 计算生存率改善
        baseline_survival = self.baseline_rates['cancer_5year_survival']
        # 假设筛查可以提高5年生存率
        survival_improvement = min(0.2, life_years_gained / (screened_population * 5)) if screened_population > 0 else 0
        
        return SurvivalBenefitMetrics(
            life_years_gained=life_years_gained,
            quality_adjusted_life_years=qaly_gained,
            disability_adjusted_life_years=daly_averted,
            average_life_extension=average_life_extension,
            survival_rate_improvement=survival_improvement
        )

    def calculate_cost_effectiveness(
        self, 
        survival_metrics: SurvivalBenefitMetrics,
        incidence_metrics: IncidenceReductionMetrics,
        mortality_metrics: MortalityReductionMetrics
    ) -> CostEffectivenessMetrics:
        """
        计算成本效果指标
        
        Args:
            survival_metrics: 生存获益指标
            incidence_metrics: 发病率降低指标
            mortality_metrics: 死亡率降低指标
            
        Returns:
            成本效果指标对象
        """
        # 计算总筛查成本
        total_screening_costs = sum(result.cost for result in self.screening_results)
        
        # 计算假结果额外成本
        false_result_costs = sum(
            result.false_result_impact.additional_cost 
            for result in self.screening_results 
            if result.false_result_impact
        )
        
        total_costs = total_screening_costs + false_result_costs
        
        # 计算治疗成本节省 (早期发现减少的治疗成本)
        treatment_cost_savings = self._calculate_treatment_cost_savings()
        
        # 计算成本效果比
        cost_per_life_year = total_costs / survival_metrics.life_years_gained if survival_metrics.life_years_gained > 0 else float('inf')
        cost_per_qaly = total_costs / survival_metrics.quality_adjusted_life_years if survival_metrics.quality_adjusted_life_years > 0 else float('inf')
        cost_per_cancer_prevented = total_costs / incidence_metrics.prevented_cancer_cases if incidence_metrics.prevented_cancer_cases > 0 else float('inf')
        cost_per_death_prevented = total_costs / mortality_metrics.prevented_deaths if mortality_metrics.prevented_deaths > 0 else float('inf')
        
        # 计算增量成本效果比 (ICER)
        # 这里简化为成本每QALY，实际应该与无筛查策略比较
        icer = cost_per_qaly
        
        return CostEffectivenessMetrics(
            cost_per_life_year_gained=cost_per_life_year,
            cost_per_qaly=cost_per_qaly,
            cost_per_cancer_prevented=cost_per_cancer_prevented,
            cost_per_death_prevented=cost_per_death_prevented,
            incremental_cost_effectiveness_ratio=icer,
            total_screening_costs=total_screening_costs,
            total_treatment_cost_savings=treatment_cost_savings
        )

    def _count_cancers_after_screening(self) -> int:
        """统计筛查后仍发生的癌症数量"""
        # 这里需要根据个体的后续状态变化来统计
        # 简化实现：假设真阳性检出的癌症被预防
        true_positives = sum(1 for result in self.screening_results if result.is_true_positive)
        
        # 估算基线癌症发生数
        population_size = len(self.population.individuals)
        baseline_cancer_rate = self.baseline_rates['cancer_incidence_per_100k'] / 100000
        expected_cancers = int(population_size * baseline_cancer_rate)
        
        # 筛查后癌症数 = 预期癌症数 - 检出并预防的癌症数
        return max(0, expected_cancers - true_positives)

    def _count_cancer_deaths_after_screening(self) -> int:
        """统计筛查后的癌症死亡数量"""
        # 简化实现：基于死亡率和生存获益计算
        population_size = len(self.population.individuals)
        baseline_death_rate = self.baseline_rates['cancer_mortality_per_100k'] / 100000
        expected_deaths = int(population_size * baseline_death_rate)
        
        # 估算筛查预防的死亡数
        survival_benefit = self.calculate_survival_benefit()
        prevented_deaths = int(survival_benefit.life_years_gained / 10)  # 简化估算
        
        return max(0, expected_deaths - prevented_deaths)

    def _calculate_treatment_cost_savings(self) -> float:
        """计算治疗成本节省"""
        # 早期发现和治疗的成本节省
        cost_savings = 0.0
        
        for result in self.screening_results:
            if result.is_true_positive:
                true_state = result.additional_data.get('true_disease_state')
                
                # 根据检出的疾病状态估算成本节省
                if true_state in ['low_risk_adenoma', 'high_risk_adenoma']:
                    cost_savings += 5000.0  # 预防癌症的治疗成本节省
                elif true_state == 'preclinical_cancer':
                    cost_savings += 50000.0  # 早期癌症治疗成本节省
                elif true_state and true_state.startswith('clinical_cancer'):
                    if 'stage_i' in true_state:
                        cost_savings += 30000.0
                    elif 'stage_ii' in true_state:
                        cost_savings += 20000.0
        
        return cost_savings

    def generate_effectiveness_report(self) -> Dict[str, Any]:
        """
        生成筛查效果评估报告
        
        Returns:
            效果评估报告字典
        """
        detection_metrics = self.calculate_detection_metrics()
        incidence_metrics = self.calculate_incidence_reduction()
        mortality_metrics = self.calculate_mortality_reduction()
        survival_metrics = self.calculate_survival_benefit()
        cost_effectiveness = self.calculate_cost_effectiveness(
            survival_metrics, incidence_metrics, mortality_metrics
        )
        
        report = {
            'report_metadata': {
                'generation_time': datetime.now(),
                'population_size': len(self.population.individuals),
                'total_screenings': len(self.screening_results),
                'analysis_version': '1.0'
            },
            'detection_metrics': {
                'adenoma_detection_rate_per_1000': detection_metrics.adenoma_detection_rate,
                'cancer_detection_rate_per_1000': detection_metrics.preclinical_cancer_detection_rate + detection_metrics.clinical_cancer_detection_rate,
                'early_stage_cancer_rate': detection_metrics.early_stage_cancer_rate,
                'total_adenomas_detected': detection_metrics.total_adenomas_detected,
                'total_cancers_detected': detection_metrics.total_cancers_detected
            },
            'incidence_reduction': {
                'cancer_incidence_reduction_percent': incidence_metrics.cancer_incidence_reduction * 100,
                'prevented_cancer_cases': incidence_metrics.prevented_cancer_cases,
                'baseline_incidence_per_100k': incidence_metrics.baseline_incidence_rate,
                'screening_incidence_per_100k': incidence_metrics.screening_incidence_rate
            },
            'mortality_reduction': {
                'cancer_mortality_reduction_percent': mortality_metrics.cancer_mortality_reduction * 100,
                'prevented_deaths': mortality_metrics.prevented_deaths,
                'baseline_mortality_per_100k': mortality_metrics.baseline_mortality_rate,
                'screening_mortality_per_100k': mortality_metrics.screening_mortality_rate
            },
            'survival_benefit': {
                'total_life_years_gained': survival_metrics.life_years_gained,
                'total_qaly_gained': survival_metrics.quality_adjusted_life_years,
                'average_life_extension_years': survival_metrics.average_life_extension,
                'survival_rate_improvement_percent': survival_metrics.survival_rate_improvement * 100
            },
            'cost_effectiveness': {
                'cost_per_life_year_gained': cost_effectiveness.cost_per_life_year_gained,
                'cost_per_qaly': cost_effectiveness.cost_per_qaly,
                'cost_per_cancer_prevented': cost_effectiveness.cost_per_cancer_prevented,
                'icer': cost_effectiveness.incremental_cost_effectiveness_ratio,
                'total_screening_costs': cost_effectiveness.total_screening_costs,
                'treatment_cost_savings': cost_effectiveness.total_treatment_cost_savings
            }
        }
        
        return report
