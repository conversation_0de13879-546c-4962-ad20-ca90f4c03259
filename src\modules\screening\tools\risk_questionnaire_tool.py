"""
风险评估问卷工具实现

基于风险因素的筛查工具，通过问卷评估个体的结直肠癌风险。
"""

import logging
import random
from typing import Dict, Optional, Any

from src.core.enums import DiseaseState, AnatomicalLocation, Gender
from src.core.individual import Individual
from src.modules.disease import RiskCalculator, RiskLevel
from ..enums import ScreeningToolType, ScreeningResult, InvasivenessLevel, OperatorSkillLevel
from ..screening_tool import ScreeningTool, ScreeningPerformance, ScreeningCharacteristics

logger = logging.getLogger(__name__)


class RiskQuestionnaireTool(ScreeningTool):
    """风险评估问卷筛查工具"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化风险评估问卷工具

        Args:
            config: 工具配置字典
        """
        if config is None:
            config = {}

        # 默认风险问卷性能参数
        default_performance = ScreeningPerformance(
            sensitivity_by_state={
                DiseaseState.NORMAL: 0.0,
                DiseaseState.LOW_RISK_ADENOMA: 0.05,
                DiseaseState.HIGH_RISK_ADENOMA: 0.15,
                DiseaseState.PRECLINICAL_CANCER: 0.25,
                DiseaseState.CLINICAL_CANCER_STAGE_I: 0.40,
                DiseaseState.CLINICAL_CANCER_STAGE_II: 0.50,
                DiseaseState.CLINICAL_CANCER_STAGE_III: 0.60,
                DiseaseState.CLINICAL_CANCER_STAGE_IV: 0.70
            },
            specificity=0.70,
            location_sensitivity_modifiers={
                AnatomicalLocation.PROXIMAL_COLON: 1.0,
                AnatomicalLocation.DISTAL_COLON: 1.0,
                AnatomicalLocation.RECTUM: 1.0
            }
        )

        # 默认风险问卷特性
        default_characteristics = ScreeningCharacteristics(
            invasiveness=InvasivenessLevel.NON_INVASIVE,
            operator_skill_required=OperatorSkillLevel.LOW,
            preparation_required=False,
            turnaround_time_days=0,
            can_detect_proximal=False,
            can_detect_distal=False,
            can_detect_rectal=False
        )

        super().__init__(
            tool_type=ScreeningToolType.RISK_QUESTIONNAIRE,
            performance=default_performance,
            characteristics=default_characteristics,
            config=config
        )
        
        # 初始化风险计算器
        self.risk_calculator = RiskCalculator()
        
        # 风险等级到筛查结果的映射
        self.risk_threshold_config = config.get("risk_thresholds", {
            "low_risk_cutoff": 0.3,      # 低风险阈值
            "high_risk_cutoff": 0.7,     # 高风险阈值
            "recommend_screening": True   # 是否推荐进一步筛查
        })
        
        logger.info(f"风险评估问卷工具初始化完成，阈值配置: {self.risk_threshold_config}")
    
    def perform_screening(
        self, 
        individual: Individual, 
        **kwargs
    ) -> ScreeningResult:
        """
        执行风险评估问卷筛查
        
        Args:
            individual: 被筛查个体
            **kwargs: 额外参数
            
        Returns:
            ScreeningResult: 筛查结果
        """
        try:
            # 计算个体风险评分
            risk_score = self.risk_calculator.calculate_risk_score(
                individual_id=individual.individual_id,
                risk_profile=individual.risk_factor_profile,
                age=individual.get_current_age(),
                gender=individual.gender
            )
            
            # 根据风险等级确定筛查结果
            result = self._determine_screening_result(risk_score.risk_level, risk_score.final_score)
            
            # 记录筛查日志
            logger.debug(
                f"风险问卷筛查结果 - 个体ID: {individual.individual_id}, "
                f"风险等级: {risk_score.risk_level}, 风险评分: {risk_score.final_score:.3f}, "
                f"筛查结果: {result}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"风险评估问卷筛查失败: {e}")
            # 默认返回阴性结果
            return ScreeningResult.NEGATIVE
    
    def _determine_screening_result(self, risk_level: RiskLevel, risk_score: float) -> ScreeningResult:
        """
        根据风险等级和评分确定筛查结果
        
        Args:
            risk_level: 风险等级
            risk_score: 风险评分
            
        Returns:
            ScreeningResult: 筛查结果
        """
        low_cutoff = self.risk_threshold_config.get("low_risk_cutoff", 0.3)
        high_cutoff = self.risk_threshold_config.get("high_risk_cutoff", 0.7)
        
        if risk_level == RiskLevel.HIGH or risk_score >= high_cutoff:
            # 高风险：推荐进一步筛查（阳性）
            return ScreeningResult.POSITIVE
        elif risk_level == RiskLevel.MEDIUM or risk_score >= low_cutoff:
            # 中等风险：可能推荐筛查（根据配置决定）
            if self.risk_threshold_config.get("recommend_screening", True):
                return ScreeningResult.POSITIVE
            else:
                return ScreeningResult.NEGATIVE
        else:
            # 低风险：不推荐筛查（阴性）
            return ScreeningResult.NEGATIVE
    
    def calculate_detection_probability(self, individual: Individual) -> float:
        """
        计算检测概率（风险问卷不直接检测疾病，返回风险评分）
        
        Args:
            individual: 个体对象
            
        Returns:
            float: 风险评分（0-1之间）
        """
        try:
            risk_score = self.risk_calculator.calculate_risk_score(
                individual_id=individual.individual_id,
                risk_profile=individual.risk_factor_profile,
                age=individual.get_current_age(),
                gender=individual.gender
            )
            return min(1.0, max(0.0, risk_score.final_score))
        except Exception:
            return 0.0
    
    def calculate_specificity(self, individual: Individual) -> float:
        """
        计算特异性（风险问卷的特异性相对较低）
        
        Args:
            individual: 个体对象
            
        Returns:
            float: 特异性值
        """
        # 风险问卷的特异性通常较低，因为它基于风险因素而非直接检测
        base_specificity = self.performance.specificity
        
        # 可以根据个体特征进行调整
        # 这里保持简单，直接返回基础特异性
        return base_specificity
    
    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具详细信息"""
        base_info = super().get_tool_info()
        
        # 添加风险问卷特有信息
        base_info.update({
            "risk_thresholds": self.risk_threshold_config,
            "assessment_type": "risk_factor_based",
            "direct_detection": False,
            "requires_risk_profile": True
        })
        
        return base_info
    
    def validate_individual_eligibility(self, individual: Individual) -> bool:
        """
        验证个体是否适合进行风险评估问卷
        
        Args:
            individual: 个体对象
            
        Returns:
            bool: 是否适合
        """
        # 检查是否有风险因素档案
        if not hasattr(individual, 'risk_factor_profile') or individual.risk_factor_profile is None:
            logger.warning(f"个体 {individual.individual_id} 缺少风险因素档案，无法进行风险评估")
            return False
        
        # 检查年龄范围（通常适用于成年人）
        age = individual.get_current_age()
        if age < 18:
            logger.warning(f"个体 {individual.individual_id} 年龄过小 ({age}岁)，不适合风险评估问卷")
            return False
        
        return True
