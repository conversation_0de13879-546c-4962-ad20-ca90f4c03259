"""
成本调整系统单元测试
"""

import pytest
import math
from unittest.mock import patch, mock_open

from src.modules.economics.cost_adjustments import (
    CostAdjustmentEngine,
    InflationData,
    CurrencyRate
)


class TestCostAdjustmentEngine:
    """成本调整引擎测试"""
    
    @pytest.fixture
    def adjustment_engine(self):
        """创建成本调整引擎实例"""
        return CostAdjustmentEngine(base_year=2023, discount_rate=0.03)
    
    def test_initialization(self, adjustment_engine):
        """测试初始化"""
        assert adjustment_engine.base_year == 2023
        assert adjustment_engine.discount_rate == 0.03
        assert adjustment_engine.base_currency == "CNY"
        assert len(adjustment_engine.inflation_data) > 0  # 应该有默认通胀数据
    
    def test_present_value_calculation(self, adjustment_engine):
        """测试现值计算"""
        # 测试基本现值计算
        future_cost = 1000.0
        years = 5
        expected_pv = 1000.0 / ((1 + 0.03) ** 5)
        
        pv = adjustment_engine.present_value(future_cost, years)
        assert abs(pv - expected_pv) < 0.01
    
    def test_present_value_zero_years(self, adjustment_engine):
        """测试零年现值计算"""
        cost = 1000.0
        pv = adjustment_engine.present_value(cost, 0)
        assert pv == cost
    
    def test_present_value_custom_rate(self, adjustment_engine):
        """测试自定义折现率现值计算"""
        future_cost = 1000.0
        years = 3
        custom_rate = 0.05
        expected_pv = 1000.0 / ((1 + 0.05) ** 3)
        
        pv = adjustment_engine.present_value(future_cost, years, custom_rate)
        assert abs(pv - expected_pv) < 0.01
    
    def test_future_value_calculation(self, adjustment_engine):
        """测试未来值计算"""
        present_cost = 1000.0
        years = 5
        expected_fv = 1000.0 * ((1 + 0.03) ** 5)
        
        fv = adjustment_engine.future_value(present_cost, years)
        assert abs(fv - expected_fv) < 0.01
    
    def test_net_present_value(self, adjustment_engine):
        """测试净现值计算"""
        cost_stream = [
            (1000.0, 2023),  # 基准年
            (1100.0, 2024),  # 第一年
            (1200.0, 2025),  # 第二年
            (1300.0, 2026)   # 第三年
        ]
        
        npv = adjustment_engine.net_present_value(cost_stream)
        
        # 手动计算验证
        expected_npv = (
            1000.0 +  # 基准年，无折现
            1100.0 / (1.03 ** 1) +  # 第一年
            1200.0 / (1.03 ** 2) +  # 第二年
            1300.0 / (1.03 ** 3)    # 第三年
        )
        
        assert abs(npv - expected_npv) < 0.01
    
    def test_annualized_cost(self, adjustment_engine):
        """测试年化成本计算"""
        total_cost = 10000.0
        years = 5
        
        annualized = adjustment_engine.annualized_cost(total_cost, years)
        
        # 验证年化成本大于简单平均
        simple_average = total_cost / years
        assert annualized > simple_average
    
    def test_annualized_cost_zero_rate(self):
        """测试零折现率年化成本"""
        engine = CostAdjustmentEngine(discount_rate=0.0)
        total_cost = 10000.0
        years = 5
        
        annualized = engine.annualized_cost(total_cost, years)
        expected = total_cost / years
        
        assert abs(annualized - expected) < 0.01
    
    def test_adjust_for_inflation_same_year(self, adjustment_engine):
        """测试同年通胀调整"""
        cost = 1000.0
        adjusted = adjustment_engine.adjust_for_inflation(cost, 2023, 2023)
        assert adjusted == cost
    
    def test_adjust_for_inflation_future(self, adjustment_engine):
        """测试未来年份通胀调整"""
        cost = 1000.0
        # 从2023调整到2024
        adjusted = adjustment_engine.adjust_for_inflation(cost, 2023, 2024)
        
        # 应该大于原始成本（通胀）
        assert adjusted > cost
    
    def test_adjust_for_inflation_past(self, adjustment_engine):
        """测试历史年份通胀调整"""
        cost = 1000.0
        # 从2023调整到2020
        adjusted = adjustment_engine.adjust_for_inflation(cost, 2023, 2020)
        
        # 应该小于原始成本（反向通胀）
        assert adjusted < cost
    
    def test_add_currency_rate(self, adjustment_engine):
        """测试添加汇率"""
        adjustment_engine.add_currency_rate("CNY", "USD", 0.14, "2023-01-01")
        
        # 验证正向汇率
        assert "CNY_USD" in adjustment_engine.currency_rates
        assert adjustment_engine.currency_rates["CNY_USD"].exchange_rate == 0.14
        
        # 验证反向汇率
        assert "USD_CNY" in adjustment_engine.currency_rates
        assert abs(adjustment_engine.currency_rates["USD_CNY"].exchange_rate - (1/0.14)) < 0.001
    
    def test_convert_currency_same_currency(self, adjustment_engine):
        """测试相同货币转换"""
        amount = 1000.0
        converted = adjustment_engine.convert_currency(amount, "CNY", "CNY")
        assert converted == amount
    
    def test_convert_currency_with_rate(self, adjustment_engine):
        """测试带汇率的货币转换"""
        amount = 1000.0
        rate = 0.14
        converted = adjustment_engine.convert_currency(amount, "CNY", "USD", rate)
        expected = amount * rate
        assert abs(converted - expected) < 0.01
    
    def test_convert_currency_stored_rate(self, adjustment_engine):
        """测试使用存储汇率的货币转换"""
        adjustment_engine.add_currency_rate("CNY", "USD", 0.14)
        
        amount = 1000.0
        converted = adjustment_engine.convert_currency(amount, "CNY", "USD")
        expected = amount * 0.14
        assert abs(converted - expected) < 0.01
    
    def test_convert_currency_no_rate_error(self, adjustment_engine):
        """测试无汇率错误"""
        with pytest.raises(ValueError, match="无法找到汇率"):
            adjustment_engine.convert_currency(1000.0, "CNY", "EUR")
    
    def test_adjust_cost_to_base_year(self, adjustment_engine):
        """测试调整成本到基准年"""
        cost = 1000.0
        cost_year = 2020
        
        adjusted = adjustment_engine.adjust_cost_to_base_year(cost, cost_year)
        
        # 应该大于原始成本（通胀调整）
        assert adjusted > cost
    
    def test_adjust_cost_to_base_year_with_currency(self, adjustment_engine):
        """测试带货币转换的基准年调整"""
        adjustment_engine.add_currency_rate("CNY", "USD", 0.14)
        
        cost = 1000.0
        cost_year = 2020
        
        adjusted = adjustment_engine.adjust_cost_to_base_year(
            cost, cost_year, target_currency="USD"
        )
        
        # 应该是通胀调整后再货币转换
        inflation_adjusted = adjustment_engine.adjust_for_inflation(cost, cost_year, 2023)
        expected = inflation_adjusted * 0.14
        
        assert abs(adjusted - expected) < 0.01
    
    def test_calculate_real_discount_rate(self, adjustment_engine):
        """测试实际折现率计算"""
        nominal_rate = 0.06
        inflation_rate = 0.03
        
        real_rate = adjustment_engine.calculate_real_discount_rate(nominal_rate, inflation_rate)
        expected = (0.06 - 0.03) / (1 + 0.03)
        
        assert abs(real_rate - expected) < 0.001
    
    def test_sensitivity_analysis_discount_rate(self, adjustment_engine):
        """测试折现率敏感性分析"""
        cost_stream = [
            (1000.0, 2023),
            (1100.0, 2024),
            (1200.0, 2025)
        ]
        
        results = adjustment_engine.sensitivity_analysis_discount_rate(
            cost_stream, 
            rate_range=(0.01, 0.05), 
            steps=5
        )
        
        assert len(results) == 5
        
        # 验证结果结构
        for result in results:
            assert 'discount_rate' in result
            assert 'net_present_value' in result
            assert 'rate_percentage' in result
        
        # 验证NPV随折现率递减
        npvs = [r['net_present_value'] for r in results]
        for i in range(1, len(npvs)):
            assert npvs[i] <= npvs[i-1]  # NPV应该递减
    
    def test_get_inflation_rate(self, adjustment_engine):
        """测试获取通胀率"""
        # 测试存在的年份
        rate_2023 = adjustment_engine.get_inflation_rate(2023)
        assert isinstance(rate_2023, float)
        assert rate_2023 >= 0
        
        # 测试不存在的年份（应该返回默认值）
        rate_future = adjustment_engine.get_inflation_rate(2050)
        assert rate_future == 0.03
    
    def test_get_cpi_index(self, adjustment_engine):
        """测试获取CPI指数"""
        # 基准年应该是100
        cpi_base = adjustment_engine.get_cpi_index(2023)
        assert abs(cpi_base - 100.0) < 0.1
        
        # 未来年份应该大于100
        cpi_future = adjustment_engine.get_cpi_index(2025)
        assert cpi_future > 100.0
        
        # 历史年份应该小于100
        cpi_past = adjustment_engine.get_cpi_index(2020)
        assert cpi_past < 100.0
    
    def test_validate_adjustment_parameters_valid(self, adjustment_engine):
        """测试有效参数验证"""
        result = adjustment_engine.validate_adjustment_parameters()
        assert result['valid'] is True
        assert len(result['errors']) == 0
    
    def test_validate_adjustment_parameters_invalid_rate(self):
        """测试无效折现率验证"""
        engine = CostAdjustmentEngine(discount_rate=0.25)  # 25%折现率
        result = engine.validate_adjustment_parameters()
        
        # 应该有警告但仍然有效
        assert len(result['warnings']) > 0
    
    def test_validate_adjustment_parameters_invalid_year(self):
        """测试无效基准年验证"""
        engine = CostAdjustmentEngine(base_year=1950)  # 过早的基准年
        result = engine.validate_adjustment_parameters()
        
        # 应该有警告
        assert len(result['warnings']) > 0
