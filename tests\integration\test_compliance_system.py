"""
依从性建模系统集成测试

测试ComplianceModel、TemporalComplianceModel和ComplianceAnalytics
三个组件的协同工作和整体功能。
"""

import pytest
import yaml
from datetime import datetime, timedelta
from collections import namedtuple

from src.modules.screening.compliance_model import ComplianceModel, ScreeningEvent
from src.modules.screening.temporal_compliance import (
    TemporalComplianceModel, 
    TemporalComplianceParameters
)
from src.services.compliance_analytics import ComplianceAnalytics
from src.modules.screening.enums import ScreeningToolType, ScreeningResult
from src.core.individual import Individual
from src.core.enums import Gender


class TestComplianceSystemIntegration:
    """测试依从性建模系统集成"""
    
    @pytest.fixture
    def compliance_config(self):
        """提供依从性配置"""
        return {
            'base_rates': {
                'fit_screening': 0.65,
                'colonoscopy_screening': 0.45,
                'sigmoidoscopy_screening': 0.55
            },
            'temporal_patterns': {
                'first_time_multiplier': 0.8,
                'repeat_multiplier': 1.2,
                'time_decay_rate': 0.05
            },
            'followup_compliance': {
                'positive_fit_to_colonoscopy': 0.75,
                'waiting_time_impact': -0.02,
                'multiple_positive_decay': 0.9
            }
        }
    
    @pytest.fixture
    def temporal_parameters(self):
        """提供时间趋势参数"""
        return TemporalComplianceParameters(
            first_time_multiplier=0.8,
            repeat_multiplier=1.2,
            time_decay_rate=0.05,
            recovery_rate=0.1,
            max_decay_years=10,
            interruption_penalty=0.2,
            consistency_bonus=0.1
        )
    
    @pytest.fixture
    def compliance_system(self, compliance_config, temporal_parameters):
        """提供完整的依从性建模系统"""
        compliance_model = ComplianceModel(compliance_config)
        temporal_model = TemporalComplianceModel(temporal_parameters)
        analytics_service = ComplianceAnalytics(compliance_model, temporal_model)
        
        return {
            'compliance_model': compliance_model,
            'temporal_model': temporal_model,
            'analytics_service': analytics_service
        }
    
    @pytest.fixture
    def test_population(self):
        """提供测试人群"""
        MockPopulation = namedtuple('Population', ['individuals'])
        
        individuals = []
        for i in range(10):
            individual = Individual(
                birth_year=1960 + i,  # 年龄从65到56岁
                gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
                individual_id=f"test_ind_{i:03d}"
            )
            individuals.append(individual)
        
        return MockPopulation(individuals=individuals)
    
    @pytest.fixture
    def test_screening_history(self, test_population):
        """提供测试筛查历史"""
        screening_events = []
        base_date = datetime.now() - timedelta(days=365 * 3)  # 3年前开始
        
        for i, individual in enumerate(test_population.individuals):
            # 为每个个体创建不同的筛查模式
            if i < 3:
                # 一致性筛查者：每年一次FIT
                for year in range(3):
                    event = ScreeningEvent(
                        date=base_date + timedelta(days=365 * year + i * 10),
                        tool_type=ScreeningToolType.FIT,
                        result=ScreeningResult.NEGATIVE if year < 2 else ScreeningResult.POSITIVE,
                        individual_id=individual.individual_id
                    )
                    screening_events.append(event)
            
            elif i < 6:
                # 间歇性筛查者：不规律筛查
                intervals = [400, 300, 500]  # 不规律间隔
                for j, interval in enumerate(intervals):
                    event = ScreeningEvent(
                        date=base_date + timedelta(days=sum(intervals[:j+1])),
                        tool_type=ScreeningToolType.FIT,
                        result=ScreeningResult.NEGATIVE,
                        individual_id=individual.individual_id
                    )
                    screening_events.append(event)
            
            elif i < 8:
                # 结肠镜筛查者：5年一次
                event = ScreeningEvent(
                    date=base_date + timedelta(days=365),
                    tool_type=ScreeningToolType.COLONOSCOPY,
                    result=ScreeningResult.NEGATIVE,
                    individual_id=individual.individual_id
                )
                screening_events.append(event)
            
            # 其余个体无筛查历史
        
        return screening_events
    
    def test_compliance_probability_calculation(self, compliance_system, test_population, test_screening_history):
        """测试依从性概率计算的完整流程"""
        compliance_model = compliance_system['compliance_model']
        temporal_model = compliance_system['temporal_model']
        
        # 测试不同个体的依从性概率
        individual = test_population.individuals[0]  # 一致性筛查者
        individual_history = [
            event for event in test_screening_history 
            if event.individual_id == individual.individual_id
        ]
        
        # 使用基础依从性模型计算
        basic_prob = compliance_model.calculate_compliance_probability(
            individual, ScreeningToolType.FIT, individual_history
        )
        
        # 使用时间趋势模型计算时间因子
        temporal_factor = temporal_model.calculate_temporal_factor(
            individual, individual_history
        )
        
        # 验证计算结果合理性
        assert 0.0 <= basic_prob <= 1.0
        assert 0.1 <= temporal_factor <= 2.0
        
        # 验证一致性筛查者有较高的时间因子（包含一致性奖励）
        assert temporal_factor > 1.0
    
    def test_diagnostic_compliance_workflow(self, compliance_system, test_population, test_screening_history):
        """测试诊断依从性工作流程"""
        compliance_model = compliance_system['compliance_model']
        
        # 找到有阳性结果的个体
        positive_events = [
            event for event in test_screening_history 
            if event.result == ScreeningResult.POSITIVE
        ]
        
        if positive_events:
            positive_event = positive_events[0]
            individual = next(
                ind for ind in test_population.individuals 
                if ind.individual_id == positive_event.individual_id
            )
            
            individual_history = [
                event for event in test_screening_history 
                if event.individual_id == individual.individual_id
            ]
            
            # 测试不同等待时间的诊断依从性
            for waiting_weeks in [0, 2, 4, 8]:
                diagnostic_compliance = compliance_model.calculate_diagnostic_compliance(
                    individual, positive_event, waiting_weeks, individual_history
                )
                
                assert 0.0 <= diagnostic_compliance <= 1.0
                
                # 验证等待时间越长，依从性越低
                if waiting_weeks > 0:
                    no_wait_compliance = compliance_model.calculate_diagnostic_compliance(
                        individual, positive_event, 0, individual_history
                    )
                    assert diagnostic_compliance <= no_wait_compliance
    
    def test_temporal_pattern_analysis(self, compliance_system, test_population, test_screening_history):
        """测试时间模式分析"""
        temporal_model = compliance_system['temporal_model']
        
        # 测试不同筛查模式的个体
        for i, individual in enumerate(test_population.individuals[:6]):
            individual_history = [
                event for event in test_screening_history 
                if event.individual_id == individual.individual_id
            ]
            
            if individual_history:
                # 生成依从性趋势报告
                trend_report = temporal_model.generate_compliance_trend_report(
                    individual, test_screening_history
                )
                
                assert 'individual_id' in trend_report
                assert 'compliance_pattern' in trend_report
                assert 'current_temporal_factor' in trend_report
                
                # 验证一致性筛查者vs间歇性筛查者的模式识别
                if i < 3:  # 一致性筛查者
                    # 应该识别为一致性或恢复性模式
                    pattern = trend_report['compliance_pattern']
                    assert pattern in ['consistent', 'recovering']
                elif i < 6:  # 间歇性筛查者
                    # 应该识别为间歇性或下降性模式
                    pattern = trend_report['compliance_pattern']
                    assert pattern in ['intermittent', 'declining']
    
    def test_population_analytics(self, compliance_system, test_population, test_screening_history):
        """测试人群级别的分析功能"""
        analytics_service = compliance_system['analytics_service']
        
        # 模拟筛查事件收集
        analytics_service._collect_screening_events = lambda pop: test_screening_history
        
        # 生成人群依从性报告
        population_report = analytics_service.generate_compliance_report(test_population)
        
        # 验证报告结构
        assert 'overall_statistics' in population_report
        assert 'tool_analysis' in population_report
        assert 'demographic_analysis' in population_report
        assert 'temporal_trends' in population_report
        assert 'recommendations' in population_report
        
        # 验证统计数据
        overall_stats = population_report['overall_statistics']
        assert overall_stats['total_individuals'] == 10
        assert overall_stats['compliant_individuals'] > 0
        assert 0.0 <= overall_stats['compliance_rate'] <= 1.0
        
        # 验证工具分析
        tool_analysis = population_report['tool_analysis']
        assert 'fecal_immunochemical_test' in tool_analysis
        
        # 验证人口统计学分析
        demographic_analysis = population_report['demographic_analysis']
        assert 'gender' in demographic_analysis
        assert 'age_group' in demographic_analysis
    
    def test_intervention_prediction(self, compliance_system, test_population, test_screening_history):
        """测试干预措施效果预测"""
        analytics_service = compliance_system['analytics_service']
        
        # 模拟筛查事件收集
        analytics_service._collect_screening_events = lambda pop: test_screening_history
        
        # 定义干预措施
        interventions = [
            {'type': 'education', 'intensity': 'medium', 'coverage': 0.8},
            {'type': 'reminder', 'intensity': 'high', 'coverage': 0.9},
            {'type': 'incentive', 'intensity': 'low', 'coverage': 0.6}
        ]
        
        for intervention in interventions:
            prediction = analytics_service.predict_compliance_improvement(
                test_population, intervention
            )
            
            # 验证预测结果
            assert 'baseline_rate' in prediction
            assert 'predicted_rate' in prediction
            assert 'absolute_improvement' in prediction
            assert 'relative_improvement' in prediction
            assert 'affected_population' in prediction
            
            # 验证改善效果
            assert prediction['predicted_rate'] >= prediction['baseline_rate']
            assert prediction['absolute_improvement'] >= 0
            assert prediction['affected_population'] > 0
    
    def test_system_configuration_validation(self, compliance_system):
        """测试系统配置验证"""
        compliance_model = compliance_system['compliance_model']
        
        # 验证依从性率配置
        validation_results = compliance_model.validate_compliance_rates()
        
        # 所有验证项都应该通过
        assert all(validation_results.values()), f"验证失败: {validation_results}"
        
        # 验证参数范围
        for tool_type in [ScreeningToolType.FIT, ScreeningToolType.COLONOSCOPY, ScreeningToolType.SIGMOIDOSCOPY]:
            params = compliance_model.get_compliance_parameters(tool_type)
            
            assert 0.0 <= params.base_compliance_rate <= 1.0
            assert 0.0 <= params.positive_followup_rate <= 1.0
            assert 0.0 <= params.time_decay_factor <= 1.0
            assert params.first_time_multiplier >= 0
            assert params.repeat_multiplier >= 0
    
    def test_yaml_configuration_loading(self):
        """测试YAML配置文件加载"""
        config_path = "data/compliance_models/china_urban_compliance.yaml"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 验证配置结构
            assert 'compliance_model' in config_data
            compliance_config = config_data['compliance_model']
            
            assert 'base_rates' in compliance_config
            assert 'temporal_patterns' in compliance_config
            assert 'followup_compliance' in compliance_config
            
            # 创建依从性模型验证配置有效性
            model_config = {
                'base_rates': compliance_config['base_rates'],
                'temporal_patterns': compliance_config['temporal_patterns'],
                'followup_compliance': compliance_config['followup_compliance']
            }
            
            compliance_model = ComplianceModel(model_config)
            
            # 验证模型可以正常工作
            validation_results = compliance_model.validate_compliance_rates()
            assert all(validation_results.values())
            
        except FileNotFoundError:
            pytest.skip("配置文件不存在，跳过YAML配置测试")
    
    def test_performance_benchmarks(self, compliance_system, test_population, test_screening_history):
        """测试性能基准"""
        import time
        
        compliance_model = compliance_system['compliance_model']
        temporal_model = compliance_system['temporal_model']
        analytics_service = compliance_system['analytics_service']
        
        # 模拟筛查事件收集
        analytics_service._collect_screening_events = lambda pop: test_screening_history
        
        # 测试依从性概率计算性能
        start_time = time.time()
        for individual in test_population.individuals:
            individual_history = [
                event for event in test_screening_history 
                if event.individual_id == individual.individual_id
            ]
            compliance_model.calculate_compliance_probability(
                individual, ScreeningToolType.FIT, individual_history
            )
        calculation_time = time.time() - start_time
        
        # 测试人群分析性能
        start_time = time.time()
        population_report = analytics_service.generate_compliance_report(test_population)
        analysis_time = time.time() - start_time
        
        # 性能断言（应该在合理时间内完成）
        assert calculation_time < 1.0, f"依从性计算耗时过长: {calculation_time:.3f}秒"
        assert analysis_time < 2.0, f"人群分析耗时过长: {analysis_time:.3f}秒"
        
        print(f"性能基准 - 依从性计算: {calculation_time:.3f}秒, 人群分析: {analysis_time:.3f}秒")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
