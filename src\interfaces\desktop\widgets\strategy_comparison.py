"""
筛查策略比较和可视化组件

提供多个筛查策略的并排比较、差异分析和可视化展示功能。
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QSplitter, QFrame, QComboBox, QCheckBox,
    QScrollArea, QTabWidget, QTextEdit, QProgressBar,
    QMessageBox, QFileDialog, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot
from PyQt6.QtGui import QFont, QColor, QPalette, QIcon, QPixmap
# 尝试导入PyQt6.QtCharts，如果不可用则使用占位符
try:
    from PyQt6.QtCharts import QChart, QChartView, QBarSeries, QBarSet, QValueAxis, QBarCategoryAxis
    CHARTS_AVAILABLE = True
except ImportError:
    # 创建占位符类以避免导入错误
    from PyQt6.QtWidgets import QLabel

    class QChart:
        def __init__(self): pass
        def setTitle(self, title): pass
        def addSeries(self, series): pass
        def addAxis(self, axis, alignment): pass

    class QChartView(QLabel):
        def __init__(self):
            super().__init__()
            self.setText("图表功能不可用 - 需要安装PyQt6.QtCharts")
            self.setStyleSheet("border: 1px solid gray; padding: 20px; text-align: center;")

        def setChart(self, chart): pass
        def chart(self): return QChart()

    class QBarSeries:
        def __init__(self): pass
        def append(self, bar_set): pass
        def attachAxis(self, axis): pass

    class QBarSet:
        def __init__(self, name): pass
        def append(self, value): pass

    class QValueAxis:
        def __init__(self): pass
        def setRange(self, min_val, max_val): pass
        def setTitleText(self, title): pass

    class QBarCategoryAxis:
        def __init__(self): pass
        def append(self, categories): pass

    CHARTS_AVAILABLE = False

from src.modules.screening.strategy import ScreeningStrategy
from src.modules.screening.strategy_validator import StrategyValidator, ValidationSeverity
from src.modules.screening.template_manager import TemplateManager

logger = logging.getLogger(__name__)


class StrategyComparisonWidget(QWidget):
    """
    筛查策略比较组件
    
    提供多个策略的并排比较、差异分析和可视化功能。
    """
    
    # 信号定义
    comparison_updated = pyqtSignal(list)      # 比较更新信号
    strategy_selected = pyqtSignal(object)     # 策略选择信号
    export_requested = pyqtSignal(str, list)  # 导出请求信号
    
    def __init__(self, parent=None):
        """初始化策略比较组件"""
        super().__init__(parent)
        
        self.strategies: List[ScreeningStrategy] = []
        self.validator = StrategyValidator()
        self.template_manager = TemplateManager()
        
        self._setup_ui()
        self._connect_signals()
        
        logger.info("策略比较组件初始化完成")
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 顶部工具栏
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(main_splitter)
        
        # 策略选择和基本信息区域
        selection_area = self._create_selection_area()
        main_splitter.addWidget(selection_area)
        
        # 比较结果区域
        comparison_area = self._create_comparison_area()
        main_splitter.addWidget(comparison_area)
        
        # 设置分割器比例
        main_splitter.setStretchFactor(0, 1)  # 选择区域
        main_splitter.setStretchFactor(1, 3)  # 比较区域
    
    def _create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout(toolbar)
        
        # 添加策略按钮
        self.add_strategy_btn = QPushButton("添加策略")
        self.add_strategy_btn.setIcon(QIcon(":/icons/add.png"))
        layout.addWidget(self.add_strategy_btn)
        
        # 从模板加载按钮
        self.load_template_btn = QPushButton("从模板加载")
        self.load_template_btn.setIcon(QIcon(":/icons/template.png"))
        layout.addWidget(self.load_template_btn)
        
        # 移除策略按钮
        self.remove_strategy_btn = QPushButton("移除策略")
        self.remove_strategy_btn.setIcon(QIcon(":/icons/remove.png"))
        self.remove_strategy_btn.setEnabled(False)
        layout.addWidget(self.remove_strategy_btn)
        
        layout.addStretch()
        
        # 比较模式选择
        layout.addWidget(QLabel("比较模式:"))
        self.comparison_mode_combo = QComboBox()
        self.comparison_mode_combo.addItems([
            "基本信息比较",
            "详细配置比较", 
            "成本效益比较",
            "验证结果比较"
        ])
        layout.addWidget(self.comparison_mode_combo)
        
        # 导出按钮
        self.export_btn = QPushButton("导出比较")
        self.export_btn.setIcon(QIcon(":/icons/export.png"))
        layout.addWidget(self.export_btn)
        
        return toolbar
    
    def _create_selection_area(self) -> QWidget:
        """创建策略选择区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 策略列表标题
        title_label = QLabel("已选择的策略")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 策略列表表格
        self.strategy_table = QTableWidget()
        self.strategy_table.setColumnCount(6)
        self.strategy_table.setHorizontalHeaderLabels([
            "策略名称", "版本", "作者", "间隔数量", "验证状态", "操作"
        ])
        
        # 设置表格属性
        header = self.strategy_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        self.strategy_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.strategy_table.setAlternatingRowColors(True)
        self.strategy_table.setMaximumHeight(200)
        
        layout.addWidget(self.strategy_table)
        
        return widget
    
    def _create_comparison_area(self) -> QWidget:
        """创建比较结果区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 比较结果标签页
        self.comparison_tabs = QTabWidget()
        layout.addWidget(self.comparison_tabs)
        
        # 基本信息比较标签页
        basic_tab = self._create_basic_comparison_tab()
        self.comparison_tabs.addTab(basic_tab, "基本信息")
        
        # 详细配置比较标签页
        detail_tab = self._create_detail_comparison_tab()
        self.comparison_tabs.addTab(detail_tab, "详细配置")
        
        # 可视化比较标签页
        visual_tab = self._create_visual_comparison_tab()
        self.comparison_tabs.addTab(visual_tab, "可视化比较")
        
        # 差异分析标签页
        diff_tab = self._create_diff_analysis_tab()
        self.comparison_tabs.addTab(diff_tab, "差异分析")
        
        return widget
    
    def _create_basic_comparison_tab(self) -> QWidget:
        """创建基本信息比较标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本信息比较表格
        self.basic_comparison_table = QTableWidget()
        layout.addWidget(self.basic_comparison_table)
        
        return widget
    
    def _create_detail_comparison_tab(self) -> QWidget:
        """创建详细配置比较标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 详细配置比较表格
        self.detail_comparison_table = QTableWidget()
        layout.addWidget(self.detail_comparison_table)
        
        return widget
    
    def _create_visual_comparison_tab(self) -> QWidget:
        """创建可视化比较标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 图表选择
        chart_controls = QHBoxLayout()
        chart_controls.addWidget(QLabel("图表类型:"))
        
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "年龄范围比较",
            "筛查频率比较",
            "工具类型分布",
            "成本效益比较",
            "策略时间线"
        ])
        chart_controls.addWidget(self.chart_type_combo)
        
        chart_controls.addStretch()
        layout.addLayout(chart_controls)
        
        # 图表视图
        self.chart_view = QChartView()
        layout.addWidget(self.chart_view)
        
        return widget
    
    def _create_diff_analysis_tab(self) -> QWidget:
        """创建差异分析标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 差异分析结果
        self.diff_analysis_text = QTextEdit()
        self.diff_analysis_text.setReadOnly(True)
        layout.addWidget(self.diff_analysis_text)
        
        return widget
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 工具栏按钮信号
        self.add_strategy_btn.clicked.connect(self._add_strategy)
        self.load_template_btn.clicked.connect(self._load_from_template)
        self.remove_strategy_btn.clicked.connect(self._remove_strategy)
        self.export_btn.clicked.connect(self._export_comparison)
        
        # 比较模式变更信号
        self.comparison_mode_combo.currentTextChanged.connect(self._on_comparison_mode_changed)
        
        # 策略表格信号
        self.strategy_table.itemSelectionChanged.connect(self._on_strategy_selection_changed)
        
        # 图表类型变更信号
        self.chart_type_combo.currentTextChanged.connect(self._update_chart)
    
    @pyqtSlot()
    def _add_strategy(self) -> None:
        """添加策略"""
        # 这里应该打开策略选择对话框
        # 为了演示，我们创建一个简单的输入对话框
        QMessageBox.information(self, "添加策略", "策略添加功能正在开发中...")
    
    @pyqtSlot()
    def _load_from_template(self) -> None:
        """从模板加载策略"""
        try:
            templates = self.template_manager.list_templates()
            
            if not templates:
                QMessageBox.information(self, "提示", "没有可用的策略模板")
                return
            
            # 创建模板选择对话框
            template_names = [t['name'] for t in templates]
            # 这里应该创建一个更好的选择对话框
            QMessageBox.information(self, "模板加载", f"找到 {len(templates)} 个模板")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载模板失败: {str(e)}")
            logger.error(f"加载模板失败: {e}")
    
    @pyqtSlot()
    def _remove_strategy(self) -> None:
        """移除策略"""
        current_row = self.strategy_table.currentRow()
        if current_row < 0 or current_row >= len(self.strategies):
            return
        
        reply = QMessageBox.question(
            self, "确认移除",
            "确定要移除选中的策略吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 移除策略
            removed_strategy = self.strategies.pop(current_row)
            self._update_strategy_table()
            self._update_comparison()
            
            logger.info(f"移除策略: {removed_strategy.name}")
    
    @pyqtSlot()
    def _export_comparison(self) -> None:
        """导出比较结果"""
        if not self.strategies:
            QMessageBox.warning(self, "警告", "没有策略可以导出")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出比较结果",
            f"strategy_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
            "HTML文件 (*.html);;文本文件 (*.txt)"
        )
        
        if file_path:
            try:
                self._export_to_file(file_path)
                QMessageBox.information(self, "成功", f"比较结果已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
                logger.error(f"导出比较结果失败: {e}")
    
    @pyqtSlot(str)
    def _on_comparison_mode_changed(self, mode: str) -> None:
        """比较模式变更处理"""
        self._update_comparison()
    
    @pyqtSlot()
    def _on_strategy_selection_changed(self) -> None:
        """策略选择变更处理"""
        current_row = self.strategy_table.currentRow()
        self.remove_strategy_btn.setEnabled(current_row >= 0)
        
        if current_row >= 0 and current_row < len(self.strategies):
            selected_strategy = self.strategies[current_row]
            self.strategy_selected.emit(selected_strategy)
    
    @pyqtSlot(str)
    def _update_chart(self, chart_type: str) -> None:
        """更新图表"""
        if not self.strategies:
            return
        
        try:
            if chart_type == "年龄范围比较":
                self._create_age_range_chart()
            elif chart_type == "筛查频率比较":
                self._create_frequency_chart()
            elif chart_type == "工具类型分布":
                self._create_tool_distribution_chart()
            elif chart_type == "成本效益比较":
                self._create_cost_effectiveness_chart()
            elif chart_type == "策略时间线":
                self._create_timeline_chart()
        except Exception as e:
            logger.error(f"更新图表失败: {e}")
    
    def add_strategy(self, strategy: ScreeningStrategy) -> None:
        """添加策略到比较列表"""
        if strategy not in self.strategies:
            self.strategies.append(strategy)
            self._update_strategy_table()
            self._update_comparison()
            
            logger.info(f"添加策略到比较: {strategy.name}")
    
    def remove_strategy(self, strategy: ScreeningStrategy) -> bool:
        """从比较列表移除策略"""
        try:
            self.strategies.remove(strategy)
            self._update_strategy_table()
            self._update_comparison()
            
            logger.info(f"从比较中移除策略: {strategy.name}")
            return True
        except ValueError:
            return False
    
    def clear_strategies(self) -> None:
        """清空所有策略"""
        self.strategies.clear()
        self._update_strategy_table()
        self._update_comparison()
        
        logger.info("清空所有比较策略")
    
    def _update_strategy_table(self) -> None:
        """更新策略表格"""
        self.strategy_table.setRowCount(len(self.strategies))
        
        for row, strategy in enumerate(self.strategies):
            # 策略名称
            name_item = QTableWidgetItem(strategy.name)
            self.strategy_table.setItem(row, 0, name_item)
            
            # 版本
            version_item = QTableWidgetItem(strategy.version)
            self.strategy_table.setItem(row, 1, version_item)
            
            # 作者
            author_item = QTableWidgetItem(strategy.author)
            self.strategy_table.setItem(row, 2, author_item)
            
            # 间隔数量
            interval_count = QTableWidgetItem(str(len(strategy.intervals)))
            self.strategy_table.setItem(row, 3, interval_count)
            
            # 验证状态
            validation_results = self.validator.validate_strategy(strategy, "basic")
            errors = [r for r in validation_results if r.severity == ValidationSeverity.ERROR]
            status = "有效" if len(errors) == 0 else f"错误({len(errors)})"
            status_item = QTableWidgetItem(status)
            
            if len(errors) == 0:
                status_item.setBackground(QColor(200, 255, 200))  # 绿色背景
            else:
                status_item.setBackground(QColor(255, 200, 200))  # 红色背景
            
            self.strategy_table.setItem(row, 4, status_item)
            
            # 操作按钮（这里简化为文本）
            action_item = QTableWidgetItem("查看详情")
            self.strategy_table.setItem(row, 5, action_item)
    
    def _update_comparison(self) -> None:
        """更新比较结果"""
        if len(self.strategies) < 2:
            self._clear_comparison_results()
            return
        
        # 更新基本信息比较
        self._update_basic_comparison()
        
        # 更新详细配置比较
        self._update_detail_comparison()
        
        # 更新可视化比较
        self._update_chart(self.chart_type_combo.currentText())
        
        # 更新差异分析
        self._update_diff_analysis()
        
        # 发送比较更新信号
        self.comparison_updated.emit(self.strategies)
    
    def _update_basic_comparison(self) -> None:
        """更新基本信息比较"""
        if not self.strategies:
            return
        
        # 设置表格行和列
        comparison_fields = [
            "策略名称", "描述", "版本", "作者", "创建日期",
            "目标年龄范围", "风险水平", "间隔数量", "预算约束", "成本效益阈值"
        ]
        
        self.basic_comparison_table.setRowCount(len(comparison_fields))
        self.basic_comparison_table.setColumnCount(len(self.strategies))
        
        # 设置表头
        strategy_names = [s.name for s in self.strategies]
        self.basic_comparison_table.setHorizontalHeaderLabels(strategy_names)
        self.basic_comparison_table.setVerticalHeaderLabels(comparison_fields)
        
        # 填充数据
        for col, strategy in enumerate(self.strategies):
            data = [
                strategy.name,
                strategy.description[:50] + "..." if len(strategy.description) > 50 else strategy.description,
                strategy.version,
                strategy.author,
                strategy.created_date.strftime("%Y-%m-%d") if strategy.created_date else "",
                f"{strategy.target_population.age_range[0]}-{strategy.target_population.age_range[1]}岁",
                strategy.target_population.risk_level,
                str(len(strategy.intervals)),
                f"{strategy.budget_constraint:,.0f}元" if strategy.budget_constraint else "无限制",
                f"{strategy.cost_effectiveness_threshold:,.0f}元/QALY" if strategy.cost_effectiveness_threshold else "无限制"
            ]
            
            for row, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                self.basic_comparison_table.setItem(row, col, item)
        
        # 调整列宽
        self.basic_comparison_table.resizeColumnsToContents()
    
    def _update_detail_comparison(self) -> None:
        """更新详细配置比较"""
        if not self.strategies:
            return

        # 收集所有筛查间隔信息
        all_intervals = []
        for strategy in self.strategies:
            for interval in strategy.intervals:
                all_intervals.append({
                    'strategy': strategy.name,
                    'tool_type': interval.tool_type.display_name,
                    'frequency': interval.frequency_years,
                    'start_age': interval.start_age,
                    'end_age': interval.end_age,
                    'sequence_order': interval.sequence_order
                })

        # 设置表格
        if all_intervals:
            self.detail_comparison_table.setRowCount(len(all_intervals))
            self.detail_comparison_table.setColumnCount(6)
            self.detail_comparison_table.setHorizontalHeaderLabels([
                "策略名称", "筛查工具", "频率(年)", "开始年龄", "结束年龄", "执行顺序"
            ])

            # 填充数据
            for row, interval_data in enumerate(all_intervals):
                self.detail_comparison_table.setItem(row, 0, QTableWidgetItem(interval_data['strategy']))
                self.detail_comparison_table.setItem(row, 1, QTableWidgetItem(interval_data['tool_type']))
                self.detail_comparison_table.setItem(row, 2, QTableWidgetItem(f"{interval_data['frequency']:.1f}"))
                self.detail_comparison_table.setItem(row, 3, QTableWidgetItem(str(interval_data['start_age'])))
                self.detail_comparison_table.setItem(row, 4, QTableWidgetItem(str(interval_data['end_age'])))
                self.detail_comparison_table.setItem(row, 5, QTableWidgetItem(str(interval_data['sequence_order'])))

            # 调整列宽
            self.detail_comparison_table.resizeColumnsToContents()
        else:
            self.detail_comparison_table.clear()
    
    def _update_diff_analysis(self) -> None:
        """更新差异分析"""
        if len(self.strategies) < 2:
            self.diff_analysis_text.clear()
            return
        
        analysis_text = "策略差异分析报告\n"
        analysis_text += "=" * 50 + "\n\n"
        
        # 基本信息差异
        analysis_text += "1. 基本信息差异:\n"
        for i, strategy1 in enumerate(self.strategies):
            for j, strategy2 in enumerate(self.strategies[i+1:], i+1):
                analysis_text += f"\n{strategy1.name} vs {strategy2.name}:\n"
                
                # 年龄范围差异
                age1 = strategy1.target_population.age_range
                age2 = strategy2.target_population.age_range
                if age1 != age2:
                    analysis_text += f"  - 年龄范围: {age1[0]}-{age1[1]}岁 vs {age2[0]}-{age2[1]}岁\n"
                
                # 间隔数量差异
                if len(strategy1.intervals) != len(strategy2.intervals):
                    analysis_text += f"  - 间隔数量: {len(strategy1.intervals)} vs {len(strategy2.intervals)}\n"
        
        # 工具类型差异
        analysis_text += "\n2. 筛查工具差异:\n"
        for i, strategy in enumerate(self.strategies):
            tools = [interval.tool_type.display_name for interval in strategy.intervals]
            analysis_text += f"{strategy.name}: {', '.join(tools)}\n"
        
        self.diff_analysis_text.setPlainText(analysis_text)
    
    def _create_age_range_chart(self) -> None:
        """创建年龄范围比较图表"""
        if not CHARTS_AVAILABLE:
            chart = QChart()
            chart.setTitle("图表功能不可用 - 需要安装PyQt6.QtCharts")
            self.chart_view.setChart(chart)
            return

        chart = QChart()
        chart.setTitle("策略年龄范围比较")
        
        series = QBarSeries()
        
        start_ages = QBarSet("开始年龄")
        end_ages = QBarSet("结束年龄")
        
        categories = []
        for strategy in self.strategies:
            categories.append(strategy.name[:10] + "..." if len(strategy.name) > 10 else strategy.name)
            start_ages.append(strategy.target_population.age_range[0])
            end_ages.append(strategy.target_population.age_range[1])
        
        series.append(start_ages)
        series.append(end_ages)
        chart.addSeries(series)
        
        # 设置坐标轴
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        series.attachAxis(axis_x)
        
        axis_y = QValueAxis()
        axis_y.setRange(0, 100)
        axis_y.setTitleText("年龄")
        chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
        series.attachAxis(axis_y)
        
        self.chart_view.setChart(chart)
    
    def _create_frequency_chart(self) -> None:
        """创建筛查频率比较图表"""
        if not CHARTS_AVAILABLE:
            chart = QChart()
            chart.setTitle("图表功能不可用 - 需要安装PyQt6.QtCharts")
            self.chart_view.setChart(chart)
            return

        chart = QChart()
        chart.setTitle("筛查频率比较")

        series = QBarSeries()

        # 为每种工具类型创建一个数据集
        tool_types = set()
        for strategy in self.strategies:
            for interval in strategy.intervals:
                tool_types.add(interval.tool_type.display_name)

        tool_datasets = {}
        for tool_type in tool_types:
            tool_datasets[tool_type] = QBarSet(tool_type)

        categories = []
        for strategy in self.strategies:
            categories.append(strategy.name[:10] + "..." if len(strategy.name) > 10 else strategy.name)

            # 为每种工具类型收集频率数据
            strategy_tool_freq = {}
            for interval in strategy.intervals:
                tool_name = interval.tool_type.display_name
                if tool_name not in strategy_tool_freq:
                    strategy_tool_freq[tool_name] = []
                strategy_tool_freq[tool_name].append(interval.frequency_years)

            # 添加数据到对应的数据集
            for tool_type in tool_types:
                if tool_type in strategy_tool_freq:
                    # 使用平均频率
                    avg_freq = sum(strategy_tool_freq[tool_type]) / len(strategy_tool_freq[tool_type])
                    tool_datasets[tool_type].append(avg_freq)
                else:
                    tool_datasets[tool_type].append(0)

        # 添加所有数据集到系列
        for dataset in tool_datasets.values():
            series.append(dataset)

        chart.addSeries(series)

        # 设置坐标轴
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        series.attachAxis(axis_x)

        axis_y = QValueAxis()
        axis_y.setRange(0, 20)
        axis_y.setTitleText("筛查频率(年)")
        chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
        series.attachAxis(axis_y)

        self.chart_view.setChart(chart)
    
    def _create_tool_distribution_chart(self) -> None:
        """创建工具类型分布图表"""
        if not CHARTS_AVAILABLE:
            chart = QChart()
            chart.setTitle("图表功能不可用 - 需要安装PyQt6.QtCharts")
            self.chart_view.setChart(chart)
            return

        chart = QChart()
        chart.setTitle("筛查工具类型分布")

        series = QBarSeries()

        # 统计每个策略中各种工具的使用次数
        all_tool_types = set()
        for strategy in self.strategies:
            for interval in strategy.intervals:
                all_tool_types.add(interval.tool_type.display_name)

        # 为每种工具类型创建数据集
        tool_datasets = {}
        for tool_type in all_tool_types:
            tool_datasets[tool_type] = QBarSet(tool_type)

        categories = []
        for strategy in self.strategies:
            categories.append(strategy.name[:10] + "..." if len(strategy.name) > 10 else strategy.name)

            # 统计该策略中各工具的使用次数
            tool_counts = {}
            for interval in strategy.intervals:
                tool_name = interval.tool_type.display_name
                tool_counts[tool_name] = tool_counts.get(tool_name, 0) + 1

            # 添加数据到对应的数据集
            for tool_type in all_tool_types:
                count = tool_counts.get(tool_type, 0)
                tool_datasets[tool_type].append(count)

        # 添加所有数据集到系列
        for dataset in tool_datasets.values():
            series.append(dataset)

        chart.addSeries(series)

        # 设置坐标轴
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        series.attachAxis(axis_x)

        axis_y = QValueAxis()
        axis_y.setRange(0, 10)
        axis_y.setTitleText("使用次数")
        chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
        series.attachAxis(axis_y)

        self.chart_view.setChart(chart)
    
    def _create_cost_effectiveness_chart(self) -> None:
        """创建成本效益比较图表"""
        if not CHARTS_AVAILABLE:
            chart = QChart()
            chart.setTitle("图表功能不可用 - 需要安装PyQt6.QtCharts")
            self.chart_view.setChart(chart)
            return

        chart = QChart()
        chart.setTitle("成本效益比较")

        series = QBarSeries()

        budget_set = QBarSet("预算约束")
        threshold_set = QBarSet("成本效益阈值")

        categories = []
        for strategy in self.strategies:
            categories.append(strategy.name[:10] + "..." if len(strategy.name) > 10 else strategy.name)

            # 预算约束（转换为万元）
            budget = strategy.budget_constraint / 10000 if strategy.budget_constraint else 0
            budget_set.append(budget)

            # 成本效益阈值（转换为万元/QALY）
            threshold = strategy.cost_effectiveness_threshold / 10000 if strategy.cost_effectiveness_threshold else 0
            threshold_set.append(threshold)

        series.append(budget_set)
        series.append(threshold_set)
        chart.addSeries(series)

        # 设置坐标轴
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        series.attachAxis(axis_x)

        axis_y = QValueAxis()
        axis_y.setRange(0, 1000)  # 万元
        axis_y.setTitleText("金额(万元)")
        chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
        series.attachAxis(axis_y)

        self.chart_view.setChart(chart)

    def _create_timeline_chart(self) -> None:
        """创建策略时间线图表"""
        if not CHARTS_AVAILABLE:
            chart = QChart()
            chart.setTitle("图表功能不可用 - 需要安装PyQt6.QtCharts")
            self.chart_view.setChart(chart)
            return

        chart = QChart()
        chart.setTitle("策略执行时间线")

        series = QBarSeries()

        # 为每个策略创建时间线数据
        categories = []
        age_ranges = QBarSet("年龄跨度")

        for strategy in self.strategies:
            categories.append(strategy.name[:10] + "..." if len(strategy.name) > 10 else strategy.name)

            # 计算策略的总年龄跨度
            min_age = min(interval.start_age for interval in strategy.intervals) if strategy.intervals else 0
            max_age = max(interval.end_age for interval in strategy.intervals) if strategy.intervals else 0
            age_span = max_age - min_age

            age_ranges.append(age_span)

        series.append(age_ranges)
        chart.addSeries(series)

        # 设置坐标轴
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        series.attachAxis(axis_x)

        axis_y = QValueAxis()
        axis_y.setRange(0, 80)
        axis_y.setTitleText("年龄跨度(年)")
        chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
        series.attachAxis(axis_y)

        self.chart_view.setChart(chart)

    def _clear_comparison_results(self) -> None:
        """清空比较结果"""
        self.basic_comparison_table.clear()
        self.detail_comparison_table.clear()
        self.diff_analysis_text.clear()
        
        # 清空图表
        empty_chart = QChart()
        empty_chart.setTitle("请选择至少两个策略进行比较")
        self.chart_view.setChart(empty_chart)
    
    def _export_to_file(self, file_path: str) -> None:
        """导出比较结果到文件"""
        if file_path.endswith('.html'):
            self._export_to_html(file_path)
        else:
            self._export_to_text(file_path)
    
    def _export_to_html(self, file_path: str) -> None:
        """导出为HTML格式"""
        html_content = f"""
        <html>
        <head>
            <title>筛查策略比较报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .strategy-name {{ font-weight: bold; color: #2c3e50; }}
                .section {{ margin: 20px 0; }}
            </style>
        </head>
        <body>
            <h1>筛查策略比较报告</h1>
            <div class="section">
                <p><strong>生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>比较策略数量:</strong> {len(self.strategies)}</p>
            </div>

            <div class="section">
                <h2>策略基本信息</h2>
                <table>
                    <tr>
                        <th>策略名称</th>
                        <th>版本</th>
                        <th>作者</th>
                        <th>目标年龄范围</th>
                        <th>间隔数量</th>
                        <th>预算约束</th>
                    </tr>
        """

        for strategy in self.strategies:
            age_range = f"{strategy.target_population.age_range[0]}-{strategy.target_population.age_range[1]}岁"
            budget = f"{strategy.budget_constraint:,.0f}元" if strategy.budget_constraint else "无限制"
            html_content += f"""
                    <tr>
                        <td class="strategy-name">{strategy.name}</td>
                        <td>{strategy.version}</td>
                        <td>{strategy.author}</td>
                        <td>{age_range}</td>
                        <td>{len(strategy.intervals)}</td>
                        <td>{budget}</td>
                    </tr>
            """

        html_content += """
                </table>
            </div>

            <div class="section">
                <h2>详细配置比较</h2>
                <table>
                    <tr>
                        <th>策略名称</th>
                        <th>筛查工具</th>
                        <th>频率(年)</th>
                        <th>开始年龄</th>
                        <th>结束年龄</th>
                        <th>执行顺序</th>
                    </tr>
        """

        for strategy in self.strategies:
            for interval in strategy.intervals:
                html_content += f"""
                    <tr>
                        <td>{strategy.name}</td>
                        <td>{interval.tool_type.display_name}</td>
                        <td>{interval.frequency_years:.1f}</td>
                        <td>{interval.start_age}</td>
                        <td>{interval.end_age}</td>
                        <td>{interval.sequence_order}</td>
                    </tr>
                """

        html_content += """
                </table>
            </div>

            <div class="section">
                <h2>差异分析</h2>
                <pre>
        """

        html_content += self.diff_analysis_text.toPlainText()

        html_content += """
                </pre>
            </div>
        </body>
        </html>
        """

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _export_to_text(self, file_path: str) -> None:
        """导出为文本格式"""
        content = f"筛查策略比较报告\n"
        content += "=" * 50 + "\n"
        content += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += f"比较策略数量: {len(self.strategies)}\n\n"

        content += "策略基本信息:\n"
        content += "-" * 30 + "\n"
        for i, strategy in enumerate(self.strategies, 1):
            age_range = f"{strategy.target_population.age_range[0]}-{strategy.target_population.age_range[1]}岁"
            budget = f"{strategy.budget_constraint:,.0f}元" if strategy.budget_constraint else "无限制"
            content += f"{i}. {strategy.name} (v{strategy.version})\n"
            content += f"   作者: {strategy.author}\n"
            content += f"   目标年龄: {age_range}\n"
            content += f"   间隔数量: {len(strategy.intervals)}\n"
            content += f"   预算约束: {budget}\n\n"

        content += "详细配置:\n"
        content += "-" * 30 + "\n"
        for strategy in self.strategies:
            content += f"\n{strategy.name}:\n"
            for interval in strategy.intervals:
                content += f"  - {interval.tool_type.display_name}: "
                content += f"{interval.frequency_years:.1f}年频率, "
                content += f"{interval.start_age}-{interval.end_age}岁, "
                content += f"顺序{interval.sequence_order}\n"

        content += "\n差异分析:\n"
        content += "-" * 30 + "\n"
        content += self.diff_analysis_text.toPlainText()

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
