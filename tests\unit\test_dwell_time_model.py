"""
疾病状态停留时间建模系统单元测试

测试DwellTimeModel类的各项功能，包括参数配置、随机抽样、统计功能等。
"""

import pytest
import numpy as np
from unittest.mock import patch, MagicMock
from scipy import stats

from src.modules.disease.dwell_time_model import (
    DwellTimeModel,
    DwellTimeParameters,
    DwellTimeStatistics,
    DistributionType
)


class TestDwellTimeParameters:
    """测试DwellTimeParameters数据类"""
    
    def test_valid_parameters(self):
        """测试有效参数创建"""
        params = DwellTimeParameters(
            distribution_type=DistributionType.NORMAL,
            mean=5.0,
            std=2.0,
            min_time=1.0,
            max_time=15.0
        )
        
        assert params.distribution_type == DistributionType.NORMAL
        assert params.mean == 5.0
        assert params.std == 2.0
        assert params.min_time == 1.0
        assert params.max_time == 15.0
    
    def test_invalid_mean(self):
        """测试无效的平均值"""
        with pytest.raises(ValueError, match="平均停留时间必须大于0"):
            DwellTimeParameters(
                distribution_type=DistributionType.NORMAL,
                mean=0.0,
                std=2.0
            )
    
    def test_invalid_std(self):
        """测试无效的标准差"""
        with pytest.raises(ValueError, match="标准差必须大于0"):
            DwellTimeParameters(
                distribution_type=DistributionType.NORMAL,
                mean=5.0,
                std=0.0
            )
    
    def test_invalid_time_range(self):
        """测试无效的时间范围"""
        with pytest.raises(ValueError, match="最大停留时间必须大于最小停留时间"):
            DwellTimeParameters(
                distribution_type=DistributionType.NORMAL,
                mean=5.0,
                std=2.0,
                min_time=10.0,
                max_time=5.0
            )


class TestDwellTimeStatistics:
    """测试DwellTimeStatistics数据类"""
    
    def test_initial_state(self):
        """测试初始状态"""
        stats = DwellTimeStatistics(state="test_state")
        
        assert stats.state == "test_state"
        assert stats.sample_count == 0
        assert stats.mean_time == 0.0
        assert stats.std_time == 0.0
        assert stats.min_time == float('inf')
        assert stats.max_time == 0.0
        assert len(stats.samples) == 0
    
    def test_update_statistics_single_sample(self):
        """测试单个样本的统计更新"""
        stats = DwellTimeStatistics(state="test_state")
        stats.update_statistics(5.0)
        
        assert stats.sample_count == 1
        assert stats.mean_time == 5.0
        assert stats.std_time == 0.0
        assert stats.min_time == 5.0
        assert stats.max_time == 5.0
        assert len(stats.samples) == 1
    
    def test_update_statistics_multiple_samples(self):
        """测试多个样本的统计更新"""
        stats = DwellTimeStatistics(state="test_state")
        samples = [3.0, 5.0, 7.0, 4.0, 6.0]
        
        for sample in samples:
            stats.update_statistics(sample)
        
        assert stats.sample_count == 5
        assert stats.mean_time == 5.0
        assert stats.std_time == pytest.approx(1.58, rel=1e-2)
        assert stats.min_time == 3.0
        assert stats.max_time == 7.0
    
    def test_get_summary(self):
        """测试统计摘要"""
        stats = DwellTimeStatistics(state="test_state")
        samples = [1.0, 2.0, 3.0, 4.0, 5.0]
        
        for sample in samples:
            stats.update_statistics(sample)
        
        summary = stats.get_summary()
        
        assert summary["sample_count"] == 5
        assert summary["mean_time"] == 3.0
        assert summary["min_time"] == 1.0
        assert summary["max_time"] == 5.0
        assert summary["median_time"] == 3.0
        assert summary["q25"] == 2.0
        assert summary["q75"] == 4.0


class TestDwellTimeModel:
    """测试DwellTimeModel类"""
    
    def test_initialization_default(self):
        """测试默认初始化"""
        model = DwellTimeModel()
        
        # 检查默认状态是否已初始化
        expected_states = [
            "normal", "low_risk_adenoma", "high_risk_adenoma", 
            "preclinical_cancer", "clinical_cancer_stage_i",
            "clinical_cancer_stage_ii", "clinical_cancer_stage_iii",
            "clinical_cancer_stage_iv"
        ]
        
        for state in expected_states:
            assert state in model.state_parameters
            assert state in model.statistics
    
    def test_initialization_with_config(self):
        """测试使用配置初始化"""
        config = {
            "test_state": {
                "distribution_type": "normal",
                "mean": 10.0,
                "std": 3.0,
                "min_time": 2.0,
                "max_time": 20.0
            }
        }
        
        model = DwellTimeModel(config=config)
        
        assert "test_state" in model.state_parameters
        params = model.state_parameters["test_state"]
        assert params.distribution_type == DistributionType.NORMAL
        assert params.mean == 10.0
        assert params.std == 3.0
        assert params.min_time == 2.0
        assert params.max_time == 20.0
    
    def test_sample_dwell_time_normal_distribution(self):
        """测试正态分布的停留时间抽样"""
        model = DwellTimeModel()
        model.set_random_seed(42)  # 设置种子以确保可重现性
        
        samples = []
        for _ in range(100):
            sample = model.sample_dwell_time("low_risk_adenoma")
            samples.append(sample)
        
        # 检查样本是否在合理范围内
        assert all(1.0 <= s <= 15.0 for s in samples)
        
        # 检查样本的统计特性
        mean_sample = np.mean(samples)
        std_sample = np.std(samples, ddof=1)
        
        # 应该接近配置的参数（允许一定误差）
        assert abs(mean_sample - 5.0) < 1.0
        assert abs(std_sample - 2.0) < 1.0
    
    def test_sample_dwell_time_with_age_adjustment(self):
        """测试带年龄调整的停留时间抽样"""
        model = DwellTimeModel()
        model.set_random_seed(42)
        
        # 年轻患者
        young_sample = model.sample_dwell_time("low_risk_adenoma", age=40)
        
        # 老年患者
        old_sample = model.sample_dwell_time("low_risk_adenoma", age=70)
        
        # 由于年龄调整，老年患者的停留时间应该更短
        # 注意：这是统计趋势，单个样本可能有例外
        model.set_random_seed(42)
        young_samples = [model.sample_dwell_time("low_risk_adenoma", age=40) for _ in range(50)]
        
        model.set_random_seed(42)
        old_samples = [model.sample_dwell_time("low_risk_adenoma", age=70) for _ in range(50)]
        
        assert np.mean(old_samples) < np.mean(young_samples)
    
    def test_sample_dwell_time_with_gender_adjustment(self):
        """测试带性别调整的停留时间抽样"""
        model = DwellTimeModel()
        model.set_random_seed(42)
        
        # 男性患者
        male_samples = [model.sample_dwell_time("low_risk_adenoma", gender="male") for _ in range(50)]
        
        model.set_random_seed(42)
        # 女性患者
        female_samples = [model.sample_dwell_time("low_risk_adenoma", gender="female") for _ in range(50)]
        
        # 根据配置，女性的停留时间应该稍长
        assert np.mean(female_samples) > np.mean(male_samples)
    
    def test_sample_dwell_time_invalid_state(self):
        """测试无效状态的抽样"""
        model = DwellTimeModel()
        
        with pytest.raises(ValueError, match="未知的疾病状态"):
            model.sample_dwell_time("invalid_state")
    
    def test_get_state_parameters(self):
        """测试获取状态参数"""
        model = DwellTimeModel()
        
        params = model.get_state_parameters("low_risk_adenoma")
        assert params is not None
        assert params.mean == 5.0
        assert params.std == 2.0
        
        # 测试不存在的状态
        assert model.get_state_parameters("nonexistent") is None
    
    def test_update_state_parameters(self):
        """测试更新状态参数"""
        model = DwellTimeModel()
        
        new_params = DwellTimeParameters(
            distribution_type=DistributionType.LOGNORMAL,
            mean=10.0,
            std=4.0,
            min_time=2.0,
            max_time=25.0
        )
        
        model.update_state_parameters("low_risk_adenoma", new_params)
        
        updated_params = model.get_state_parameters("low_risk_adenoma")
        assert updated_params.distribution_type == DistributionType.LOGNORMAL
        assert updated_params.mean == 10.0
        assert updated_params.std == 4.0
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        model = DwellTimeModel()
        model.set_random_seed(42)
        
        # 生成一些样本
        for _ in range(10):
            model.sample_dwell_time("low_risk_adenoma")
        
        stats = model.get_statistics("low_risk_adenoma")
        assert stats is not None
        assert stats.sample_count == 10
        assert stats.mean_time > 0
        assert len(stats.samples) == 10
    
    def test_reset_statistics(self):
        """测试重置统计信息"""
        model = DwellTimeModel()
        
        # 生成一些样本
        for _ in range(5):
            model.sample_dwell_time("low_risk_adenoma")
        
        # 重置特定状态的统计
        model.reset_statistics("low_risk_adenoma")
        stats = model.get_statistics("low_risk_adenoma")
        assert stats.sample_count == 0
        assert len(stats.samples) == 0
        
        # 重置所有统计
        for _ in range(3):
            model.sample_dwell_time("high_risk_adenoma")
        
        model.reset_statistics()
        all_stats = model.get_all_statistics()
        for state_stats in all_stats.values():
            assert state_stats["sample_count"] == 0

    def test_validate_parameters(self):
        """测试参数验证"""
        model = DwellTimeModel()

        # 默认参数应该都是有效的
        validation_results = model.validate_parameters()
        assert len(validation_results) == 0

        # 添加无效参数
        invalid_params = DwellTimeParameters(
            distribution_type=DistributionType.WEIBULL,
            mean=5.0,
            std=2.0,
            shape_param=None  # Weibull需要形状参数
        )

        model.update_state_parameters("invalid_state", invalid_params)
        validation_results = model.validate_parameters()
        assert "invalid_state" in validation_results
        assert any("Weibull分布需要正的形状参数" in error for error in validation_results["invalid_state"])

    def test_export_configuration(self):
        """测试配置导出"""
        model = DwellTimeModel()

        config = model.export_configuration()

        # 检查导出的配置包含所有状态
        expected_states = [
            "normal", "low_risk_adenoma", "high_risk_adenoma",
            "preclinical_cancer", "clinical_cancer_stage_i",
            "clinical_cancer_stage_ii", "clinical_cancer_stage_iii",
            "clinical_cancer_stage_iv"
        ]

        for state in expected_states:
            assert state in config
            assert "distribution_type" in config[state]
            assert "mean" in config[state]
            assert "std" in config[state]

    def test_calculate_expected_dwell_time(self):
        """测试期望停留时间计算"""
        model = DwellTimeModel()

        # 正态分布的期望值应该等于均值
        expected_time = model.calculate_expected_dwell_time("low_risk_adenoma")
        assert expected_time == 5.0

        # 测试不存在的状态
        with pytest.raises(ValueError, match="未知的疾病状态"):
            model.calculate_expected_dwell_time("nonexistent")

    def test_get_supported_states(self):
        """测试获取支持的状态列表"""
        model = DwellTimeModel()

        states = model.get_supported_states()
        expected_states = [
            "normal", "low_risk_adenoma", "high_risk_adenoma",
            "preclinical_cancer", "clinical_cancer_stage_i",
            "clinical_cancer_stage_ii", "clinical_cancer_stage_iii",
            "clinical_cancer_stage_iv"
        ]

        for state in expected_states:
            assert state in states

    def test_different_distributions(self):
        """测试不同分布类型的抽样"""
        model = DwellTimeModel()
        model.set_random_seed(42)

        # 测试对数正态分布
        lognormal_params = DwellTimeParameters(
            distribution_type=DistributionType.LOGNORMAL,
            mean=5.0,
            std=2.0,
            min_time=0.1,
            max_time=20.0
        )
        model.update_state_parameters("test_lognormal", lognormal_params)

        samples = [model.sample_dwell_time("test_lognormal") for _ in range(100)]
        assert all(0.1 <= s <= 20.0 for s in samples)
        assert len(samples) == 100

        # 测试指数分布
        exp_params = DwellTimeParameters(
            distribution_type=DistributionType.EXPONENTIAL,
            mean=3.0,
            std=3.0,  # 指数分布的标准差等于均值
            min_time=0.0,
            max_time=15.0
        )
        model.update_state_parameters("test_exponential", exp_params)

        samples = [model.sample_dwell_time("test_exponential") for _ in range(100)]
        assert all(0.0 <= s <= 15.0 for s in samples)
        assert len(samples) == 100

    def test_boundary_conditions(self):
        """测试边界条件"""
        model = DwellTimeModel()
        model.set_random_seed(42)

        # 测试极小的时间范围
        small_range_params = DwellTimeParameters(
            distribution_type=DistributionType.NORMAL,
            mean=2.0,
            std=0.5,
            min_time=1.9,
            max_time=2.1
        )
        model.update_state_parameters("small_range", small_range_params)

        samples = [model.sample_dwell_time("small_range") for _ in range(50)]
        assert all(1.9 <= s <= 2.1 for s in samples)

    def test_statistical_properties(self):
        """测试统计特性"""
        model = DwellTimeModel()
        model.set_random_seed(42)

        # 生成大量样本以测试统计特性
        samples = [model.sample_dwell_time("low_risk_adenoma") for _ in range(1000)]

        # 检查样本均值和标准差是否接近理论值
        sample_mean = np.mean(samples)
        sample_std = np.std(samples, ddof=1)

        # 允许一定的统计误差
        assert abs(sample_mean - 5.0) < 0.5
        assert abs(sample_std - 2.0) < 0.5

        # 检查样本是否在指定范围内
        assert all(1.0 <= s <= 15.0 for s in samples)

    def test_configuration_loading_edge_cases(self):
        """测试配置加载的边界情况"""
        # 测试空配置
        model = DwellTimeModel(config={})
        assert len(model.state_parameters) > 0  # 应该有默认参数

        # 测试无效配置格式
        invalid_config = {
            "test_state": "invalid_format"  # 应该是字典
        }
        model = DwellTimeModel(config=invalid_config)
        assert "test_state" not in model.state_parameters

        # 测试缺少必需参数的配置
        incomplete_config = {
            "test_state": {
                "distribution_type": "normal",
                "mean": 5.0
                # 缺少 std 参数
            }
        }
        model = DwellTimeModel(config=incomplete_config)
        assert "test_state" not in model.state_parameters
