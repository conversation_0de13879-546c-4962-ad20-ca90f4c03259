"""
参数管理器单元测试

测试参数定义、约束验证、版本控制和导入/导出功能。
"""

import pytest
import tempfile
import json
import yaml
import pandas as pd
from pathlib import Path
from datetime import datetime

from src.modules.config.parameter_manager import (
    ParameterManager,
    ParameterDefinition,
    ParameterConstraint,
    ParameterVersion
)


class TestParameterConstraint:
    """测试参数约束"""

    def test_constraint_creation(self):
        """测试约束创建"""
        constraint = ParameterConstraint(
            min_value=0.0,
            max_value=1.0,
            data_type="float",
            description="概率值"
        )
        
        assert constraint.min_value == 0.0
        assert constraint.max_value == 1.0
        assert constraint.data_type == "float"
        assert constraint.description == "概率值"

    def test_constraint_with_allowed_values(self):
        """测试允许值约束"""
        constraint = ParameterConstraint(
            allowed_values=["male", "female"],
            data_type="str",
            description="性别"
        )
        
        assert constraint.allowed_values == ["male", "female"]
        assert constraint.data_type == "str"


class TestParameterDefinition:
    """测试参数定义"""

    def test_parameter_definition_creation(self):
        """测试参数定义创建"""
        constraint = ParameterConstraint(min_value=0.0, max_value=1.0, data_type="float")
        
        param = ParameterDefinition(
            name="test_param",
            value=0.5,
            trainable=True,
            constraint=constraint,
            category="test",
            description="测试参数",
            unit="ratio"
        )
        
        assert param.name == "test_param"
        assert param.value == 0.5
        assert param.trainable is True
        assert param.constraint == constraint
        assert param.category == "test"
        assert param.description == "测试参数"
        assert param.unit == "ratio"

    def test_parameter_validation_success(self):
        """测试参数验证成功"""
        constraint = ParameterConstraint(min_value=0.0, max_value=1.0, data_type="float")
        
        # 有效值应该通过验证
        param = ParameterDefinition(
            name="valid_param",
            value=0.5,
            constraint=constraint
        )
        
        assert param.value == 0.5

    def test_parameter_validation_failure(self):
        """测试参数验证失败"""
        constraint = ParameterConstraint(min_value=0.0, max_value=1.0, data_type="float")
        
        # 超出范围的值应该失败
        with pytest.raises(ValueError):
            ParameterDefinition(
                name="invalid_param",
                value=1.5,  # 超出最大值
                constraint=constraint
            )

    def test_parameter_update_value(self):
        """测试参数值更新"""
        constraint = ParameterConstraint(min_value=0.0, max_value=1.0, data_type="float")
        
        param = ParameterDefinition(
            name="update_param",
            value=0.5,
            constraint=constraint
        )
        
        # 有效更新
        param.update_value(0.8)
        assert param.value == 0.8
        
        # 无效更新应该失败
        with pytest.raises(ValueError):
            param.update_value(1.5)

    def test_parameter_type_validation(self):
        """测试参数类型验证"""
        # 整数类型约束
        int_constraint = ParameterConstraint(min_value=1, max_value=10, data_type="int")
        
        param = ParameterDefinition(
            name="int_param",
            value=5,
            constraint=int_constraint
        )
        
        assert param.value == 5
        
        # 布尔类型约束
        bool_constraint = ParameterConstraint(data_type="bool")
        
        param_bool = ParameterDefinition(
            name="bool_param",
            value=True,
            constraint=bool_constraint
        )
        
        assert param_bool.value is True

    def test_parameter_allowed_values_validation(self):
        """测试允许值验证"""
        constraint = ParameterConstraint(
            allowed_values=["option1", "option2", "option3"],
            data_type="str"
        )
        
        # 有效值
        param = ParameterDefinition(
            name="choice_param",
            value="option1",
            constraint=constraint
        )
        
        assert param.value == "option1"
        
        # 无效值应该失败
        with pytest.raises(ValueError):
            ParameterDefinition(
                name="invalid_choice_param",
                value="invalid_option",
                constraint=constraint
            )


class TestParameterManager:
    """测试参数管理器"""

    def test_parameter_manager_initialization(self):
        """测试参数管理器初始化"""
        manager = ParameterManager()
        
        # 验证默认参数已加载
        assert len(manager.parameters) > 0
        assert "adenoma_size_threshold" in manager.parameters
        assert "adenoma_initiation_inflection_point" in manager.parameters
        
        # 验证版本历史
        assert len(manager.parameter_history) == 1
        assert manager.current_version == "initial"

    def test_add_parameter(self):
        """测试添加参数"""
        manager = ParameterManager()
        
        constraint = ParameterConstraint(min_value=0.0, max_value=1.0, data_type="float")
        
        manager.add_parameter(
            name="new_param",
            value=0.5,
            trainable=True,
            constraint=constraint,
            category="test",
            description="新参数",
            unit="ratio"
        )
        
        assert "new_param" in manager.parameters
        assert manager.get_parameter("new_param") == 0.5

    def test_get_parameter(self):
        """测试获取参数"""
        manager = ParameterManager()
        
        # 获取存在的参数
        value = manager.get_parameter("adenoma_size_threshold")
        assert value == 10.0
        
        # 获取不存在的参数应该失败
        with pytest.raises(KeyError):
            manager.get_parameter("nonexistent_param")

    def test_set_parameter(self):
        """测试设置参数"""
        manager = ParameterManager()
        
        # 设置存在的参数
        manager.set_parameter("villous_risk_multiplier", 1.8)
        assert manager.get_parameter("villous_risk_multiplier") == 1.8
        
        # 设置不存在的参数应该失败
        with pytest.raises(KeyError):
            manager.set_parameter("nonexistent_param", 1.0)

    def test_get_parameters_by_category(self):
        """测试按类别获取参数"""
        manager = ParameterManager()
        
        adenoma_params = manager.get_parameters_by_category("adenoma_classification")
        
        assert "adenoma_size_threshold" in adenoma_params
        assert "villous_risk_multiplier" in adenoma_params
        assert "high_grade_dysplasia_multiplier" in adenoma_params

    def test_get_trainable_parameters(self):
        """测试获取可训练参数"""
        manager = ParameterManager()
        
        trainable_params = manager.get_trainable_parameters()
        
        # 可训练参数应该包含某些参数
        assert "villous_risk_multiplier" in trainable_params
        assert "adenoma_initiation_inflection_point" in trainable_params
        
        # 不可训练参数不应该包含
        assert "adenoma_size_threshold" not in trainable_params

    def test_update_parameters(self):
        """测试批量更新参数"""
        manager = ParameterManager()
        
        updates = {
            "villous_risk_multiplier": 1.7,
            "high_grade_dysplasia_multiplier": 2.2
        }
        
        manager.update_parameters(updates)
        
        assert manager.get_parameter("villous_risk_multiplier") == 1.7
        assert manager.get_parameter("high_grade_dysplasia_multiplier") == 2.2

    def test_validate_all_parameters(self):
        """测试验证所有参数"""
        manager = ParameterManager()
        
        # 默认参数应该都有效
        assert manager.validate_all_parameters() is True
        
        # 设置无效参数
        manager.parameters["villous_risk_multiplier"].value = 5.0  # 超出约束范围
        
        # 验证应该失败
        assert manager.validate_all_parameters() is False

    def test_version_management(self):
        """测试版本管理"""
        manager = ParameterManager()
        
        # 修改一些参数
        manager.set_parameter("villous_risk_multiplier", 1.8)
        
        # 保存新版本
        manager.save_version("v1.0", "第一个版本")
        
        assert manager.current_version == "v1.0"
        assert len(manager.parameter_history) == 2
        
        # 继续修改参数
        manager.set_parameter("villous_risk_multiplier", 1.9)
        
        # 加载之前的版本
        manager.load_version("v1.0")
        
        assert manager.get_parameter("villous_risk_multiplier") == 1.8
        assert manager.current_version == "v1.0"

    def test_version_history(self):
        """测试版本历史"""
        manager = ParameterManager()
        
        # 创建几个版本
        manager.save_version("v1.0", "版本1")
        manager.save_version("v2.0", "版本2")
        
        history = manager.get_version_history()
        
        assert len(history) == 3  # initial + v1.0 + v2.0
        assert history[-1]["version_id"] == "v2.0"
        assert history[-1]["description"] == "版本2"
        assert "timestamp" in history[-1]
        assert "hash_value" in history[-1]

    def test_parameter_export_yaml(self):
        """测试YAML格式导出"""
        manager = ParameterManager()
        
        yaml_content = manager.export_parameters("yaml")
        
        # 验证YAML格式
        data = yaml.safe_load(yaml_content)
        
        assert "metadata" in data
        assert "parameters" in data
        assert "adenoma_classification" in data["parameters"]
        assert "adenoma_initiation" in data["parameters"]

    def test_parameter_export_json(self):
        """测试JSON格式导出"""
        manager = ParameterManager()

        json_content = manager.export_parameters("json")

        # 验证JSON格式
        data = json.loads(json_content)

        assert "metadata" in data
        assert "parameters" in data
        assert "adenoma_classification" in data["parameters"]

    def test_parameter_export_excel(self):
        """测试Excel格式导出"""
        manager = ParameterManager()

        df = manager.export_parameters("excel")

        # 验证DataFrame格式
        assert isinstance(df, pd.DataFrame)
        assert len(df) > 0

        # 验证列名
        expected_columns = [
            "parameter_name", "category", "value", "trainable",
            "description", "unit", "data_type", "min_value",
            "max_value", "allowed_values", "constraint_description"
        ]
        for col in expected_columns:
            assert col in df.columns

        # 验证第一行是元数据行
        assert str(df.iloc[0]['parameter_name']).startswith('#')

    def test_parameter_export_csv(self):
        """测试CSV格式导出"""
        manager = ParameterManager()

        df = manager.export_parameters("csv")

        # 验证DataFrame格式
        assert isinstance(df, pd.DataFrame)
        assert len(df) > 0

        # 验证包含参数数据
        param_rows = df[~df['parameter_name'].astype(str).str.startswith('#')]
        assert len(param_rows) > 0

    def test_save_and_load_file(self):
        """测试文件保存和加载"""
        manager = ParameterManager()
        
        # 修改一些参数
        manager.set_parameter("villous_risk_multiplier", 1.8)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_file = f.name
        
        try:
            # 保存到文件
            manager.save_to_file(temp_file, "yaml")
            
            # 创建新的管理器并加载
            new_manager = ParameterManager()
            new_manager.load_from_file(temp_file)
            
            # 验证参数已正确加载
            assert new_manager.get_parameter("villous_risk_multiplier") == 1.8
            
        finally:
            # 清理临时文件
            Path(temp_file).unlink(missing_ok=True)

    def test_save_and_load_excel_file(self):
        """测试Excel文件保存和加载"""
        manager = ParameterManager()

        # 修改一些参数
        manager.set_parameter("villous_risk_multiplier", 1.8)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False) as f:
            temp_file = f.name

        try:
            # 保存到Excel文件
            manager.save_to_file(temp_file, "excel")

            # 创建新的管理器并加载
            new_manager = ParameterManager()
            new_manager.load_from_file(temp_file)

            # 验证参数已正确加载
            assert new_manager.get_parameter("villous_risk_multiplier") == 1.8

        finally:
            # 清理临时文件
            Path(temp_file).unlink(missing_ok=True)

    def test_save_and_load_csv_file(self):
        """测试CSV文件保存和加载"""
        manager = ParameterManager()

        # 修改一些参数
        manager.set_parameter("villous_risk_multiplier", 1.9)
        manager.set_parameter("high_grade_dysplasia_multiplier", 2.1)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            temp_file = f.name

        try:
            # 保存到CSV文件
            manager.save_to_file(temp_file, "csv")

            # 创建新的管理器并加载
            new_manager = ParameterManager()
            new_manager.load_from_file(temp_file)

            # 验证参数已正确加载
            assert new_manager.get_parameter("villous_risk_multiplier") == 1.9
            assert new_manager.get_parameter("high_grade_dysplasia_multiplier") == 2.1

        finally:
            # 清理临时文件
            Path(temp_file).unlink(missing_ok=True)

    def test_parameter_summary(self):
        """测试参数摘要"""
        manager = ParameterManager()
        
        summary = manager.get_parameter_summary()
        
        assert "total_parameters" in summary
        assert "trainable_parameters" in summary
        assert "categories" in summary
        assert "current_version" in summary
        assert "version_count" in summary
        
        assert summary["total_parameters"] > 0
        assert summary["trainable_parameters"] > 0
        assert len(summary["categories"]) > 0

    def test_invalid_export_format(self):
        """测试无效导出格式"""
        manager = ParameterManager()

        with pytest.raises(ValueError):
            manager.export_parameters("xml")  # 不支持的格式

    def test_dataframe_export_structure(self):
        """测试DataFrame导出结构"""
        manager = ParameterManager()

        df = manager._export_to_dataframe()

        # 验证DataFrame结构
        assert isinstance(df, pd.DataFrame)
        assert len(df) > 1  # 至少有元数据行和一些参数行

        # 验证元数据行
        metadata_row = df.iloc[0]
        assert str(metadata_row['parameter_name']).startswith('#')

        # 验证参数行
        param_rows = df.iloc[1:]
        assert len(param_rows) > 0

        # 验证所有必需列都存在
        required_columns = ['parameter_name', 'category', 'value', 'trainable']
        for col in required_columns:
            assert col in df.columns
            # 参数行的这些列不应该为空
            assert not param_rows[col].isna().all()

    def test_load_nonexistent_file(self):
        """测试加载不存在的文件"""
        manager = ParameterManager()
        
        with pytest.raises(FileNotFoundError):
            manager.load_from_file("nonexistent_file.yaml")

    def test_load_invalid_version(self):
        """测试加载不存在的版本"""
        manager = ParameterManager()
        
        with pytest.raises(ValueError):
            manager.load_version("nonexistent_version")


class TestParameterVersion:
    """测试参数版本"""

    def test_parameter_version_creation(self):
        """测试参数版本创建"""
        params = {"param1": 1.0, "param2": 2.0}
        
        version = ParameterVersion(
            version_id="test_v1",
            timestamp=datetime.now(),
            parameters=params,
            description="测试版本"
        )
        
        assert version.version_id == "test_v1"
        assert version.parameters == params
        assert version.description == "测试版本"
        assert len(version.hash_value) == 32  # MD5哈希长度

    def test_parameter_version_hash(self):
        """测试参数版本哈希"""
        params1 = {"param1": 1.0, "param2": 2.0}
        params2 = {"param1": 1.0, "param2": 2.0}
        params3 = {"param1": 1.0, "param2": 3.0}
        
        version1 = ParameterVersion("v1", datetime.now(), params1)
        version2 = ParameterVersion("v2", datetime.now(), params2)
        version3 = ParameterVersion("v3", datetime.now(), params3)
        
        # 相同参数应该有相同哈希
        assert version1.hash_value == version2.hash_value
        
        # 不同参数应该有不同哈希
        assert version1.hash_value != version3.hash_value


class TestIntegrationScenarios:
    """测试集成场景"""

    def test_complete_parameter_workflow(self):
        """测试完整的参数工作流"""
        # 创建参数管理器
        manager = ParameterManager()
        
        # 添加自定义参数
        constraint = ParameterConstraint(min_value=0.0, max_value=1.0, data_type="float")
        manager.add_parameter(
            "custom_param",
            0.5,
            trainable=True,
            constraint=constraint,
            category="custom",
            description="自定义参数"
        )
        
        # 修改参数
        manager.set_parameter("custom_param", 0.8)
        manager.set_parameter("villous_risk_multiplier", 1.7)
        
        # 保存版本
        manager.save_version("workflow_v1", "工作流测试版本")
        
        # 导出配置
        yaml_config = manager.export_parameters("yaml")
        json_config = manager.export_parameters("json")
        
        # 验证导出内容
        yaml_data = yaml.safe_load(yaml_config)
        json_data = json.loads(json_config)
        
        assert yaml_data["parameters"]["custom"]["custom_param"]["value"] == 0.8
        assert json_data["parameters"]["custom"]["custom_param"]["value"] == 0.8
        
        # 获取摘要
        summary = manager.get_parameter_summary()
        assert "custom" in summary["categories"]
        
        # 验证版本历史
        history = manager.get_version_history()
        assert len(history) >= 2
        assert any(v["version_id"] == "workflow_v1" for v in history)

    def test_parameter_constraint_edge_cases(self):
        """测试参数约束边界情况"""
        manager = ParameterManager()
        
        # 测试边界值
        constraint = ParameterConstraint(min_value=0.0, max_value=1.0, data_type="float")
        
        manager.add_parameter("edge_param", 0.0, constraint=constraint)  # 最小值
        assert manager.get_parameter("edge_param") == 0.0
        
        manager.set_parameter("edge_param", 1.0)  # 最大值
        assert manager.get_parameter("edge_param") == 1.0
        
        # 测试刚好超出边界的值
        with pytest.raises(ValueError):
            manager.set_parameter("edge_param", -0.001)  # 略小于最小值
        
        with pytest.raises(ValueError):
            manager.set_parameter("edge_param", 1.001)  # 略大于最大值


if __name__ == "__main__":
    pytest.main([__file__])
