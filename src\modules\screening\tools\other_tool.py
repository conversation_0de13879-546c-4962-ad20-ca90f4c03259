"""
其他筛查工具实现

通用的筛查工具实现，可以配置为任何未明确定义的筛查方法。
"""

import logging
import random
from typing import Dict, Optional, Any

from src.core.enums import DiseaseState, AnatomicalLocation, Gender
from src.core.individual import Individual
from ..enums import ScreeningToolType, ScreeningResult, InvasivenessLevel, OperatorSkillLevel
from ..screening_tool import ScreeningTool, ScreeningPerformance, ScreeningCharacteristics

logger = logging.getLogger(__name__)


class OtherTool(ScreeningTool):
    """其他筛查工具（通用实现）"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化其他筛查工具

        Args:
            config: 工具配置字典
        """
        if config is None:
            config = {}

        # 默认其他工具性能参数
        default_performance = ScreeningPerformance(
            sensitivity_by_state={
                DiseaseState.NORMAL: 0.0,
                DiseaseState.LOW_RISK_ADENOMA: 0.20,
                DiseaseState.HIGH_RISK_ADENOMA: 0.40,
                DiseaseState.PRECLINICAL_CANCER: 0.70,
                DiseaseState.CLINICAL_CANCER_STAGE_I: 0.80,
                DiseaseState.CLINICAL_CANCER_STAGE_II: 0.85,
                DiseaseState.CLINICAL_CANCER_STAGE_III: 0.90,
                DiseaseState.CLINICAL_CANCER_STAGE_IV: 0.95
            },
            specificity=0.85,
            location_sensitivity_modifiers={
                AnatomicalLocation.PROXIMAL_COLON: 0.9,
                AnatomicalLocation.DISTAL_COLON: 1.0,
                AnatomicalLocation.RECTUM: 1.0
            }
        )

        # 默认其他工具特性
        default_characteristics = ScreeningCharacteristics(
            invasiveness=InvasivenessLevel.MINIMALLY_INVASIVE,
            operator_skill_required=OperatorSkillLevel.MEDIUM,
            preparation_required=False,
            turnaround_time_days=1,
            can_detect_proximal=True,
            can_detect_distal=True,
            can_detect_rectal=True
        )

        super().__init__(
            tool_type=ScreeningToolType.OTHER,
            performance=default_performance,
            characteristics=default_characteristics,
            config=config
        )
        
        # 获取工具特定配置
        self.tool_name = config.get("tool_name", "未知筛查工具")
        self.tool_description = config.get("tool_description", "通用筛查工具实现")
        
        # 检测机制配置
        self.detection_mechanism = config.get("detection_mechanism", {
            "type": "probability_based",  # 检测机制类型
            "base_accuracy": 0.8,         # 基础准确性
            "variability": 0.1            # 变异性
        })
        
        # 特殊配置参数
        self.special_parameters = config.get("special_parameters", {})
        
        logger.info(f"其他筛查工具初始化完成: {self.tool_name}")
    
    def perform_screening(
        self, 
        individual: Individual, 
        **kwargs
    ) -> ScreeningResult:
        """
        执行其他筛查工具筛查
        
        Args:
            individual: 被筛查个体
            **kwargs: 额外参数
            
        Returns:
            ScreeningResult: 筛查结果
        """
        try:
            # 获取个体当前疾病状态
            current_state = individual.current_disease_state
            
            # 计算检测概率
            detection_probability = self.calculate_detection_probability(individual)
            
            # 应用工具特定的调整
            adjusted_detection_probability = self._apply_tool_specific_adjustments(
                detection_probability, individual, **kwargs
            )
            
            # 计算特异性
            specificity = self.calculate_specificity(individual)
            
            # 模拟筛查结果
            if current_state == DiseaseState.NORMAL:
                # 正常状态：基于特异性判断是否假阳性
                if random.random() > specificity:
                    result = ScreeningResult.POSITIVE  # 假阳性
                else:
                    result = ScreeningResult.NEGATIVE  # 真阴性
            else:
                # 疾病状态：基于敏感性判断是否检出
                if random.random() < adjusted_detection_probability:
                    result = ScreeningResult.POSITIVE  # 真阳性
                else:
                    result = ScreeningResult.NEGATIVE  # 假阴性
            
            # 记录筛查日志
            logger.debug(
                f"{self.tool_name}筛查结果 - 个体ID: {getattr(individual, 'individual_id', 'unknown')}, "
                f"状态: {current_state}, 结果: {result}, "
                f"检测概率: {adjusted_detection_probability:.3f}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"{self.tool_name}筛查失败: {e}")
            return ScreeningResult.NEGATIVE
    
    def _apply_tool_specific_adjustments(
        self, 
        base_probability: float, 
        individual: Individual, 
        **kwargs
    ) -> float:
        """
        应用工具特定的调整因子
        
        Args:
            base_probability: 基础检测概率
            individual: 个体对象
            **kwargs: 额外参数
            
        Returns:
            float: 调整后的检测概率
        """
        adjusted_probability = base_probability
        
        # 应用检测机制调整
        mechanism = self.detection_mechanism
        if mechanism.get("type") == "probability_based":
            # 基于概率的调整
            accuracy = mechanism.get("base_accuracy", 0.8)
            variability = mechanism.get("variability", 0.1)
            
            # 添加随机变异性
            random_factor = 1.0 + random.uniform(-variability, variability)
            adjusted_probability = base_probability * accuracy * random_factor
        
        elif mechanism.get("type") == "threshold_based":
            # 基于阈值的调整
            threshold = mechanism.get("threshold", 0.5)
            if base_probability >= threshold:
                adjusted_probability = mechanism.get("above_threshold_probability", 0.9)
            else:
                adjusted_probability = mechanism.get("below_threshold_probability", 0.1)
        
        # 应用特殊参数调整
        for param_name, param_value in self.special_parameters.items():
            if param_name == "age_factor" and hasattr(individual, 'get_current_age'):
                age = individual.get_current_age()
                age_adjustment = param_value.get("slope", 0.0) * (age - param_value.get("baseline", 50.0))
                adjusted_probability *= (1.0 + age_adjustment)
            
            elif param_name == "gender_factor" and hasattr(individual, 'gender'):
                gender_multiplier = param_value.get(individual.gender.value.lower(), 1.0)
                adjusted_probability *= gender_multiplier
        
        # 确保概率在有效范围内
        return max(0.0, min(1.0, adjusted_probability))
    
    def calculate_detection_probability(self, individual: Individual) -> float:
        """
        计算检测概率
        
        Args:
            individual: 个体对象
            
        Returns:
            float: 检测概率
        """
        # 获取基础敏感性
        current_state = individual.current_disease_state
        base_sensitivity = self.performance.sensitivity_by_state.get(current_state, 0.0)
        
        # 应用位置调整
        if hasattr(individual, 'anatomical_location'):
            location_factor = self._calculate_location_factor(individual.anatomical_location)
        else:
            location_factor = 1.0
        
        # 计算最终检测概率
        final_probability = base_sensitivity * location_factor
        
        return max(0.0, min(1.0, final_probability))
    
    def calculate_specificity(self, individual: Individual) -> float:
        """
        计算特异性
        
        Args:
            individual: 个体对象
            
        Returns:
            float: 特异性值
        """
        base_specificity = self.performance.specificity
        
        # 可以根据个体特征进行调整
        # 这里保持简单实现
        return base_specificity
    
    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具详细信息"""
        base_info = super().get_tool_info()
        
        # 添加其他工具特有信息
        base_info.update({
            "tool_name": self.tool_name,
            "tool_description": self.tool_description,
            "detection_mechanism": self.detection_mechanism,
            "special_parameters": self.special_parameters,
            "is_generic_implementation": True
        })
        
        return base_info
    
    def validate_configuration(self) -> bool:
        """
        验证工具配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 验证基础配置
            if not super()._validate_configuration():
                return False
            
            # 验证检测机制配置
            mechanism = self.detection_mechanism
            if mechanism.get("type") not in ["probability_based", "threshold_based", "custom"]:
                logger.warning(f"未知的检测机制类型: {mechanism.get('type')}")
                return False
            
            # 验证特殊参数
            for param_name, param_value in self.special_parameters.items():
                if not isinstance(param_value, (dict, float, int, str, bool)):
                    logger.warning(f"特殊参数 {param_name} 的值类型无效")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
