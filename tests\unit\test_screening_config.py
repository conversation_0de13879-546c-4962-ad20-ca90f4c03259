"""
筛查工具配置管理测试

测试配置文件的加载、保存、验证和模板生成功能。
"""

import pytest
import tempfile
import yaml
import json
from pathlib import Path
from unittest.mock import patch, mock_open

from src.modules.screening import (
    ScreeningToolType, ScreeningToolConfigManager, ScreeningToolConfigTemplates,
    ScreeningToolFactory
)
from src.utils.validators import ScreeningToolValidationError


class TestScreeningToolConfigTemplates:
    """测试配置模板生成"""
    
    def test_create_base_template(self):
        """测试基础模板创建"""
        template = ScreeningToolConfigTemplates.create_base_template(
            ScreeningToolType.FIT,
            "测试FIT工具",
            "测试描述",
            "1.0"
        )
        
        assert "screening_tool" in template
        assert "metadata" in template
        
        tool_config = template["screening_tool"]
        assert tool_config["name"] == "测试FIT工具"
        assert tool_config["type"] == "FIT"
        assert tool_config["version"] == "1.0"
        assert tool_config["description"] == "测试描述"
        
        # 检查必需的配置部分
        assert "performance" in tool_config
        assert "costs" in tool_config
        assert "characteristics" in tool_config
    
    def test_create_fit_template(self):
        """测试FIT模板创建"""
        template = ScreeningToolConfigTemplates.create_fit_template()
        
        tool_config = template["screening_tool"]
        assert tool_config["type"] == "FIT"
        assert tool_config["name"] == "粪便免疫化学检测 (FIT)"
        
        # 检查FIT特异性配置
        performance = tool_config["performance"]
        assert "detection_threshold" in performance
        assert performance["detection_threshold"] == 100.0
        assert performance["specificity"] == 0.95
        
        # 检查敏感性配置
        sensitivity = performance["sensitivity_by_state"]
        assert "normal" in sensitivity
        assert "clinical_cancer_stage_i" in sensitivity
        assert sensitivity["normal"] == 0.0
        assert sensitivity["clinical_cancer_stage_i"] == 0.85
        
        # 检查成本配置
        costs = tool_config["costs"]
        assert "direct_cost" in costs
        assert "material_cost" in costs
        assert costs["direct_cost"] == 25.0
    
    def test_create_colonoscopy_template(self):
        """测试结肠镜模板创建"""
        template = ScreeningToolConfigTemplates.create_colonoscopy_template()
        
        tool_config = template["screening_tool"]
        assert tool_config["type"] == "COLONOSCOPY"
        assert tool_config["name"] == "结肠镜检查"
        
        # 检查结肠镜特异性配置
        performance = tool_config["performance"]
        assert performance["specificity"] == 0.99
        assert performance["operator_dependency"] == 2.0
        
        # 检查特性配置
        characteristics = tool_config["characteristics"]
        assert characteristics["invasiveness"] == "invasive"
        assert characteristics["requires_sedation"] is True
        assert characteristics["preparation_required"] is True
        
        # 检查成本配置
        costs = tool_config["costs"]
        assert "anesthesia_cost" in costs
        assert "equipment_cost" in costs
    
    def test_create_sigmoidoscopy_template(self):
        """测试乙状结肠镜模板创建"""
        template = ScreeningToolConfigTemplates.create_sigmoidoscopy_template()
        
        tool_config = template["screening_tool"]
        assert tool_config["type"] == "SIGMOIDOSCOPY"
        
        # 检查位置检测能力
        characteristics = tool_config["characteristics"]
        assert characteristics["can_detect_proximal"] is False
        assert characteristics["can_detect_distal"] is True
        assert characteristics["can_detect_rectal"] is True
        
        # 检查位置敏感性调整
        location_modifiers = tool_config["performance"]["location_sensitivity_modifiers"]
        assert location_modifiers["proximal_colon"] == 0.0
        assert location_modifiers["distal_colon"] == 1.0
    
    def test_create_custom_template(self):
        """测试自定义模板创建"""
        custom_params = {
            "screening_tool": {
                "name": "自定义FIT工具",
                "description": "自定义描述",
                "performance": {
                    "specificity": 0.97
                },
                "costs": {
                    "direct_cost": 30.0,
                    "indirect_cost": 20.0  # 添加足够的成本以通过验证
                }
            }
        }

        template = ScreeningToolConfigTemplates.create_custom_template(
            ScreeningToolType.FIT,
            custom_params
        )

        tool_config = template["screening_tool"]
        assert tool_config["name"] == "自定义FIT工具"
        assert tool_config["description"] == "自定义描述"
        assert tool_config["performance"]["specificity"] == 0.97
        assert tool_config["costs"]["direct_cost"] == 30.0
    
    def test_get_available_templates(self):
        """测试获取可用模板列表"""
        templates = ScreeningToolConfigTemplates.get_available_templates()
        
        assert isinstance(templates, dict)
        assert "fit" in templates
        assert "colonoscopy" in templates
        assert "sigmoidoscopy" in templates
        assert "base" in templates


class TestScreeningToolConfigManager:
    """测试配置管理器"""
    
    @pytest.fixture
    def temp_config_dir(self):
        """创建临时配置目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def config_manager(self, temp_config_dir):
        """创建配置管理器实例"""
        return ScreeningToolConfigManager(temp_config_dir)
    
    @pytest.fixture
    def sample_config(self):
        """创建样本配置"""
        return {
            "screening_tool": {
                "name": "测试FIT工具",
                "type": "FIT",
                "version": "1.0",
                "performance": {
                    "specificity": 0.95,
                    "sensitivity_by_state": {
                        "normal": 0.0,
                        "clinical_cancer_stage_i": 0.85
                    }
                },
                "costs": {
                    "direct_cost": 25.0,
                    "indirect_cost": 50.0
                },
                "characteristics": {
                    "invasiveness": "non_invasive",
                    "preparation_required": False
                }
            },
            "metadata": {
                "created_date": "2025-08-03",
                "version": "1.0"
            }
        }
    
    def test_config_manager_initialization(self, temp_config_dir):
        """测试配置管理器初始化"""
        manager = ScreeningToolConfigManager(temp_config_dir)
        assert manager.config_directory == Path(temp_config_dir)
        assert manager.config_directory.exists()
    
    def test_save_and_load_configuration(self, config_manager, sample_config):
        """测试配置保存和加载"""
        # 保存配置
        saved_path = config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            sample_config,
            "test_fit_config.yaml"
        )
        
        assert Path(saved_path).exists()
        
        # 加载配置
        loaded_config = config_manager.load_tool_configuration(
            ScreeningToolType.FIT,
            "test_fit_config.yaml"
        )
        
        assert loaded_config["screening_tool"]["name"] == "测试FIT工具"
        assert loaded_config["screening_tool"]["type"] == "FIT"
    
    def test_save_configuration_formats(self, config_manager, sample_config):
        """测试不同格式的配置保存"""
        # 保存为YAML
        yaml_path = config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            sample_config,
            "test_config.yaml",
            format_type="yaml"
        )
        assert Path(yaml_path).suffix == ".yaml"
        
        # 保存为JSON
        json_path = config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            sample_config,
            "test_config.json",
            format_type="json"
        )
        assert Path(json_path).suffix == ".json"
    
    def test_configuration_validation(self, config_manager):
        """测试配置验证"""
        # 有效配置
        valid_config = {
            "screening_tool": {
                "name": "有效配置",
                "type": "FIT",
                "performance": {
                    "specificity": 0.95,
                    "sensitivity_by_state": {
                        "normal": 0.0,
                        "clinical_cancer_stage_i": 0.85
                    }
                },
                "costs": {
                    "direct_cost": 25.0
                }
            }
        }
        
        # 应该成功保存
        config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            valid_config,
            "valid_config.yaml"
        )
        
        # 无效配置 - 缺少screening_tool部分
        invalid_config = {
            "metadata": {
                "version": "1.0"
            }
        }
        
        with pytest.raises(ScreeningToolValidationError):
            config_manager.save_tool_configuration(
                ScreeningToolType.FIT,
                invalid_config,
                "invalid_config.yaml"
            )
    
    def test_load_nonexistent_file(self, config_manager):
        """测试加载不存在的文件"""
        with pytest.raises(FileNotFoundError):
            config_manager.load_tool_configuration(
                ScreeningToolType.FIT,
                "nonexistent_config.yaml"
            )
    
    def test_list_available_configurations(self, config_manager, sample_config):
        """测试列出可用配置"""
        # 保存几个配置文件
        config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            sample_config,
            "fit_config_1.yaml"
        )
        
        config_manager.save_tool_configuration(
            ScreeningToolType.COLONOSCOPY,
            sample_config,
            "colonoscopy_config_1.yaml",
            validate=False  # 跳过验证，因为sample_config的成本对结肠镜来说太低
        )
        
        # 列出配置
        configurations = config_manager.list_available_configurations()
        
        assert len(configurations) >= 2
        assert any(config["file_name"] == "fit_config_1.yaml" for config in configurations)
        assert any(config["file_name"] == "colonoscopy_config_1.yaml" for config in configurations)
        
        # 检查配置信息
        fit_config = next(config for config in configurations if config["file_name"] == "fit_config_1.yaml")
        assert fit_config["tool_name"] == "测试FIT工具"
        assert fit_config["tool_type"] == "FIT"
    
    def test_create_tool_from_config(self, config_manager, sample_config):
        """测试从配置创建工具"""
        # 保存配置
        config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            sample_config,
            "tool_creation_test.yaml"
        )
        
        # 从配置创建工具
        tool = config_manager.create_tool_from_config(
            ScreeningToolType.FIT,
            "tool_creation_test.yaml"
        )
        
        assert tool.tool_type == ScreeningToolType.FIT
        assert tool.performance.specificity == 0.95
    
    def test_configuration_caching(self, config_manager, sample_config):
        """测试配置缓存"""
        # 保存配置
        config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            sample_config,
            "cache_test.yaml"
        )
        
        # 第一次加载
        config1 = config_manager.load_tool_configuration(
            ScreeningToolType.FIT,
            "cache_test.yaml"
        )
        
        # 第二次加载（应该从缓存）
        config2 = config_manager.load_tool_configuration(
            ScreeningToolType.FIT,
            "cache_test.yaml"
        )
        
        assert config1 is config2  # 应该是同一个对象（从缓存）
        
        # 检查缓存信息
        cache_info = config_manager.get_cache_info()
        assert cache_info["cached_configs"] >= 1
    
    def test_clear_cache(self, config_manager, sample_config):
        """测试清空缓存"""
        # 加载配置到缓存
        config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            sample_config,
            "clear_cache_test.yaml"
        )
        config_manager.load_tool_configuration(
            ScreeningToolType.FIT,
            "clear_cache_test.yaml"
        )
        
        # 确认缓存中有数据
        cache_info_before = config_manager.get_cache_info()
        assert cache_info_before["cached_configs"] > 0
        
        # 清空缓存
        config_manager.clear_cache()
        
        # 确认缓存已清空
        cache_info_after = config_manager.get_cache_info()
        assert cache_info_after["cached_configs"] == 0
