"""
疾病状态和相关枚举定义

定义结直肠癌筛查模拟中使用的所有枚举类型。
"""

from enum import Enum
from typing import List, Set


class DiseaseState(Enum):
    """疾病状态枚举"""
    NORMAL = "normal"
    LOW_RISK_ADENOMA = "low_risk_adenoma"
    HIGH_RISK_ADENOMA = "high_risk_adenoma"
    SMALL_SERRATED = "small_serrated"
    LARGE_SERRATED = "large_serrated"
    PRECLINICAL_CANCER = "preclinical_cancer"
    CLINICAL_CANCER_STAGE_I = "clinical_cancer_stage_i"
    CLINICAL_CANCER_STAGE_II = "clinical_cancer_stage_ii"
    CLINICAL_CANCER_STAGE_III = "clinical_cancer_stage_iii"
    CLINICAL_CANCER_STAGE_IV = "clinical_cancer_stage_iv"
    CLINICAL_CANCER = "clinical_cancer"  # 保留向后兼容性
    DEATH_CANCER = "death_cancer"
    DEATH_OTHER = "death_other"

    @classmethod
    def get_cancer_states(cls) -> Set["DiseaseState"]:
        """获取所有癌症相关状态"""
        return {
            cls.PRECLINICAL_CANCER,
            cls.CLINICAL_CANCER_STAGE_I,
            cls.CLINICAL_CANCER_STAGE_II,
            cls.CLINICAL_CANCER_STAGE_III,
            cls.CLINICAL_CANCER_STAGE_IV,
            cls.CLINICAL_CANCER,  # 向后兼容
            cls.DEATH_CANCER
        }

    @classmethod
    def get_adenoma_states(cls) -> Set["DiseaseState"]:
        """获取所有腺瘤状态"""
        return {cls.LOW_RISK_ADENOMA, cls.HIGH_RISK_ADENOMA}

    @classmethod
    def get_serrated_states(cls) -> Set["DiseaseState"]:
        """获取所有锯齿状腺瘤状态"""
        return {cls.SMALL_SERRATED, cls.LARGE_SERRATED}

    @classmethod
    def get_death_states(cls) -> Set["DiseaseState"]:
        """获取所有死亡状态"""
        return {cls.DEATH_CANCER, cls.DEATH_OTHER}

    @classmethod
    def get_clinical_cancer_stages(cls) -> Set["DiseaseState"]:
        """获取所有临床癌症分期状态"""
        return {
            cls.CLINICAL_CANCER_STAGE_I,
            cls.CLINICAL_CANCER_STAGE_II,
            cls.CLINICAL_CANCER_STAGE_III,
            cls.CLINICAL_CANCER_STAGE_IV
        }

    def is_cancer(self) -> bool:
        """判断是否为癌症状态"""
        return self in self.get_cancer_states()

    def is_adenoma(self) -> bool:
        """判断是否为腺瘤状态"""
        return self in self.get_adenoma_states()

    def is_serrated(self) -> bool:
        """判断是否为锯齿状腺瘤状态"""
        return self in self.get_serrated_states()

    def is_death(self) -> bool:
        """判断是否为死亡状态"""
        return self in self.get_death_states()

    def is_clinical_cancer_stage(self) -> bool:
        """判断是否为临床癌症分期状态"""
        return self in self.get_clinical_cancer_stages()


class PathwayType(Enum):
    """疾病进展通路类型"""
    ADENOMA_CARCINOMA = "adenoma_carcinoma"
    SERRATED_ADENOMA = "serrated_adenoma"

    def get_compatible_states(self) -> Set[DiseaseState]:
        """获取与该通路兼容的疾病状态"""
        if self == PathwayType.ADENOMA_CARCINOMA:
            return {
                DiseaseState.NORMAL,
                DiseaseState.LOW_RISK_ADENOMA,
                DiseaseState.HIGH_RISK_ADENOMA,
                DiseaseState.PRECLINICAL_CANCER,
                DiseaseState.CLINICAL_CANCER_STAGE_I,
                DiseaseState.CLINICAL_CANCER_STAGE_II,
                DiseaseState.CLINICAL_CANCER_STAGE_III,
                DiseaseState.CLINICAL_CANCER_STAGE_IV,
                DiseaseState.CLINICAL_CANCER,  # 向后兼容
                DiseaseState.DEATH_CANCER,
                DiseaseState.DEATH_OTHER,
            }
        elif self == PathwayType.SERRATED_ADENOMA:
            return {
                DiseaseState.NORMAL,
                DiseaseState.SMALL_SERRATED,
                DiseaseState.LARGE_SERRATED,
                DiseaseState.PRECLINICAL_CANCER,
                DiseaseState.CLINICAL_CANCER_STAGE_I,
                DiseaseState.CLINICAL_CANCER_STAGE_II,
                DiseaseState.CLINICAL_CANCER_STAGE_III,
                DiseaseState.CLINICAL_CANCER_STAGE_IV,
                DiseaseState.CLINICAL_CANCER,  # 向后兼容
                DiseaseState.DEATH_CANCER,
                DiseaseState.DEATH_OTHER,
            }
        return set()


class CancerStage(Enum):
    """癌症分期"""
    STAGE_I = "stage_i"
    STAGE_II = "stage_ii"
    STAGE_III = "stage_iii"
    STAGE_IV = "stage_iv"
    ADENOMA = "adenoma"  # 腺瘤
    SERRATED_ADENOMA = "serrated_adenoma"  # 锯齿状腺瘤

    @classmethod
    def get_early_stages(cls) -> Set["CancerStage"]:
        """获取早期癌症分期"""
        return {cls.STAGE_I, cls.STAGE_II}

    @classmethod
    def get_late_stages(cls) -> Set["CancerStage"]:
        """获取晚期癌症分期"""
        return {cls.STAGE_III, cls.STAGE_IV}

    @classmethod
    def get_progression_order(cls) -> List["CancerStage"]:
        """获取癌症分期进展顺序"""
        return [cls.STAGE_I, cls.STAGE_II, cls.STAGE_III, cls.STAGE_IV]

    def is_early_stage(self) -> bool:
        """判断是否为早期分期"""
        return self in self.get_early_stages()

    def is_late_stage(self) -> bool:
        """判断是否为晚期分期"""
        return self in self.get_late_stages()

    def get_next_stage(self) -> "CancerStage":
        """获取下一个分期"""
        progression = self.get_progression_order()
        try:
            current_index = progression.index(self)
            if current_index < len(progression) - 1:
                return progression[current_index + 1]
        except ValueError:
            pass  # 如果不在progression中（如腺瘤），返回自身
        return self


class AnatomicalLocation(Enum):
    """解剖位置枚举"""
    PROXIMAL_COLON = "proximal_colon"    # 近端结肠
    DISTAL_COLON = "distal_colon"        # 远端结肠
    RECTUM = "rectum"                    # 直肠


class Gender(Enum):
    """性别枚举"""
    MALE = "male"
    FEMALE = "female"


class ScreeningResult(Enum):
    """筛查结果枚举"""
    NEGATIVE = "negative"
    POSITIVE = "positive"
    INADEQUATE = "inadequate"
    NOT_PERFORMED = "not_performed"


class ScreeningTool(Enum):
    """筛查工具枚举"""
    COLONOSCOPY = "colonoscopy"
    SIGMOIDOSCOPY = "sigmoidoscopy"
    FIT = "fit"  # 粪便免疫化学检测
    FOBT = "fobt"  # 粪便潜血试验
    CT_COLONOGRAPHY = "ct_colonography"
    STOOL_DNA = "stool_dna"
