"""
筛查工具配置管理系统

实现筛查工具配置的导入、导出、验证和版本管理功能。
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from src.utils.validators import (
    validate_screening_tool_configuration,
    validate_performance_parameter_consistency,
    validate_cost_parameter_reasonableness,
    ScreeningToolValidationError
)
from .enums import ScreeningToolType
from .screening_tool import ScreeningToolFactory


logger = logging.getLogger(__name__)


@dataclass
class ConfigurationMetadata:
    """配置元数据"""
    
    created_date: str
    created_by: str
    last_modified: str
    version: str
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ConfigurationMetadata":
        """从字典创建"""
        return cls(**data)


class ScreeningToolConfigManager:
    """筛查工具配置管理器"""
    
    def __init__(self, config_directory: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_directory: 配置文件目录路径
        """
        if config_directory is None:
            # 默认配置目录
            project_root = Path(__file__).parent.parent.parent.parent
            config_directory = project_root / "data" / "screening_tools"
        
        self.config_directory = Path(config_directory)
        self.config_directory.mkdir(parents=True, exist_ok=True)
        
        # 配置缓存
        self._config_cache: Dict[str, Dict[str, Any]] = {}
        self._metadata_cache: Dict[str, ConfigurationMetadata] = {}
    
    def load_tool_configuration(
        self,
        tool_type: Union[ScreeningToolType, str],
        config_file: Optional[str] = None,
        validate: bool = True
    ) -> Dict[str, Any]:
        """
        加载筛查工具配置
        
        Args:
            tool_type: 工具类型
            config_file: 配置文件路径（可选）
            validate: 是否验证配置
            
        Returns:
            Dict[str, Any]: 配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            ScreeningToolValidationError: 配置验证失败
        """
        # 标准化工具类型
        if isinstance(tool_type, str):
            tool_type_str = tool_type.lower()
        else:
            tool_type_str = tool_type.value
        
        # 检查缓存
        cache_key = f"{tool_type_str}_{config_file or 'default'}"
        if cache_key in self._config_cache:
            logger.debug(f"从缓存加载配置: {cache_key}")
            return self._config_cache[cache_key]
        
        # 确定配置文件路径
        if config_file is None:
            config_file = self._get_default_config_file(tool_type_str)
        
        config_path = self.config_directory / config_file
        
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        # 加载配置文件
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
                    config_data = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    config_data = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            logger.info(f"成功加载配置文件: {config_path}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {config_path}, 错误: {e}")
            raise ScreeningToolValidationError(
                f"加载配置文件失败: {e}",
                "config_file",
                str(config_path)
            )
        
        # 验证配置
        if validate:
            try:
                config_data = self._validate_configuration(config_data, tool_type_str)
            except Exception as e:
                logger.error(f"配置验证失败: {e}")
                raise
        
        # 缓存配置
        self._config_cache[cache_key] = config_data
        
        return config_data
    
    def save_tool_configuration(
        self,
        tool_type: Union[ScreeningToolType, str],
        config_data: Dict[str, Any],
        config_file: Optional[str] = None,
        format_type: str = "yaml",
        validate: bool = True
    ) -> str:
        """
        保存筛查工具配置
        
        Args:
            tool_type: 工具类型
            config_data: 配置数据
            config_file: 配置文件名（可选）
            format_type: 文件格式（yaml/json）
            validate: 是否验证配置
            
        Returns:
            str: 保存的文件路径
            
        Raises:
            ScreeningToolValidationError: 配置验证失败
        """
        # 标准化工具类型
        if isinstance(tool_type, str):
            tool_type_str = tool_type.lower()
        else:
            tool_type_str = tool_type.value
        
        # 验证配置
        if validate:
            config_data = self._validate_configuration(config_data, tool_type_str)
        
        # 确定配置文件路径
        if config_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_file = f"{tool_type_str}_config_{timestamp}.{format_type}"
        
        config_path = self.config_directory / config_file
        
        # 添加元数据
        if "metadata" not in config_data:
            config_data["metadata"] = {}
        
        config_data["metadata"].update({
            "last_modified": datetime.now().isoformat(),
            "saved_by": "ScreeningToolConfigManager"
        })
        
        # 保存配置文件
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                if format_type.lower() == "yaml":
                    yaml.dump(config_data, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif format_type.lower() == "json":
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
                else:
                    raise ValueError(f"不支持的文件格式: {format_type}")
            
            logger.info(f"成功保存配置文件: {config_path}")
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {config_path}, 错误: {e}")
            raise ScreeningToolValidationError(
                f"保存配置文件失败: {e}",
                "config_save",
                str(config_path)
            )
        
        # 更新缓存
        cache_key = f"{tool_type_str}_{config_file}"
        self._config_cache[cache_key] = config_data
        
        return str(config_path)
    
    def list_available_configurations(self) -> List[Dict[str, Any]]:
        """
        列出所有可用的配置文件
        
        Returns:
            List[Dict[str, Any]]: 配置文件信息列表
        """
        configurations = []
        
        for config_file in self.config_directory.glob("*.yaml"):
            try:
                # 读取基本信息
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                tool_info = config_data.get("screening_tool", {})
                metadata = config_data.get("metadata", {})
                
                configurations.append({
                    "file_name": config_file.name,
                    "file_path": str(config_file),
                    "tool_name": tool_info.get("name", "未知"),
                    "tool_type": tool_info.get("type", "未知"),
                    "version": tool_info.get("version", "未知"),
                    "description": tool_info.get("description", ""),
                    "last_modified": metadata.get("last_modified", "未知"),
                    "file_size": config_file.stat().st_size
                })
                
            except Exception as e:
                logger.warning(f"读取配置文件信息失败: {config_file}, 错误: {e}")
                configurations.append({
                    "file_name": config_file.name,
                    "file_path": str(config_file),
                    "tool_name": "读取失败",
                    "tool_type": "未知",
                    "version": "未知",
                    "description": f"读取失败: {e}",
                    "last_modified": "未知",
                    "file_size": config_file.stat().st_size
                })
        
        return configurations
    
    def create_tool_from_config(
        self,
        tool_type: Union[ScreeningToolType, str],
        config_file: Optional[str] = None
    ):
        """
        从配置文件创建筛查工具实例
        
        Args:
            tool_type: 工具类型
            config_file: 配置文件路径（可选）
            
        Returns:
            ScreeningTool: 工具实例
        """
        # 加载配置
        config_data = self.load_tool_configuration(tool_type, config_file)
        
        # 提取工具配置
        tool_config = config_data.get("screening_tool", {})
        
        # 转换工具类型
        if isinstance(tool_type, str):
            for enum_type in ScreeningToolType:
                if enum_type.value == tool_type.lower():
                    tool_type = enum_type
                    break
        
        # 创建工具实例
        return ScreeningToolFactory.create_tool(tool_type, tool_config)
    
    def _get_default_config_file(self, tool_type: str) -> str:
        """获取默认配置文件名"""
        return f"{tool_type}_tool_config.yaml"
    
    def _validate_configuration(
        self,
        config_data: Dict[str, Any],
        tool_type: str
    ) -> Dict[str, Any]:
        """验证配置数据"""
        # 检查基本结构
        if "screening_tool" not in config_data:
            raise ScreeningToolValidationError(
                "配置文件必须包含 'screening_tool' 部分",
                "config_structure",
                config_data
            )
        
        tool_config = config_data["screening_tool"]
        
        # 验证工具配置
        validated_tool_config = validate_screening_tool_configuration(
            tool_config, tool_type
        )
        
        # 验证性能参数一致性
        if "performance" in validated_tool_config:
            performance = validated_tool_config["performance"]
            if "sensitivity_by_state" in performance and "specificity" in performance:
                validate_performance_parameter_consistency(
                    performance["sensitivity_by_state"],
                    performance["specificity"]
                )
        
        # 验证成本参数合理性
        if "costs" in validated_tool_config:
            validate_cost_parameter_reasonableness(
                validated_tool_config["costs"],
                tool_type
            )
        
        # 更新配置
        config_data["screening_tool"] = validated_tool_config
        
        return config_data
    
    def clear_cache(self) -> None:
        """清空配置缓存"""
        self._config_cache.clear()
        self._metadata_cache.clear()
        logger.info("配置缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            "cached_configs": len(self._config_cache),
            "cached_metadata": len(self._metadata_cache),
            "cache_keys": list(self._config_cache.keys())
        }
