"""
神经网络校准接口

实现与深度神经网络校准系统的接口，支持参数向量化、反向量化、
校准结果更新和状态跟踪功能。
"""

import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
import logging
from datetime import datetime
from pathlib import Path
import json

from src.modules.config.parameter_manager import ParameterManager

logger = logging.getLogger(__name__)


@dataclass
class CalibrationTarget:
    """校准目标定义"""
    name: str
    target_value: float
    weight: float = 1.0
    tolerance: float = 0.05
    description: str = ""


@dataclass
class CalibrationResult:
    """校准结果"""
    iteration: int
    parameter_vector: np.ndarray
    loss_value: float
    target_errors: Dict[str, float]
    convergence_status: str
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class CalibrationStatus:
    """校准状态"""
    is_running: bool = False
    current_iteration: int = 0
    total_iterations: int = 0
    best_loss: float = float('inf')
    convergence_achieved: bool = False
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class NeuralNetworkCalibrator:
    """神经网络校准器
    
    提供与深度神经网络校准系统的接口，支持：
    - 参数向量化和反向量化
    - 校准目标管理
    - 校准结果处理和参数更新
    - 校准状态跟踪和日志记录
    - 校准结果验证
    """

    def __init__(
        self,
        parameter_manager: ParameterManager,
        calibration_config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化神经网络校准器
        
        Args:
            parameter_manager: 参数管理器实例
            calibration_config: 校准配置
        """
        self.parameter_manager = parameter_manager
        self.config = calibration_config or {}
        
        # 校准相关属性
        self.calibration_targets: Dict[str, CalibrationTarget] = {}
        self.parameter_mapping: Dict[str, int] = {}  # 参数名到向量索引的映射
        self.vector_mapping: Dict[int, str] = {}     # 向量索引到参数名的映射
        self.calibration_history: List[CalibrationResult] = []
        self.calibration_status = CalibrationStatus()
        
        # 校准函数
        self.objective_function: Optional[Callable] = None
        self.constraint_functions: List[Callable] = []
        
        # 初始化校准目标和参数映射
        self._initialize_calibration_targets()
        self._initialize_parameter_mapping()
        
        logger.info("神经网络校准器初始化完成")

    def _initialize_calibration_targets(self) -> None:
        """初始化默认校准目标"""
        # 从配置加载校准目标
        if "calibration_targets" in self.config:
            for target_config in self.config["calibration_targets"]:
                target = CalibrationTarget(**target_config)
                self.calibration_targets[target.name] = target
        else:
            # 使用默认校准目标
            default_targets = []

            # 男性不同年龄段低风险腺瘤患病率
            male_low_risk_adenoma_targets = [
                ("50-59", 0.15, "50-59岁男性低风险腺瘤患病率"),
                ("60-69", 0.25, "60-69岁男性低风险腺瘤患病率"),
                ("70-79", 0.35, "70-79岁男性低风险腺瘤患病率"),
                ("80+", 0.40, "80岁以上男性低风险腺瘤患病率")
            ]

            for age_group, target_value, description in male_low_risk_adenoma_targets:
                default_targets.append(CalibrationTarget(
                    name=f"male_low_risk_adenoma_{age_group.replace('-', '_').replace('+', 'plus')}",
                    target_value=target_value,
                    weight=1.0,
                    tolerance=0.02,
                    description=description
                ))

            # 女性不同年龄段低风险腺瘤患病率
            female_low_risk_adenoma_targets = [
                ("50-59", 0.12, "50-59岁女性低风险腺瘤患病率"),
                ("60-69", 0.20, "60-69岁女性低风险腺瘤患病率"),
                ("70-79", 0.28, "70-79岁女性低风险腺瘤患病率"),
                ("80+", 0.32, "80岁以上女性低风险腺瘤患病率")
            ]

            for age_group, target_value, description in female_low_risk_adenoma_targets:
                default_targets.append(CalibrationTarget(
                    name=f"female_low_risk_adenoma_{age_group.replace('-', '_').replace('+', 'plus')}",
                    target_value=target_value,
                    weight=1.0,
                    tolerance=0.02,
                    description=description
                ))

            # 男性不同年龄段高风险腺瘤患病率
            male_high_risk_adenoma_targets = [
                ("50-59", 0.08, "50-59岁男性高风险腺瘤患病率"),
                ("60-69", 0.15, "60-69岁男性高风险腺瘤患病率"),
                ("70-79", 0.22, "70-79岁男性高风险腺瘤患病率"),
                ("80+", 0.25, "80岁以上男性高风险腺瘤患病率")
            ]

            for age_group, target_value, description in male_high_risk_adenoma_targets:
                default_targets.append(CalibrationTarget(
                    name=f"male_high_risk_adenoma_{age_group.replace('-', '_').replace('+', 'plus')}",
                    target_value=target_value,
                    weight=1.2,
                    tolerance=0.015,
                    description=description
                ))

            # 女性不同年龄段高风险腺瘤患病率
            female_high_risk_adenoma_targets = [
                ("50-59", 0.06, "50-59岁女性高风险腺瘤患病率"),
                ("60-69", 0.12, "60-69岁女性高风险腺瘤患病率"),
                ("70-79", 0.18, "70-79岁女性高风险腺瘤患病率"),
                ("80+", 0.20, "80岁以上女性高风险腺瘤患病率")
            ]

            for age_group, target_value, description in female_high_risk_adenoma_targets:
                default_targets.append(CalibrationTarget(
                    name=f"female_high_risk_adenoma_{age_group.replace('-', '_').replace('+', 'plus')}",
                    target_value=target_value,
                    weight=1.2,
                    tolerance=0.015,
                    description=description
                ))

            # 男性不同年龄段结直肠癌发病率
            male_cancer_incidence_targets = [
                ("50-59", 0.0003, "50-59岁男性结直肠癌年发病率"),
                ("60-69", 0.0008, "60-69岁男性结直肠癌年发病率"),
                ("70-79", 0.0015, "70-79岁男性结直肠癌年发病率"),
                ("80+", 0.0020, "80岁以上男性结直肠癌年发病率")
            ]

            for age_group, target_value, description in male_cancer_incidence_targets:
                default_targets.append(CalibrationTarget(
                    name=f"male_cancer_incidence_{age_group.replace('-', '_').replace('+', 'plus')}",
                    target_value=target_value,
                    weight=1.5,
                    tolerance=0.0001,
                    description=description
                ))

            # 女性不同年龄段结直肠癌发病率
            female_cancer_incidence_targets = [
                ("50-59", 0.0002, "50-59岁女性结直肠癌年发病率"),
                ("60-69", 0.0006, "60-69岁女性结直肠癌年发病率"),
                ("70-79", 0.0012, "70-79岁女性结直肠癌年发病率"),
                ("80+", 0.0016, "80岁以上女性结直肠癌年发病率")
            ]

            for age_group, target_value, description in female_cancer_incidence_targets:
                default_targets.append(CalibrationTarget(
                    name=f"female_cancer_incidence_{age_group.replace('-', '_').replace('+', 'plus')}",
                    target_value=target_value,
                    weight=1.5,
                    tolerance=0.0001,
                    description=description
                ))

            # 男性癌症不同肠道部位占比
            male_location_proportion_targets = [
                ("proximal_colon", 0.38, "男性近端结肠癌占比"),
                ("distal_colon", 0.37, "男性远端结肠癌占比"),
                ("rectum", 0.25, "男性直肠癌占比")
            ]

            for location, target_value, description in male_location_proportion_targets:
                default_targets.append(CalibrationTarget(
                    name=f"male_cancer_location_{location}",
                    target_value=target_value,
                    weight=1.3,
                    tolerance=0.03,
                    description=description
                ))

            # 女性癌症不同肠道部位占比
            female_location_proportion_targets = [
                ("proximal_colon", 0.42, "女性近端结肠癌占比"),
                ("distal_colon", 0.33, "女性远端结肠癌占比"),
                ("rectum", 0.25, "女性直肠癌占比")
            ]

            for location, target_value, description in female_location_proportion_targets:
                default_targets.append(CalibrationTarget(
                    name=f"female_cancer_location_{location}",
                    target_value=target_value,
                    weight=1.3,
                    tolerance=0.03,
                    description=description
                ))
            
            for target in default_targets:
                self.calibration_targets[target.name] = target

    def _initialize_parameter_mapping(self) -> None:
        """初始化参数映射"""
        # 获取所有可训练参数
        trainable_params = self.parameter_manager.get_trainable_parameters()
        
        # 创建参数名到向量索引的映射
        for i, param_name in enumerate(sorted(trainable_params.keys())):
            self.parameter_mapping[param_name] = i
            self.vector_mapping[i] = param_name
        
        logger.info(f"初始化参数映射，共 {len(trainable_params)} 个可训练参数")

    def vectorize_parameters(self, parameter_dict: Optional[Dict[str, Any]] = None) -> np.ndarray:
        """
        将参数字典向量化
        
        Args:
            parameter_dict: 参数字典，如果为None则使用当前参数
            
        Returns:
            np.ndarray: 参数向量
        """
        if parameter_dict is None:
            parameter_dict = self.parameter_manager.get_trainable_parameters()
        
        # 创建参数向量
        vector_size = len(self.parameter_mapping)
        parameter_vector = np.zeros(vector_size)
        
        for param_name, vector_index in self.parameter_mapping.items():
            if param_name in parameter_dict:
                parameter_vector[vector_index] = float(parameter_dict[param_name])
            else:
                # 使用当前参数值作为默认值
                parameter_vector[vector_index] = float(
                    self.parameter_manager.get_parameter(param_name)
                )
        
        return parameter_vector

    def devectorize_parameters(self, parameter_vector: np.ndarray) -> Dict[str, float]:
        """
        将参数向量反向量化为参数字典
        
        Args:
            parameter_vector: 参数向量
            
        Returns:
            Dict[str, float]: 参数字典
        """
        if len(parameter_vector) != len(self.vector_mapping):
            raise ValueError(
                f"参数向量长度 {len(parameter_vector)} 与映射长度 {len(self.vector_mapping)} 不匹配"
            )
        
        parameter_dict = {}
        for vector_index, param_name in self.vector_mapping.items():
            parameter_dict[param_name] = float(parameter_vector[vector_index])
        
        return parameter_dict

    def update_parameters_from_vector(self, parameter_vector: np.ndarray) -> None:
        """
        从参数向量更新参数管理器中的参数
        
        Args:
            parameter_vector: 参数向量
        """
        parameter_dict = self.devectorize_parameters(parameter_vector)
        
        # 验证参数约束
        for param_name, value in parameter_dict.items():
            try:
                # 临时设置参数以验证约束
                self.parameter_manager.set_parameter(param_name, value)
            except ValueError as e:
                logger.warning(f"参数 {param_name} 值 {value} 违反约束: {e}")
                # 使用约束范围内的最近值
                param_def = self.parameter_manager.parameters[param_name]
                if param_def.constraint:
                    if param_def.constraint.min_value is not None:
                        value = max(value, param_def.constraint.min_value)
                    if param_def.constraint.max_value is not None:
                        value = min(value, param_def.constraint.max_value)
                    
                    self.parameter_manager.set_parameter(param_name, value)
                    parameter_dict[param_name] = value
        
        logger.info(f"从向量更新了 {len(parameter_dict)} 个参数")

    def add_calibration_target(self, target: CalibrationTarget) -> None:
        """
        添加校准目标
        
        Args:
            target: 校准目标
        """
        self.calibration_targets[target.name] = target
        logger.info(f"添加校准目标: {target.name} = {target.target_value}")

    def remove_calibration_target(self, target_name: str) -> None:
        """
        移除校准目标
        
        Args:
            target_name: 目标名称
        """
        if target_name in self.calibration_targets:
            del self.calibration_targets[target_name]
            logger.info(f"移除校准目标: {target_name}")

    def set_objective_function(self, objective_func: Callable) -> None:
        """
        设置目标函数
        
        Args:
            objective_func: 目标函数，接受参数向量，返回损失值
        """
        self.objective_function = objective_func
        logger.info("设置目标函数")

    def add_constraint_function(self, constraint_func: Callable) -> None:
        """
        添加约束函数
        
        Args:
            constraint_func: 约束函数，接受参数向量，返回约束违反程度
        """
        self.constraint_functions.append(constraint_func)
        logger.info("添加约束函数")

    def start_calibration(
        self,
        max_iterations: int = 1000,
        convergence_tolerance: float = 1e-6
    ) -> None:
        """
        开始校准过程
        
        Args:
            max_iterations: 最大迭代次数
            convergence_tolerance: 收敛容差
        """
        self.calibration_status = CalibrationStatus(
            is_running=True,
            current_iteration=0,
            total_iterations=max_iterations,
            best_loss=float('inf'),
            convergence_achieved=False,
            start_time=datetime.now()
        )
        
        logger.info(f"开始校准，最大迭代次数: {max_iterations}")

    def update_calibration_result(self, result: CalibrationResult) -> None:
        """
        更新校准结果
        
        Args:
            result: 校准结果
        """
        # 更新参数
        self.update_parameters_from_vector(result.parameter_vector)
        
        # 记录结果
        self.calibration_history.append(result)
        
        # 更新状态
        self.calibration_status.current_iteration = result.iteration
        if result.loss_value < self.calibration_status.best_loss:
            self.calibration_status.best_loss = result.loss_value
        
        # 检查收敛
        if result.convergence_status == "converged":
            self.calibration_status.convergence_achieved = True
            self.stop_calibration()
        
        logger.info(
            f"校准迭代 {result.iteration}: 损失 = {result.loss_value:.6f}, "
            f"状态 = {result.convergence_status}"
        )

    def stop_calibration(self) -> None:
        """停止校准过程"""
        self.calibration_status.is_running = False
        self.calibration_status.end_time = datetime.now()
        
        duration = (
            self.calibration_status.end_time - self.calibration_status.start_time
        ).total_seconds()
        
        logger.info(f"校准结束，耗时: {duration:.2f} 秒")

    def validate_calibration_result(self) -> Dict[str, Any]:
        """
        验证校准结果
        
        Returns:
            Dict: 验证结果
        """
        if not self.calibration_history:
            return {"status": "no_results", "message": "没有校准结果"}
        
        # 获取最佳结果
        best_result = min(self.calibration_history, key=lambda r: r.loss_value)
        
        # 检查目标达成情况
        target_achievements = {}
        all_targets_met = True
        
        for target_name, target in self.calibration_targets.items():
            if target_name in best_result.target_errors:
                error = abs(best_result.target_errors[target_name])
                achieved = error <= target.tolerance
                target_achievements[target_name] = {
                    "achieved": achieved,
                    "error": error,
                    "tolerance": target.tolerance,
                    "relative_error": error / target.target_value if target.target_value != 0 else 0
                }
                
                if not achieved:
                    all_targets_met = False
        
        # 参数有效性检查
        parameter_validity = self.parameter_manager.validate_all_parameters()
        
        validation_result = {
            "status": "valid" if all_targets_met and parameter_validity else "invalid",
            "all_targets_met": all_targets_met,
            "parameter_validity": parameter_validity,
            "best_loss": best_result.loss_value,
            "total_iterations": len(self.calibration_history),
            "convergence_achieved": self.calibration_status.convergence_achieved,
            "target_achievements": target_achievements
        }
        
        return validation_result

    def export_calibration_results(self, file_path: str) -> None:
        """
        导出校准结果
        
        Args:
            file_path: 导出文件路径
        """
        export_data = {
            "calibration_status": {
                "is_running": self.calibration_status.is_running,
                "current_iteration": self.calibration_status.current_iteration,
                "total_iterations": self.calibration_status.total_iterations,
                "best_loss": self.calibration_status.best_loss,
                "convergence_achieved": self.calibration_status.convergence_achieved,
                "start_time": self.calibration_status.start_time.isoformat() if self.calibration_status.start_time else None,
                "end_time": self.calibration_status.end_time.isoformat() if self.calibration_status.end_time else None
            },
            "calibration_targets": {
                name: {
                    "target_value": target.target_value,
                    "weight": target.weight,
                    "tolerance": target.tolerance,
                    "description": target.description
                }
                for name, target in self.calibration_targets.items()
            },
            "calibration_history": [
                {
                    "iteration": result.iteration,
                    "parameter_vector": result.parameter_vector.tolist(),
                    "loss_value": result.loss_value,
                    "target_errors": result.target_errors,
                    "convergence_status": result.convergence_status,
                    "timestamp": result.timestamp.isoformat()
                }
                for result in self.calibration_history
            ],
            "final_parameters": self.parameter_manager.get_trainable_parameters(),
            "validation_result": self.validate_calibration_result()
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"校准结果已导出到: {file_path}")

    def get_calibration_summary(self) -> Dict[str, Any]:
        """
        获取校准摘要
        
        Returns:
            Dict: 校准摘要
        """
        if not self.calibration_history:
            return {"status": "no_calibration_performed"}
        
        best_result = min(self.calibration_history, key=lambda r: r.loss_value)
        
        return {
            "total_iterations": len(self.calibration_history),
            "best_loss": best_result.loss_value,
            "convergence_achieved": self.calibration_status.convergence_achieved,
            "calibration_targets_count": len(self.calibration_targets),
            "trainable_parameters_count": len(self.parameter_mapping),
            "validation_result": self.validate_calibration_result(),
            "duration_seconds": (
                (self.calibration_status.end_time - self.calibration_status.start_time).total_seconds()
                if self.calibration_status.start_time and self.calibration_status.end_time
                else None
            )
        }

    def reset_calibration(self) -> None:
        """重置校准状态和历史"""
        self.calibration_history.clear()
        self.calibration_status = CalibrationStatus()
        logger.info("校准状态和历史已重置")
