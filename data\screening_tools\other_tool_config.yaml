# 其他筛查工具配置文件（通用模板）
screening_tool:
  name: "其他筛查工具"
  type: "OTHER"
  version: "1.0"
  description: "通用筛查工具配置模板，可根据具体工具进行定制"
  
  # 工具特定信息
  tool_name: "通用筛查工具"
  tool_description: "可配置的通用筛查工具实现"
  
  # 性能参数配置
  performance:
    # 总体特异性（可根据具体工具调整）
    specificity: 0.85
    
    # 疾病状态特异性敏感性（示例值，需根据具体工具调整）
    sensitivity_by_state:
      normal: 0.0
      low_risk_adenoma: 0.20
      high_risk_adenoma: 0.40
      small_serrated: 0.15
      large_serrated: 0.30
      preclinical_cancer: 0.70
      clinical_cancer_stage_i: 0.80
      clinical_cancer_stage_ii: 0.85
      clinical_cancer_stage_iii: 0.90
      clinical_cancer_stage_iv: 0.95
    
    # 解剖位置特异性敏感性调整因子
    location_sensitivity_modifiers:
      proximal_colon: 0.9            # 近端结肠
      distal_colon: 1.0              # 远端结肠
      rectum: 1.0                    # 直肠
  
  # 检测机制配置
  detection_mechanism:
    type: "probability_based"        # 检测机制类型
    base_accuracy: 0.8               # 基础准确性
    variability: 0.1                 # 变异性
    
    # 可选的阈值配置（当type为threshold_based时使用）
    # threshold: 0.5
    # above_threshold_probability: 0.9
    # below_threshold_probability: 0.1
  
  # 特殊参数配置
  special_parameters:
    # 年龄因素（暂时注销）
    # age_factor:
    #   slope: 0.001                 # 年龄斜率
    #   baseline: 50.0               # 基线年龄
    
    # 性别因素
    gender_factor:
      male: 1.0
      female: 0.95
    
    # 其他自定义参数
    custom_adjustments:
      quality_factor: 1.0            # 质量因子
      environmental_factor: 1.0      # 环境因子
  
  # 成本配置
  costs:
    direct_cost: 100.0               # 直接检查费用
    material_cost: 20.0              # 材料费用
    processing_cost: 30.0            # 处理费用
    indirect_cost: 80.0              # 间接成本
    
    # 成本调整因子（暂时注销）
    # age_adjustment:
    #   young: 1.0                   # <65岁
    #   middle: 1.1                  # 65-75岁
    #   elderly: 1.2                 # >75岁
    
    gender_adjustment:
      male: 1.0
      female: 1.0
    
    regional_adjustment:
      urban: 1.0
      suburban: 0.9
      rural: 0.8
  
  # 工具特性
  characteristics:
    invasiveness: "minimally_invasive"  # 侵入性程度
    preparation_required: false         # 是否需要准备
    # operator_skill_required: "medium"  # 操作者技能要求（暂时注销）
    turnaround_time_days: 1            # 周转时间
    requires_sedation: false           # 是否需要镇静
    radiation_exposure: false          # 是否有辐射暴露
    sample_collection_required: true   # 是否需要样本采集
    
    # 检测能力
    can_detect_proximal: true          # 能否检测近端
    can_detect_distal: true            # 能否检测远端
    can_detect_rectal: true            # 能否检测直肠
    
    # 其他特性
    is_generic_implementation: true    # 是否为通用实现
    customizable: true                 # 是否可定制
    validation_required: true          # 是否需要验证
  
  # 工具特定配置
  tool_specific_config:
    # 检测参数
    detection_parameters:
      sensitivity_range: [0.1, 0.95]  # 敏感性范围
      specificity_range: [0.7, 0.99]  # 特异性范围
      accuracy_target: 0.85            # 目标准确性
    
    # 质量控制
    quality_control:
      calibration_required: true       # 是否需要校准
      quality_assurance: true          # 质量保证
      performance_monitoring: true     # 性能监控
    
    # 操作参数
    operational_parameters:
      batch_processing: false          # 批量处理
      real_time_results: true          # 实时结果
      automated_analysis: false        # 自动分析
  
  # 验证和限制
  validation_rules:
    # 个体适用性
    eligibility_criteria:
      min_age: 18                      # 最小年龄
      max_age: 85                      # 最大年龄
      contraindications: []            # 禁忌症
    
    # 配置验证
    config_validation:
      required_parameters: ["sensitivity_by_state", "specificity"]
      optional_parameters: ["detection_mechanism", "special_parameters"]
      parameter_ranges:
        specificity: [0.0, 1.0]
        sensitivity: [0.0, 1.0]

# 元数据
metadata:
  created_date: "2025-08-03"
  last_modified: "2025-08-03"
  version_history:
    - version: "1.0"
      date: "2025-08-03"
      changes: "初始通用模板创建"
  
  # 使用说明
  usage_notes:
    - "这是一个通用的筛查工具配置模板"
    - "使用时需要根据具体工具调整参数"
    - "特别注意敏感性和特异性的设置"
    - "检测机制可以根据工具特点选择"
  
  # 配置来源和参考
  references:
    - "通用筛查工具设计指南"
    - "筛查工具性能评估标准"
  
  # 验证状态
  validation_status: "template"
  reviewed_by: null
  approved_by: null
