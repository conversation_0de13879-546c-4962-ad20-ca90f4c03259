"""
癌症发病建模系统单元测试

测试CancerIncidenceModel类的各项功能，包括发病概率计算、统计跟踪等。
"""

import pytest
import numpy as np
from unittest.mock import patch, MagicMock

from src.modules.disease.cancer_incidence import (
    CancerIncidenceModel,
    CancerIncidenceParameters,
    IncidenceStatistics
)


class TestCancerIncidenceParameters:
    """测试CancerIncidenceParameters数据类"""
    
    def test_valid_parameters(self):
        """测试有效参数创建"""
        params = CancerIncidenceParameters(
            base_incidence_rate=0.0001,
            peak_age=65.0,
            age_spread=10.0,
            min_age=30.0,
            max_age=100.0
        )
        
        assert params.base_incidence_rate == 0.0001
        assert params.peak_age == 65.0
        assert params.age_spread == 10.0
        assert params.min_age == 30.0
        assert params.max_age == 100.0
    
    def test_invalid_base_incidence_rate(self):
        """测试无效的基础发病率"""
        with pytest.raises(ValueError, match="基础发病率必须大于0"):
            CancerIncidenceParameters(
                base_incidence_rate=0.0,
                peak_age=65.0,
                age_spread=10.0
            )
    
    def test_invalid_peak_age(self):
        """测试无效的发病高峰年龄"""
        with pytest.raises(ValueError, match="发病高峰年龄必须大于0"):
            CancerIncidenceParameters(
                base_incidence_rate=0.0001,
                peak_age=0.0,
                age_spread=10.0
            )
    
    def test_invalid_age_spread(self):
        """测试无效的年龄分布宽度"""
        with pytest.raises(ValueError, match="年龄分布宽度必须大于0"):
            CancerIncidenceParameters(
                base_incidence_rate=0.0001,
                peak_age=65.0,
                age_spread=0.0
            )
    
    def test_invalid_age_range(self):
        """测试无效的年龄范围"""
        with pytest.raises(ValueError, match="最大发病年龄必须大于最小发病年龄"):
            CancerIncidenceParameters(
                base_incidence_rate=0.0001,
                peak_age=65.0,
                age_spread=10.0,
                min_age=80.0,
                max_age=60.0
            )


class TestIncidenceStatistics:
    """测试IncidenceStatistics数据类"""
    
    def test_initial_state(self):
        """测试初始状态"""
        stats = IncidenceStatistics(gender="male", location="proximal_colon")
        
        assert stats.gender == "male"
        assert stats.location == "proximal_colon"
        assert stats.total_incidences == 0
        assert stats.mean_age_at_incidence == 0.0
        assert len(stats.age_group_counts) == 7
    
    def test_update_incidence_single(self):
        """测试单次发病更新"""
        stats = IncidenceStatistics(gender="male", location="proximal_colon")
        stats.update_incidence(65.0)
        
        assert stats.total_incidences == 1
        assert stats.mean_age_at_incidence == 65.0
        assert stats.age_group_counts["60-69"] == 1
    
    def test_update_incidence_multiple(self):
        """测试多次发病更新"""
        stats = IncidenceStatistics(gender="male", location="proximal_colon")
        ages = [55.0, 65.0, 75.0]
        
        for age in ages:
            stats.update_incidence(age)
        
        assert stats.total_incidences == 3
        assert stats.mean_age_at_incidence == pytest.approx(65.0, rel=1e-2)
        assert stats.age_group_counts["50-59"] == 1
        assert stats.age_group_counts["60-69"] == 1
        assert stats.age_group_counts["70-79"] == 1
    
    def test_age_group_classification(self):
        """测试年龄组分类"""
        stats = IncidenceStatistics(gender="female", location="rectum")
        
        test_ages = [35, 45, 55, 65, 75, 85, 95]
        expected_groups = ["30-39", "40-49", "50-59", "60-69", "70-79", "80-89", "90+"]
        
        for age, expected_group in zip(test_ages, expected_groups):
            stats.update_incidence(age)
            assert stats.age_group_counts[expected_group] >= 1
    
    def test_get_age_distribution(self):
        """测试年龄分布计算"""
        stats = IncidenceStatistics(gender="male", location="distal_colon")
        
        # 添加一些发病案例
        for _ in range(2):
            stats.update_incidence(55.0)  # 50-59组
        for _ in range(3):
            stats.update_incidence(65.0)  # 60-69组
        
        distribution = stats.get_age_distribution()
        
        assert distribution["50-59"] == 0.4  # 2/5
        assert distribution["60-69"] == 0.6  # 3/5
        assert sum(distribution.values()) == pytest.approx(1.0, rel=1e-6)


class TestCancerIncidenceModel:
    """测试CancerIncidenceModel类"""
    
    def test_initialization_default(self):
        """测试默认初始化"""
        model = CancerIncidenceModel()
        
        # 检查默认性别和位置是否已初始化
        assert "male" in model.parameters
        assert "female" in model.parameters
        
        for gender in ["male", "female"]:
            for location in ["proximal_colon", "distal_colon", "rectum"]:
                assert location in model.parameters[gender]
                assert location in model.statistics[gender]
    
    def test_initialization_with_config(self):
        """测试使用配置初始化"""
        config = {
            "test_gender": {
                "test_location": {
                    "base_incidence_rate": 0.0002,
                    "peak_age": 70.0,
                    "age_spread": 15.0,
                    "min_age": 40.0,
                    "max_age": 90.0
                }
            }
        }
        
        model = CancerIncidenceModel(config=config)
        
        assert "test_gender" in model.parameters
        assert "test_location" in model.parameters["test_gender"]
        
        params = model.parameters["test_gender"]["test_location"]
        assert params.base_incidence_rate == 0.0002
        assert params.peak_age == 70.0
        assert params.age_spread == 15.0
    
    def test_calculate_cancer_incidence_probability(self):
        """测试癌症发病概率计算"""
        model = CancerIncidenceModel()
        
        # 测试在发病高峰年龄的概率
        prob_peak = model.calculate_cancer_incidence_probability(65.0, "male", "proximal_colon")
        
        # 测试在非高峰年龄的概率
        prob_young = model.calculate_cancer_incidence_probability(40.0, "male", "proximal_colon")
        prob_old = model.calculate_cancer_incidence_probability(90.0, "male", "proximal_colon")
        
        # 高峰年龄的概率应该最高
        assert prob_peak > prob_young
        assert prob_peak > prob_old
        
        # 所有概率都应该在[0, 1]范围内
        assert 0 <= prob_peak <= 1
        assert 0 <= prob_young <= 1
        assert 0 <= prob_old <= 1
    
    def test_calculate_cancer_incidence_probability_invalid_inputs(self):
        """测试无效输入的发病概率计算"""
        model = CancerIncidenceModel()
        
        # 测试无效性别
        with pytest.raises(ValueError, match="未知的性别"):
            model.calculate_cancer_incidence_probability(65.0, "invalid_gender", "proximal_colon")
        
        # 测试无效位置
        with pytest.raises(ValueError, match="未知的解剖位置"):
            model.calculate_cancer_incidence_probability(65.0, "male", "invalid_location")
    
    def test_sample_cancer_incidence(self):
        """测试癌症发病抽样"""
        model = CancerIncidenceModel()
        model.set_random_seed(42)
        
        # 进行多次抽样
        incidences = []
        for _ in range(1000):
            incidence = model.sample_cancer_incidence(65.0, "male", "proximal_colon")
            incidences.append(incidence)
        
        # 检查结果是布尔值
        assert all(isinstance(inc, bool) for inc in incidences)
        
        # 检查发病率是否合理（应该很低）
        incidence_rate = sum(incidences) / len(incidences)
        expected_prob = model.calculate_cancer_incidence_probability(65.0, "male", "proximal_colon")
        
        # 允许一定的统计误差
        assert abs(incidence_rate - expected_prob) < 0.01
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        model = CancerIncidenceModel()
        model.set_random_seed(42)
        
        # 模拟一些发病事件
        ages = [55, 60, 65, 70, 75]
        for age in ages:
            # 强制发病以测试统计跟踪
            model.statistics["male"]["proximal_colon"].update_incidence(age)
        
        stats = model.get_statistics("male", "proximal_colon")
        assert stats.total_incidences == 5
        assert stats.mean_age_at_incidence == 65.0
    
    def test_gender_and_location_differences(self):
        """测试性别和位置差异"""
        model = CancerIncidenceModel()
        
        age = 65.0
        
        # 获取不同性别和位置的发病概率
        male_proximal = model.calculate_cancer_incidence_probability(age, "male", "proximal_colon")
        male_distal = model.calculate_cancer_incidence_probability(age, "male", "distal_colon")
        male_rectum = model.calculate_cancer_incidence_probability(age, "male", "rectum")
        
        female_proximal = model.calculate_cancer_incidence_probability(age, "female", "proximal_colon")
        female_distal = model.calculate_cancer_incidence_probability(age, "female", "distal_colon")
        female_rectum = model.calculate_cancer_incidence_probability(age, "female", "rectum")
        
        # 检查是否存在差异（基于默认配置）
        assert male_proximal != female_proximal
        assert male_distal != female_distal
        assert male_rectum != female_rectum
        
        # 男性的发病率通常更高（基于默认配置）
        assert male_proximal > female_proximal
        assert male_distal > female_distal
        assert male_rectum > female_rectum
    
    def test_age_specific_incidence_curve(self):
        """测试年龄特异性发病率曲线"""
        model = CancerIncidenceModel()
        
        ages, rates = model.calculate_age_specific_incidence_curve(
            "male", "proximal_colon", age_range=(40, 80), age_step=5.0
        )
        
        assert len(ages) == len(rates)
        assert ages[0] == 40.0
        assert ages[-1] == 80.0
        
        # 检查发病率曲线的形状（应该在高峰年龄附近最高）
        peak_age = model.parameters["male"]["proximal_colon"].peak_age
        peak_index = min(range(len(ages)), key=lambda i: abs(ages[i] - peak_age))
        
        # 高峰附近的发病率应该相对较高
        assert rates[peak_index] >= max(rates[0], rates[-1])
    
    def test_lifetime_risk_estimation(self):
        """测试终生风险估算"""
        model = CancerIncidenceModel()
        
        lifetime_risk = model.estimate_lifetime_risk("male", "proximal_colon", 30.0, 85.0)
        
        # 终生风险应该在合理范围内
        assert 0 < lifetime_risk < 1.0
        
        # 更长的年龄范围应该有更高的终生风险
        shorter_risk = model.estimate_lifetime_risk("male", "proximal_colon", 50.0, 70.0)
        assert lifetime_risk > shorter_risk

    def test_parameter_updates(self):
        """测试参数更新"""
        model = CancerIncidenceModel()

        new_params = CancerIncidenceParameters(
            base_incidence_rate=0.0005,
            peak_age=70.0,
            age_spread=15.0,
            min_age=35.0,
            max_age=95.0
        )

        model.update_parameters("male", "proximal_colon", new_params)

        updated_params = model.get_parameters("male", "proximal_colon")
        assert updated_params.base_incidence_rate == 0.0005
        assert updated_params.peak_age == 70.0
        assert updated_params.age_spread == 15.0

    def test_statistics_reset(self):
        """测试统计信息重置"""
        model = CancerIncidenceModel()

        # 添加一些统计数据
        model.statistics["male"]["proximal_colon"].update_incidence(65.0)
        model.statistics["female"]["rectum"].update_incidence(70.0)

        # 重置特定组合的统计
        model.reset_statistics("male", "proximal_colon")
        assert model.statistics["male"]["proximal_colon"].total_incidences == 0
        assert model.statistics["female"]["rectum"].total_incidences == 1

        # 重置特定性别的所有统计
        model.reset_statistics("female")
        assert model.statistics["female"]["rectum"].total_incidences == 0

        # 重置所有统计
        model.statistics["male"]["distal_colon"].update_incidence(60.0)
        model.reset_statistics()

        all_stats = model.get_all_statistics()
        for gender_stats in all_stats.values():
            for location_stats in gender_stats.values():
                assert location_stats["total_incidences"] == 0

    def test_validate_parameters(self):
        """测试参数验证"""
        model = CancerIncidenceModel()

        # 默认参数应该都是有效的
        validation_results = model.validate_parameters()
        assert len(validation_results) == 0

        # 手动创建无效参数（绕过__post_init__验证）
        invalid_params = CancerIncidenceParameters.__new__(CancerIncidenceParameters)
        invalid_params.base_incidence_rate = 1.5  # 超过1.0
        invalid_params.peak_age = 25.0           # 太年轻
        invalid_params.age_spread = 2.0          # 太窄
        invalid_params.min_age = 0.0             # 有效的最小年龄
        invalid_params.max_age = 100.0           # 有效的最大年龄

        model.update_parameters("test_gender", "test_location", invalid_params)
        validation_results = model.validate_parameters()

        assert "test_gender-test_location" in validation_results
        errors = validation_results["test_gender-test_location"]
        assert any("基础发病率不能超过1.0" in error for error in errors)
        assert any("发病高峰年龄应在40-90岁之间" in error for error in errors)
        assert any("年龄分布宽度应在5-30年之间" in error for error in errors)

    def test_export_configuration(self):
        """测试配置导出"""
        model = CancerIncidenceModel()

        config = model.export_configuration()

        # 检查导出的配置包含所有性别和位置
        assert "male" in config
        assert "female" in config

        for gender in ["male", "female"]:
            for location in ["proximal_colon", "distal_colon", "rectum"]:
                assert location in config[gender]
                assert "base_incidence_rate" in config[gender][location]
                assert "peak_age" in config[gender][location]
                assert "age_spread" in config[gender][location]

    def test_get_supported_combinations(self):
        """测试获取支持的组合"""
        model = CancerIncidenceModel()

        combinations = model.get_supported_combinations()

        expected_combinations = [
            ("male", "proximal_colon"), ("male", "distal_colon"), ("male", "rectum"),
            ("female", "proximal_colon"), ("female", "distal_colon"), ("female", "rectum")
        ]

        for combo in expected_combinations:
            assert combo in combinations

    def test_random_seed_reproducibility(self):
        """测试随机数种子的可重现性"""
        model1 = CancerIncidenceModel()
        model2 = CancerIncidenceModel()

        model1.set_random_seed(123)
        model2.set_random_seed(123)

        # 使用相同种子应该产生相同结果
        results1 = []
        results2 = []

        for _ in range(100):
            result1 = model1.sample_cancer_incidence(65.0, "male", "proximal_colon")
            result2 = model2.sample_cancer_incidence(65.0, "male", "proximal_colon")
            results1.append(result1)
            results2.append(result2)

        assert results1 == results2

    def test_edge_cases(self):
        """测试边界情况"""
        model = CancerIncidenceModel()

        # 测试极端年龄
        prob_very_young = model.calculate_cancer_incidence_probability(0.0, "male", "proximal_colon")
        prob_very_old = model.calculate_cancer_incidence_probability(150.0, "male", "proximal_colon")

        # 概率应该都在有效范围内
        assert 0 <= prob_very_young <= 1
        assert 0 <= prob_very_old <= 1

        # 极端年龄的概率应该很低
        assert prob_very_young < 0.001
        assert prob_very_old < 0.001

    def test_configuration_loading_edge_cases(self):
        """测试配置加载的边界情况"""
        # 测试空配置
        model = CancerIncidenceModel(config={})
        assert len(model.parameters) > 0  # 应该有默认参数

        # 测试无效配置格式
        invalid_config = {
            "male": "invalid_format"  # 应该是字典
        }
        model = CancerIncidenceModel(config=invalid_config)
        # 应该仍然有默认的male参数
        assert "male" in model.parameters

        # 测试缺少必需参数的配置
        incomplete_config = {
            "test_gender": {
                "test_location": {
                    "base_incidence_rate": 0.0001,
                    "peak_age": 65.0
                    # 缺少 age_spread 参数
                }
            }
        }
        model = CancerIncidenceModel(config=incomplete_config)
        # 不完整的配置应该被忽略
        assert "test_gender" not in model.parameters or "test_location" not in model.parameters.get("test_gender", {})

    def test_statistical_properties(self):
        """测试统计特性"""
        model = CancerIncidenceModel()
        model.set_random_seed(42)

        # 测试大量抽样的统计特性
        age = 65.0
        gender = "male"
        location = "proximal_colon"

        expected_prob = model.calculate_cancer_incidence_probability(age, gender, location)

        # 进行大量抽样
        num_samples = 10000
        incidences = 0
        for _ in range(num_samples):
            if model.sample_cancer_incidence(age, gender, location):
                incidences += 1

        observed_prob = incidences / num_samples

        # 观察到的概率应该接近期望概率
        assert abs(observed_prob - expected_prob) < 0.01
