"""
成本建模系统集成测试
"""

import pytest
import tempfile
import os
from pathlib import Path

from src.modules.economics import (
    ScreeningCostModel,
    TreatmentCostModel,
    CostAdjustmentEngine,
    SensitivityAnalyzer
)
from src.modules.economics.screening_costs import ScreeningTool
from src.modules.economics.treatment_costs import CancerStage, TreatmentModality
from src.modules.economics.sensitivity_analysis import ParameterType, DistributionType


class TestCostModelingIntegration:
    """成本建模系统集成测试"""
    
    @pytest.fixture
    def screening_model(self):
        """创建筛查成本模型"""
        config_file = "data/economic_parameters/screening_costs_china_2023.yaml"
        if os.path.exists(config_file):
            return ScreeningCostModel(config_file)
        else:
            # 如果配置文件不存在，创建基本模型
            model = ScreeningCostModel()
            return model
    
    @pytest.fixture
    def treatment_model(self):
        """创建治疗成本模型"""
        config_file = "data/economic_parameters/treatment_costs_china_2023.yaml"
        if os.path.exists(config_file):
            return TreatmentCostModel(config_file)
        else:
            # 如果配置文件不存在，创建基本模型
            model = TreatmentCostModel()
            return model
    
    @pytest.fixture
    def adjustment_engine(self):
        """创建成本调整引擎"""
        return CostAdjustmentEngine(base_year=2023, discount_rate=0.03)
    
    def test_screening_cost_calculation(self, screening_model):
        """测试筛查成本计算"""
        if ScreeningTool.FIT in screening_model.cost_configs:
            # 测试基本成本计算
            cost = screening_model.get_screening_cost(ScreeningTool.FIT)
            assert cost > 0
            
            # 测试批量计算
            requests = [
                {'tool': ScreeningTool.FIT, 'quantity': 100},
                {'tool': ScreeningTool.COLONOSCOPY, 'quantity': 50}
            ]
            
            if ScreeningTool.COLONOSCOPY in screening_model.cost_configs:
                results = screening_model.calculate_batch_costs(requests)
                assert results['total_cost'] > 0
                assert results['summary']['total_screenings'] == 150
    
    def test_treatment_cost_calculation(self, treatment_model):
        """测试治疗成本计算"""
        if CancerStage.STAGE_I in treatment_model.stage_specific_costs:
            # 测试基本治疗成本
            cost = treatment_model.calculate_treatment_cost(CancerStage.STAGE_I)
            assert cost > 0
            
            # 测试终生治疗成本
            lifetime_cost = treatment_model.calculate_lifetime_treatment_cost(
                CancerStage.STAGE_I, 
                diagnosis_age=60, 
                life_expectancy=80
            )
            assert lifetime_cost['total_lifetime_cost'] > cost
    
    def test_cost_adjustment_integration(self, adjustment_engine):
        """测试成本调整集成"""
        # 测试通胀调整
        cost_2020 = 1000.0
        adjusted_cost = adjustment_engine.adjust_for_inflation(cost_2020, 2020, 2023)
        assert adjusted_cost > cost_2020  # 应该因通胀而增加
        
        # 测试现值计算
        future_cost = 1000.0
        present_value = adjustment_engine.present_value(future_cost, 5)
        assert present_value < future_cost  # 现值应该小于未来值
        
        # 测试净现值计算
        cost_stream = [
            (1000.0, 2023),
            (1100.0, 2024),
            (1200.0, 2025)
        ]
        npv = adjustment_engine.net_present_value(cost_stream)
        assert npv > 0
    
    def test_sensitivity_analysis_integration(self, screening_model, treatment_model):
        """测试敏感性分析集成"""
        def combined_cost_model(params):
            """组合成本模型"""
            screening_cost = params.get('screening_cost', 100.0)
            treatment_cost = params.get('treatment_cost', 5000.0)
            screening_quantity = params.get('screening_quantity', 1000)
            treatment_probability = params.get('treatment_probability', 0.1)
            
            total_screening = screening_cost * screening_quantity
            expected_treatment = treatment_cost * treatment_probability * screening_quantity
            
            return total_screening + expected_treatment
        
        # 创建敏感性分析器
        analyzer = SensitivityAnalyzer(combined_cost_model)
        
        # 添加参数
        analyzer.add_parameter('screening_cost', 100.0, ParameterType.COST)
        analyzer.add_parameter('treatment_cost', 5000.0, ParameterType.COST)
        analyzer.add_parameter('screening_quantity', 1000, ParameterType.DURATION)
        analyzer.add_parameter('treatment_probability', 0.1, ParameterType.PROBABILITY)
        
        # 单因素敏感性分析
        result = analyzer.one_way_sensitivity_analysis('screening_cost')
        assert result.parameter_name == 'screening_cost'
        assert len(result.parameter_values) > 0
        assert len(result.outcome_values) > 0
        
        # 龙卷风图分析
        tornado_result = analyzer.tornado_analysis()
        assert 'tornado_data' in tornado_result
        assert len(tornado_result['tornado_data']) == 4  # 4个参数
    
    def test_full_workflow_integration(self, screening_model, treatment_model, adjustment_engine):
        """测试完整工作流程集成"""
        # 1. 计算筛查成本
        if ScreeningTool.FIT in screening_model.cost_configs:
            screening_cost_2023 = screening_model.get_screening_cost(ScreeningTool.FIT, quantity=1000)
        else:
            screening_cost_2023 = 50000.0  # 假设值
        
        # 2. 计算治疗成本
        if CancerStage.STAGE_I in treatment_model.stage_specific_costs:
            treatment_cost_2023 = treatment_model.calculate_treatment_cost(CancerStage.STAGE_I)
        else:
            treatment_cost_2023 = 50000.0  # 假设值
        
        # 3. 调整到基准年
        adjusted_screening = adjustment_engine.adjust_cost_to_base_year(screening_cost_2023, 2023)
        adjusted_treatment = adjustment_engine.adjust_cost_to_base_year(treatment_cost_2023, 2023)
        
        # 4. 计算总成本现值
        total_cost_stream = [
            (adjusted_screening, 2023),
            (adjusted_treatment, 2024)
        ]
        
        total_npv = adjustment_engine.net_present_value(total_cost_stream)
        
        # 验证结果
        assert adjusted_screening > 0
        assert adjusted_treatment > 0
        assert total_npv > 0
        assert total_npv < (adjusted_screening + adjusted_treatment)  # NPV应该小于简单相加
    
    def test_data_validation_integration(self, screening_model, treatment_model):
        """测试数据验证集成"""
        # 验证筛查成本配置
        if screening_model.cost_configs:
            for tool in screening_model.get_available_tools():
                validation_result = screening_model.validate_cost_config(tool)
                assert 'valid' in validation_result
                assert 'errors' in validation_result
                assert 'warnings' in validation_result
        
        # 验证治疗成本配置
        if treatment_model.stage_specific_costs:
            for stage in treatment_model.get_available_stages():
                validation_result = treatment_model.validate_treatment_config(stage)
                assert 'valid' in validation_result
                assert 'errors' in validation_result
                assert 'warnings' in validation_result
    
    def test_monte_carlo_with_real_models(self, screening_model, treatment_model, adjustment_engine):
        """测试使用真实模型的蒙特卡洛分析"""
        def healthcare_cost_model(params):
            """医疗成本模型"""
            # 筛查成本
            screening_unit_cost = params.get('screening_unit_cost', 50.0)
            screening_population = params.get('screening_population', 10000)
            screening_compliance = params.get('screening_compliance', 0.7)
            
            # 治疗成本
            treatment_unit_cost = params.get('treatment_unit_cost', 50000.0)
            detection_rate = params.get('detection_rate', 0.05)
            
            # 折现参数
            discount_rate = params.get('discount_rate', 0.03)
            
            # 计算成本
            total_screening_cost = screening_unit_cost * screening_population * screening_compliance
            detected_cases = screening_population * screening_compliance * detection_rate
            total_treatment_cost = detected_cases * treatment_unit_cost
            
            # 简化的现值计算（假设治疗在第二年）
            pv_treatment = total_treatment_cost / (1 + discount_rate)
            
            return total_screening_cost + pv_treatment
        
        # 创建敏感性分析器
        analyzer = SensitivityAnalyzer(healthcare_cost_model)
        
        # 添加带分布的参数
        analyzer.add_parameter(
            'screening_unit_cost', 50.0, ParameterType.COST,
            DistributionType.UNIFORM, {'low': 40.0, 'high': 60.0}
        )
        analyzer.add_parameter(
            'treatment_unit_cost', 50000.0, ParameterType.COST,
            DistributionType.NORMAL, {'mean': 50000.0, 'std': 5000.0}
        )
        analyzer.add_parameter(
            'screening_compliance', 0.7, ParameterType.PROBABILITY,
            DistributionType.BETA, {'alpha': 7, 'beta': 3, 'scale': 1.0}
        )
        analyzer.add_parameter(
            'detection_rate', 0.05, ParameterType.PROBABILITY,
            DistributionType.UNIFORM, {'low': 0.03, 'high': 0.07}
        )
        
        # 运行蒙特卡洛模拟
        mc_result = analyzer.monte_carlo_simulation(n_simulations=100, seed=42)
        
        # 验证结果
        assert mc_result['n_simulations'] == 100
        assert len(mc_result['outcomes']) == 100
        assert 'statistics' in mc_result
        assert 'correlations' in mc_result
        
        # 验证统计结果合理性
        stats = mc_result['statistics']
        assert stats['mean'] > 0
        assert stats['std'] > 0
        assert stats['min'] < stats['mean'] < stats['max']
        
        # 验证置信区间
        ci = stats['confidence_interval']
        assert len(ci) == 2
        assert ci[0] < stats['mean'] < ci[1]
