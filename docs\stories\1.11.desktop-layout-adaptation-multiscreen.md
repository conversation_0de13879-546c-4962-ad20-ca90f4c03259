# Story 1.11: 桌面布局适配和多屏支持

## Status

Draft

## Story

**As a** 使用不同屏幕尺寸和多显示器设置的研究人员，
**I want** 拥有自适应的桌面布局和完整的多屏幕支持功能，
**so that** 能够在各种显示环境下获得最佳的用户体验和工作效率。

## Acceptance Criteria

1. 实现多屏幕尺寸适配（1366x768、1920x1080、2560x1440+）
2. 创建可折叠侧边栏和紧凑布局（小屏幕优化）
3. 实现多显示器支持（窗口拖拽、跨屏幕显示）
4. 添加窗口位置记忆和布局状态持久化
5. 创建自适应工具栏和菜单（图标/文字模式切换）
6. 实现响应式面板布局（QSplitter动态调整）
7. 添加全屏模式和演示模式支持
8. 创建显示器分辨率自适应和DPI缩放
9. 实现用户自定义布局保存和恢复
10. 确保符合ai-frontend-prompts.md的桌面布局适配标准

## Tasks / Subtasks

- [ ] 任务1：实现多屏幕尺寸适配 (AC: 1)
  - [ ] 创建src/interfaces/desktop/layout/adaptive_layout_manager.py
  - [ ] 实现AdaptiveLayoutManager自适应布局管理器
  - [ ] 添加屏幕尺寸检测（QApplication.primaryScreen().size()）
  - [ ] 创建小屏幕布局（1366x768：紧凑模式）
  - [ ] 实现标准屏幕布局（1920x1080：标准模式）
  - [ ] 添加大屏幕布局（2560x1440+：扩展模式）
  - [ ] 创建布局切换动画效果
  - [ ] 实现最小窗口尺寸限制

- [ ] 任务2：创建可折叠侧边栏和紧凑布局 (AC: 2)
  - [ ] 创建src/interfaces/desktop/widgets/collapsible_sidebar.py
  - [ ] 实现CollapsibleSidebar可折叠侧边栏
  - [ ] 添加侧边栏折叠/展开动画（QPropertyAnimation）
  - [ ] 创建紧凑模式工具栏（仅图标显示）
  - [ ] 实现简化的图表显示（隐藏次要信息）
  - [ ] 添加标签页式内容组织（QTabWidget）
  - [ ] 创建滚动区域处理内容溢出
  - [ ] 实现快速切换按钮

- [ ] 任务3：实现多显示器支持 (AC: 3)
  - [ ] 创建src/interfaces/desktop/layout/multi_monitor_manager.py
  - [ ] 实现MultiMonitorManager多显示器管理器
  - [ ] 添加显示器检测和枚举（QScreen API）
  - [ ] 创建窗口跨屏幕拖拽功能
  - [ ] 实现分屏显示模式（主窗口+分析窗口）
  - [ ] 添加显示器配置检测和适配
  - [ ] 创建多显示器布局模板
  - [ ] 实现显示器热插拔处理

- [ ] 任务4：添加窗口位置记忆和状态持久化 (AC: 4)
  - [ ] 创建src/interfaces/desktop/layout/window_state_manager.py
  - [ ] 实现WindowStateManager窗口状态管理器
  - [ ] 添加窗口位置保存（QSettings）
  - [ ] 创建窗口大小和状态记忆
  - [ ] 实现分割器位置持久化
  - [ ] 添加多显示器配置保存
  - [ ] 创建布局配置导入导出
  - [ ] 实现启动时状态恢复

- [ ] 任务5：创建自适应工具栏和菜单 (AC: 5)
  - [ ] 创建src/interfaces/desktop/widgets/adaptive_toolbar.py
  - [ ] 实现AdaptiveToolbar自适应工具栏
  - [ ] 添加图标模式（Qt.ToolButtonIconOnly）
  - [ ] 创建文字模式（Qt.ToolButtonTextBesideIcon）
  - [ ] 实现自动模式切换（基于可用空间）
  - [ ] 添加工具栏溢出处理（下拉菜单）
  - [ ] 创建上下文相关的工具栏
  - [ ] 实现工具栏自定义功能

- [ ] 任务6：实现响应式面板布局 (AC: 6)
  - [ ] 创建src/interfaces/desktop/layout/responsive_panel_layout.py
  - [ ] 实现ResponsivePanelLayout响应式面板布局
  - [ ] 添加QSplitter动态调整
  - [ ] 创建面板最小/最大尺寸约束
  - [ ] 实现面板自动隐藏/显示
  - [ ] 添加面板停靠和浮动功能（QDockWidget）
  - [ ] 创建面板布局预设
  - [ ] 实现面板拖拽重排

- [ ] 任务7：添加全屏模式和演示模式 (AC: 7)
  - [ ] 创建src/interfaces/desktop/layout/presentation_mode.py
  - [ ] 实现PresentationMode演示模式管理器
  - [ ] 添加全屏模式切换（F11快捷键）
  - [ ] 创建演示模式（隐藏工具栏、简化界面）
  - [ ] 实现多显示器全屏支持
  - [ ] 添加演示模式工具栏（最小化控制）
  - [ ] 创建演示模式快捷键
  - [ ] 实现演示模式退出机制

- [ ] 任务8：创建显示器分辨率自适应和DPI缩放 (AC: 8)
  - [ ] 创建src/interfaces/desktop/layout/dpi_scaling_manager.py
  - [ ] 实现DPIScalingManager DPI缩放管理器
  - [ ] 添加高DPI显示器检测
  - [ ] 创建自动DPI缩放（Qt.AA_EnableHighDpiScaling）
  - [ ] 实现字体大小自适应
  - [ ] 添加图标和图片缩放
  - [ ] 创建混合DPI环境支持
  - [ ] 实现DPI变化实时响应

- [ ] 任务9：实现用户自定义布局 (AC: 9)
  - [ ] 创建src/interfaces/desktop/layout/custom_layout_manager.py
  - [ ] 实现CustomLayoutManager自定义布局管理器
  - [ ] 添加布局配置编辑器
  - [ ] 创建布局模板保存功能
  - [ ] 实现布局快速切换
  - [ ] 添加布局分享和导入
  - [ ] 创建布局重置功能
  - [ ] 实现布局版本管理

## Dev Notes

### 桌面布局架构 [Source: docs/ai-frontend-prompts.md]

**屏幕尺寸策略**：
- 小屏幕：1366x768（紧凑布局）
- 标准屏幕：1920x1080（标准布局）
- 大屏幕：2560x1440+（扩展布局）
- 多显示器：支持跨屏幕拖拽

**布局适配规则**：

**小屏幕优化（1366x768）**：
- 可折叠侧边栏（QSplitter）
- 紧凑的工具栏
- 简化的图表显示（隐藏次要信息）
- 标签页式内容组织
- 滚动区域处理内容溢出
- 最小窗口尺寸限制

**标准屏幕适配（1920x1080）**：
- 固定侧边导航（QDockWidget）
- 多列数据展示（QTableWidget）
- 完整功能的图表
- 键盘快捷键支持（QShortcut）
- 悬停效果和工具提示（QToolTip）
- 右键上下文菜单（QMenu）

**大屏幕增强（2560x1440+）**：
- 并排比较视图（QSplitter）
- 多面板同时显示（QMdiArea）
- 扩展的数据表格（更多列）
- 高密度信息展示
- 分屏功能（多窗口）
- 多显示器支持

### 技术实现架构 [Source: architecture/frontend-architecture.md]

**PyQt布局管理**：
```python
class AdaptiveMainWindow(QMainWindow):
    def setupUI(self):
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen()
        screen_size = screen.size()
        
        # 根据屏幕尺寸调整布局
        if screen_size.width() < 1400:
            self.setupCompactLayout()
        elif screen_size.width() < 1920:
            self.setupStandardLayout()
        else:
            self.setupExpandedLayout()
```

**核心技术组件**：
- QSplitter实现可调整布局
- QDockWidget支持面板拖拽
- QSettings保存布局状态
- QShortcut键盘导航优化
- QScreen监控显示器变化

### 多显示器支持 [Source: docs/ai-frontend-prompts.md]

**多显示器功能**：
- 窗口拖拽到不同显示器
- 记住窗口位置（QSettings）
- 全屏模式支持
- 显示器分辨率自适应

**技术实现**：
- QApplication.screens()获取所有显示器
- QWindow.setScreen()设置窗口显示器
- QScreen.availableGeometry()获取可用区域
- QScreen.devicePixelRatio()处理DPI缩放

### 性能和用户体验

**响应式设计**：
- 布局切换动画效果（QPropertyAnimation）
- 面板大小约束和自动调整
- 实时布局状态保存
- 快速布局切换功能

**用户体验优化**：
- 直观的布局控制界面
- 布局预设和模板
- 一键重置和恢复
- 布局分享和导入

### 项目结构对齐 [Source: architecture/unified-project-structure.md]

**文件位置**：
- 布局管理：src/interfaces/desktop/layout/
- 自适应组件：src/interfaces/desktop/widgets/adaptive_*
- 布局服务：src/interfaces/desktop/services/layout_*
- 配置存储：src/interfaces/desktop/utils/settings.py

## Testing

### 测试文件位置 [Source: architecture/unified-project-structure.md]

- `tests/unit/layout/test_adaptive_layout_manager.py`
- `tests/unit/layout/test_multi_monitor_manager.py`
- `tests/unit/layout/test_window_state_manager.py`
- `tests/unit/widgets/test_collapsible_sidebar.py`
- `tests/unit/widgets/test_adaptive_toolbar.py`
- `tests/integration/test_layout_adaptation.py`
- `tests/ui/test_multi_screen_scenarios.py`

### 测试标准

- 多屏幕尺寸适配测试（1366x768、1920x1080、2560x1440+）
- 多显示器配置测试（双屏、三屏、混合DPI）
- 窗口状态持久化测试
- 布局切换性能测试
- 用户自定义布局功能测试
- DPI缩放准确性测试
- 全屏和演示模式测试

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-31 | 1.0 | 初始故事创建，基于ai-frontend-prompts.md分析 | Bob (SM) |

## Dev Agent Record

*此部分将由开发代理在实施过程中填充*

### Agent Model Used

*待填充*

### Debug Log References

*待填充*

### Completion Notes List

*待填充*

### File List

*待填充*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填充*
