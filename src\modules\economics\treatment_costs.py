"""
治疗成本模型系统

实现癌症治疗成本的建模、计算和管理功能，支持分期特异性成本和治疗方案成本配置。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union
from enum import Enum
import logging
import yaml
import json
from pathlib import Path

# 导入统一的癌症分期枚举
from ...core.enums import CancerStage

logger = logging.getLogger(__name__)


class TreatmentModality(Enum):
    """治疗方式"""
    SURGERY = "surgery"  # 手术
    CHEMOTHERAPY = "chemotherapy"  # 化疗
    RADIOTHERAPY = "radiotherapy"  # 放疗
    TARGETED_THERAPY = "targeted_therapy"  # 靶向治疗
    IMMUNOTHERAPY = "immunotherapy"  # 免疫治疗
    PALLIATIVE_CARE = "palliative_care"  # 姑息治疗
    POLYPECTOMY = "polypectomy"  # 息肉切除


class ComplicationType(Enum):
    """并发症类型"""
    SURGICAL_COMPLICATIONS = "surgical_complications"
    CHEMOTHERAPY_TOXICITY = "chemotherapy_toxicity"
    RADIATION_TOXICITY = "radiation_toxicity"
    INFECTION = "infection"
    BLEEDING = "bleeding"
    PERFORATION = "perforation"
    ANASTOMOTIC_LEAK = "anastomotic_leak"


@dataclass
class TreatmentCost:
    """治疗成本结构"""
    initial_cost: float = 0.0  # 初始治疗成本
    monthly_cost: float = 0.0  # 月度治疗成本
    followup_cost: float = 0.0  # 随访成本
    complication_cost: float = 0.0  # 并发症成本
    
    @property
    def total_cost(self) -> float:
        """计算总成本"""
        return self.initial_cost + self.monthly_cost + self.followup_cost + self.complication_cost


@dataclass
class TreatmentProtocol:
    """治疗方案"""
    stage: CancerStage
    modalities: List[TreatmentModality]
    duration_months: float
    costs: TreatmentCost
    success_rate: float = 1.0
    complication_rates: Dict[ComplicationType, float] = field(default_factory=dict)
    recurrence_rate: float = 0.0


@dataclass
class ComplicationCost:
    """并发症成本"""
    complication_type: ComplicationType
    treatment_cost: float
    duration_days: float = 1.0
    probability: float = 0.0


class TreatmentCostModel:
    """治疗成本模型类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化治疗成本模型
        
        Args:
            config_file: 成本配置文件路径
        """
        self.treatment_protocols: Dict[CancerStage, List[TreatmentProtocol]] = {}
        self.complication_costs: Dict[ComplicationType, ComplicationCost] = {}
        self.stage_specific_costs: Dict[CancerStage, TreatmentCost] = {}
        self.modality_costs: Dict[TreatmentModality, float] = {}
        self.default_currency = "CNY"
        self.default_base_year = 2023
        
        if config_file:
            self.load_treatment_config(config_file)
    
    def load_treatment_config(self, config_file: str) -> None:
        """
        加载治疗成本配置文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            config_path = Path(config_file)
            
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            self._parse_treatment_config(config_data)
            logger.info(f"成功加载治疗成本配置: {config_file}")
            
        except Exception as e:
            logger.error(f"加载治疗成本配置失败: {e}")
            raise
    
    def _parse_treatment_config(self, config_data: Dict) -> None:
        """解析治疗配置数据"""
        treatment_costs = config_data.get('treatment_costs', {})
        
        # 解析分期特异性成本
        stage_costs = treatment_costs.get('stage_specific_costs', {})
        for stage_name, cost_data in stage_costs.items():
            try:
                stage = CancerStage(stage_name)
                self.stage_specific_costs[stage] = TreatmentCost(
                    initial_cost=cost_data.get('initial_cost', 0.0),
                    monthly_cost=cost_data.get('monthly_cost', 0.0),
                    followup_cost=cost_data.get('followup_cost', 0.0),
                    complication_cost=cost_data.get('complication_cost', 0.0)
                )
            except ValueError:
                logger.warning(f"跳过未知的癌症分期: {stage_name}")
                continue
        
        # 解析治疗方式成本
        modality_costs = treatment_costs.get('modality_costs', {})
        for modality_name, cost in modality_costs.items():
            try:
                modality = TreatmentModality(modality_name)
                self.modality_costs[modality] = cost
            except ValueError:
                logger.warning(f"跳过未知的治疗方式: {modality_name}")
                continue
        
        # 解析并发症成本
        complication_costs = treatment_costs.get('complication_costs', {})
        for comp_name, comp_data in complication_costs.items():
            try:
                comp_type = ComplicationType(comp_name)
                self.complication_costs[comp_type] = ComplicationCost(
                    complication_type=comp_type,
                    treatment_cost=comp_data.get('treatment_cost', 0.0),
                    duration_days=comp_data.get('duration_days', 1.0),
                    probability=comp_data.get('probability', 0.0)
                )
            except ValueError:
                logger.warning(f"跳过未知的并发症类型: {comp_name}")
                continue
    
    def calculate_treatment_cost(
        self,
        cancer_stage: CancerStage,
        treatment_duration_months: float = 12.0,
        complications: Optional[List[ComplicationType]] = None,
        treatment_modalities: Optional[List[TreatmentModality]] = None
    ) -> float:
        """
        计算治疗总成本
        
        Args:
            cancer_stage: 癌症分期
            treatment_duration_months: 治疗持续时间（月）
            complications: 并发症列表
            treatment_modalities: 治疗方式列表
            
        Returns:
            治疗总成本
        """
        total_cost = 0.0
        
        # 基础分期成本
        if cancer_stage in self.stage_specific_costs:
            stage_cost = self.stage_specific_costs[cancer_stage]
            total_cost += stage_cost.initial_cost
            total_cost += stage_cost.monthly_cost * treatment_duration_months
            total_cost += stage_cost.followup_cost
        
        # 治疗方式成本
        if treatment_modalities:
            for modality in treatment_modalities:
                if modality in self.modality_costs:
                    total_cost += self.modality_costs[modality]
        
        # 并发症成本
        if complications:
            for complication in complications:
                if complication in self.complication_costs:
                    comp_cost = self.complication_costs[complication]
                    total_cost += comp_cost.treatment_cost
        
        return total_cost
    
    def calculate_lifetime_treatment_cost(
        self,
        cancer_stage: CancerStage,
        diagnosis_age: int,
        life_expectancy: int,
        recurrence_probability: float = 0.0
    ) -> Dict:
        """
        计算终生治疗成本
        
        Args:
            cancer_stage: 癌症分期
            diagnosis_age: 诊断年龄
            life_expectancy: 预期寿命
            recurrence_probability: 复发概率
            
        Returns:
            终生治疗成本详情
        """
        result = {
            'initial_treatment_cost': 0.0,
            'followup_cost': 0.0,
            'recurrence_cost': 0.0,
            'total_lifetime_cost': 0.0,
            'years_of_followup': 0
        }
        
        # 初始治疗成本
        initial_cost = self.calculate_treatment_cost(cancer_stage, 12.0)
        result['initial_treatment_cost'] = initial_cost
        
        # 随访年数
        followup_years = max(0, life_expectancy - diagnosis_age)
        result['years_of_followup'] = followup_years
        
        # 年度随访成本
        if cancer_stage in self.stage_specific_costs:
            annual_followup = self.stage_specific_costs[cancer_stage].followup_cost
            result['followup_cost'] = annual_followup * followup_years
        
        # 复发治疗成本
        if recurrence_probability > 0:
            recurrence_cost = initial_cost * 0.8  # 假设复发治疗成本为初始治疗的80%
            result['recurrence_cost'] = recurrence_cost * recurrence_probability
        
        # 总终生成本
        result['total_lifetime_cost'] = (
            result['initial_treatment_cost'] + 
            result['followup_cost'] + 
            result['recurrence_cost']
        )
        
        return result

    def get_treatment_protocol(
        self,
        cancer_stage: CancerStage,
        patient_age: int = 65,
        comorbidities: Optional[List[str]] = None
    ) -> Optional[TreatmentProtocol]:
        """
        获取推荐治疗方案

        Args:
            cancer_stage: 癌症分期
            patient_age: 患者年龄
            comorbidities: 合并症列表

        Returns:
            推荐的治疗方案
        """
        if cancer_stage not in self.treatment_protocols:
            return None

        protocols = self.treatment_protocols[cancer_stage]

        # 简单的方案选择逻辑（可以根据需要扩展）
        if not protocols:
            return None

        # 根据年龄和合并症选择合适的方案
        suitable_protocols = []
        for protocol in protocols:
            # 年龄考虑：高龄患者可能不适合激进治疗
            if patient_age > 80 and TreatmentModality.CHEMOTHERAPY in protocol.modalities:
                continue

            # 合并症考虑（简化逻辑）
            if comorbidities and len(comorbidities) > 2:
                if TreatmentModality.SURGERY in protocol.modalities:
                    continue

            suitable_protocols.append(protocol)

        # 返回成功率最高的方案
        if suitable_protocols:
            return max(suitable_protocols, key=lambda p: p.success_rate)
        else:
            return protocols[0]  # 返回默认方案

    def calculate_expected_complication_cost(
        self,
        cancer_stage: CancerStage,
        treatment_modalities: List[TreatmentModality]
    ) -> float:
        """
        计算预期并发症成本

        Args:
            cancer_stage: 癌症分期
            treatment_modalities: 治疗方式列表

        Returns:
            预期并发症成本
        """
        expected_cost = 0.0

        # 根据治疗方式确定可能的并发症
        possible_complications = []

        if TreatmentModality.SURGERY in treatment_modalities:
            possible_complications.extend([
                ComplicationType.SURGICAL_COMPLICATIONS,
                ComplicationType.INFECTION,
                ComplicationType.BLEEDING,
                ComplicationType.ANASTOMOTIC_LEAK
            ])

        if TreatmentModality.CHEMOTHERAPY in treatment_modalities:
            possible_complications.append(ComplicationType.CHEMOTHERAPY_TOXICITY)

        if TreatmentModality.RADIOTHERAPY in treatment_modalities:
            possible_complications.append(ComplicationType.RADIATION_TOXICITY)

        # 计算预期成本
        for complication in possible_complications:
            if complication in self.complication_costs:
                comp_cost = self.complication_costs[complication]
                expected_cost += comp_cost.treatment_cost * comp_cost.probability

        return expected_cost

    def add_treatment_protocol(
        self,
        cancer_stage: CancerStage,
        protocol: TreatmentProtocol
    ) -> None:
        """
        添加治疗方案

        Args:
            cancer_stage: 癌症分期
            protocol: 治疗方案
        """
        if cancer_stage not in self.treatment_protocols:
            self.treatment_protocols[cancer_stage] = []

        self.treatment_protocols[cancer_stage].append(protocol)
        logger.info(f"添加治疗方案: {cancer_stage.value}")

    def update_stage_cost(
        self,
        cancer_stage: CancerStage,
        treatment_cost: TreatmentCost
    ) -> None:
        """
        更新分期特异性成本

        Args:
            cancer_stage: 癌症分期
            treatment_cost: 治疗成本
        """
        self.stage_specific_costs[cancer_stage] = treatment_cost
        logger.info(f"更新分期成本: {cancer_stage.value}")

    def get_available_stages(self) -> List[CancerStage]:
        """获取可用的癌症分期列表"""
        return list(self.stage_specific_costs.keys())

    def get_available_modalities(self) -> List[TreatmentModality]:
        """获取可用的治疗方式列表"""
        return list(self.modality_costs.keys())

    def validate_treatment_config(self, cancer_stage: CancerStage) -> Dict:
        """
        验证治疗配置

        Args:
            cancer_stage: 癌症分期

        Returns:
            验证结果字典
        """
        errors = []
        warnings = []

        # 验证分期成本配置
        if cancer_stage not in self.stage_specific_costs:
            errors.append(f"未配置分期成本: {cancer_stage.value}")
        else:
            stage_cost = self.stage_specific_costs[cancer_stage]
            if stage_cost.initial_cost < 0:
                errors.append("初始治疗成本不能为负数")
            if stage_cost.monthly_cost < 0:
                errors.append("月度治疗成本不能为负数")
            if stage_cost.followup_cost < 0:
                errors.append("随访成本不能为负数")

        # 验证治疗方案
        if cancer_stage in self.treatment_protocols:
            protocols = self.treatment_protocols[cancer_stage]
            if not protocols:
                warnings.append("未配置治疗方案")
            else:
                for i, protocol in enumerate(protocols):
                    if protocol.success_rate < 0 or protocol.success_rate > 1:
                        errors.append(f"方案{i+1}成功率必须在0-1之间")
                    if protocol.duration_months <= 0:
                        errors.append(f"方案{i+1}治疗持续时间必须大于0")

        # 验证并发症成本
        for comp_type, comp_cost in self.complication_costs.items():
            if comp_cost.treatment_cost < 0:
                errors.append(f"并发症{comp_type.value}治疗成本不能为负数")
            if comp_cost.probability < 0 or comp_cost.probability > 1:
                errors.append(f"并发症{comp_type.value}概率必须在0-1之间")

        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }

    def calculate_cost_by_modality(
        self,
        treatment_modalities: List[TreatmentModality],
        duration_months: float = 12.0
    ) -> Dict:
        """
        按治疗方式计算成本

        Args:
            treatment_modalities: 治疗方式列表
            duration_months: 治疗持续时间

        Returns:
            按方式分解的成本
        """
        cost_breakdown = {}
        total_cost = 0.0

        for modality in treatment_modalities:
            if modality in self.modality_costs:
                modality_cost = self.modality_costs[modality]

                # 某些治疗方式需要考虑持续时间
                if modality in [TreatmentModality.CHEMOTHERAPY, TreatmentModality.RADIOTHERAPY]:
                    adjusted_cost = modality_cost * (duration_months / 12.0)
                else:
                    adjusted_cost = modality_cost

                cost_breakdown[modality.value] = adjusted_cost
                total_cost += adjusted_cost
            else:
                cost_breakdown[modality.value] = 0.0

        cost_breakdown['total'] = total_cost
        return cost_breakdown
