"""
筛查策略设计器界面组件

提供筛查策略配置的图形界面，包括年龄范围设置、筛查间隔配置、
工具序列管理和策略预览功能。
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QGroupBox, QLabel, QPushButton, QSpinBox, QDoubleSpinBox,
    QComboBox, QSlider, QTextEdit, QListWidget, QListWidgetItem,
    QSplitter, QFrame, QCheckBox, QLineEdit, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QScrollArea, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot
from PyQt6.QtGui import QFont, QColor, QPalette, QIcon, QPixmap

from src.modules.screening.strategy import (
    ScreeningStrategy, ScreeningInterval, TargetPopulation, ScreeningFrequency
)
from src.modules.screening.enums import ScreeningToolType

logger = logging.getLogger(__name__)


class StrategyDesignerWidget(QWidget):
    """
    筛查策略设计器主界面
    
    提供完整的筛查策略配置功能，包括基本信息设置、
    筛查间隔配置、目标人群设置和策略预览。
    """
    
    # 信号定义
    strategy_created = pyqtSignal(object)      # 策略创建信号
    strategy_modified = pyqtSignal(object)     # 策略修改信号
    strategy_validated = pyqtSignal(bool, list)  # 策略验证信号
    
    def __init__(self, parent=None):
        """初始化策略设计器"""
        super().__init__(parent)
        
        self.current_strategy: Optional[ScreeningStrategy] = None
        self.validation_errors: List[str] = []
        
        self._setup_ui()
        self._connect_signals()
        self._create_default_strategy()
        
        logger.info("筛查策略设计器初始化完成")
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧配置面板
        config_panel = self._create_config_panel()
        main_splitter.addWidget(config_panel)
        
        # 右侧预览面板
        preview_panel = self._create_preview_panel()
        main_splitter.addWidget(preview_panel)
        
        # 设置分割器比例
        main_splitter.setStretchFactor(0, 2)  # 配置面板
        main_splitter.setStretchFactor(1, 1)  # 预览面板
        
        # 底部操作按钮
        button_layout = self._create_button_layout()
        layout.addLayout(button_layout)
    
    def _create_config_panel(self) -> QWidget:
        """创建配置面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建标签页
        self.config_tabs = QTabWidget()
        layout.addWidget(self.config_tabs)
        
        # 基本信息标签页
        basic_tab = self._create_basic_info_tab()
        self.config_tabs.addTab(basic_tab, "基本信息")
        
        # 筛查间隔标签页
        intervals_tab = self._create_intervals_tab()
        self.config_tabs.addTab(intervals_tab, "筛查间隔")
        
        # 目标人群标签页
        population_tab = self._create_population_tab()
        self.config_tabs.addTab(population_tab, "目标人群")
        
        return widget
    
    def _create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本信息组
        basic_group = QGroupBox("策略基本信息")
        basic_layout = QFormLayout(basic_group)
        
        # 策略名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入策略名称")
        basic_layout.addRow("策略名称:", self.name_edit)
        
        # 策略描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("请输入策略描述")
        basic_layout.addRow("策略描述:", self.description_edit)
        
        # 策略版本
        self.version_edit = QLineEdit()
        self.version_edit.setText("1.0")
        basic_layout.addRow("版本:", self.version_edit)
        
        # 作者
        self.author_edit = QLineEdit()
        self.author_edit.setPlaceholderText("请输入作者姓名")
        basic_layout.addRow("作者:", self.author_edit)
        
        layout.addWidget(basic_group)
        
        # 策略配置组
        config_group = QGroupBox("策略配置")
        config_layout = QFormLayout(config_group)
        
        # 是否为模板
        self.is_template_check = QCheckBox()
        config_layout.addRow("保存为模板:", self.is_template_check)
        
        # 模板分类
        self.template_category_combo = QComboBox()
        self.template_category_combo.addItems([
            "国家指南", "国际标准", "自定义方案", "研究用途", "其他"
        ])
        self.template_category_combo.setEnabled(False)
        config_layout.addRow("模板分类:", self.template_category_combo)
        
        # 预算约束
        self.budget_spin = QDoubleSpinBox()
        self.budget_spin.setRange(0, 999999999)
        self.budget_spin.setSuffix(" 元")
        self.budget_spin.setSpecialValueText("无限制")
        config_layout.addRow("预算约束:", self.budget_spin)
        
        # 成本效益阈值
        self.cost_threshold_spin = QDoubleSpinBox()
        self.cost_threshold_spin.setRange(0, 999999)
        self.cost_threshold_spin.setSuffix(" 元/QALY")
        self.cost_threshold_spin.setSpecialValueText("无限制")
        config_layout.addRow("成本效益阈值:", self.cost_threshold_spin)
        
        layout.addWidget(config_group)
        layout.addStretch()
        
        return widget
    
    def _create_intervals_tab(self) -> QWidget:
        """创建筛查间隔标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 间隔配置组
        intervals_group = QGroupBox("筛查间隔配置")
        intervals_layout = QVBoxLayout(intervals_group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_interval_btn = QPushButton("添加间隔")
        self.add_interval_btn.setIcon(QIcon(":/icons/add.png"))
        toolbar_layout.addWidget(self.add_interval_btn)
        
        self.remove_interval_btn = QPushButton("删除间隔")
        self.remove_interval_btn.setIcon(QIcon(":/icons/remove.png"))
        self.remove_interval_btn.setEnabled(False)
        toolbar_layout.addWidget(self.remove_interval_btn)
        
        toolbar_layout.addStretch()
        intervals_layout.addLayout(toolbar_layout)
        
        # 间隔列表表格
        self.intervals_table = QTableWidget()
        self.intervals_table.setColumnCount(6)
        self.intervals_table.setHorizontalHeaderLabels([
            "筛查工具", "开始年龄", "结束年龄", "频率", "自定义年数", "优先级"
        ])
        
        # 设置表格属性
        header = self.intervals_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.intervals_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.intervals_table.setAlternatingRowColors(True)
        
        intervals_layout.addWidget(self.intervals_table)
        
        layout.addWidget(intervals_group)
        
        # 间隔详细配置组
        detail_group = QGroupBox("间隔详细配置")
        detail_layout = QFormLayout(detail_group)
        
        # 筛查工具选择
        self.tool_type_combo = QComboBox()
        for tool_type in ScreeningToolType:
            self.tool_type_combo.addItem(tool_type.display_name, tool_type)
        detail_layout.addRow("筛查工具:", self.tool_type_combo)
        
        # 年龄范围
        age_layout = QHBoxLayout()
        self.start_age_spin = QSpinBox()
        self.start_age_spin.setRange(18, 100)
        self.start_age_spin.setValue(50)
        age_layout.addWidget(self.start_age_spin)
        
        age_layout.addWidget(QLabel("至"))
        
        self.end_age_spin = QSpinBox()
        self.end_age_spin.setRange(18, 100)
        self.end_age_spin.setValue(75)
        age_layout.addWidget(self.end_age_spin)
        
        detail_layout.addRow("年龄范围:", age_layout)
        
        # 筛查频率
        self.frequency_combo = QComboBox()
        for freq in ScreeningFrequency:
            self.frequency_combo.addItem(freq.display_name, freq)
        detail_layout.addRow("筛查频率:", self.frequency_combo)
        
        # 自定义频率
        self.custom_frequency_spin = QDoubleSpinBox()
        self.custom_frequency_spin.setRange(0.1, 20.0)
        self.custom_frequency_spin.setSingleStep(0.5)
        self.custom_frequency_spin.setSuffix(" 年")
        self.custom_frequency_spin.setEnabled(False)
        detail_layout.addRow("自定义频率:", self.custom_frequency_spin)
        
        # 优先级
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(1, 10)
        self.priority_spin.setValue(1)
        detail_layout.addRow("优先级:", self.priority_spin)
        
        # 序列顺序
        self.sequence_spin = QSpinBox()
        self.sequence_spin.setRange(0, 100)
        self.sequence_spin.setValue(0)
        detail_layout.addRow("序列顺序:", self.sequence_spin)
        
        layout.addWidget(detail_group)
        
        return widget
    
    def _create_population_tab(self) -> QWidget:
        """创建目标人群标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本人群配置组
        basic_pop_group = QGroupBox("基本人群配置")
        basic_pop_layout = QFormLayout(basic_pop_group)
        
        # 目标年龄范围
        pop_age_layout = QHBoxLayout()
        self.pop_start_age_spin = QSpinBox()
        self.pop_start_age_spin.setRange(18, 100)
        self.pop_start_age_spin.setValue(50)
        pop_age_layout.addWidget(self.pop_start_age_spin)
        
        pop_age_layout.addWidget(QLabel("至"))
        
        self.pop_end_age_spin = QSpinBox()
        self.pop_end_age_spin.setRange(18, 100)
        self.pop_end_age_spin.setValue(75)
        pop_age_layout.addWidget(self.pop_end_age_spin)
        
        basic_pop_layout.addRow("目标年龄范围:", pop_age_layout)
        
        # 风险水平
        self.risk_level_combo = QComboBox()
        self.risk_level_combo.addItems([
            "平均风险", "高风险", "极高风险", "低风险"
        ])
        basic_pop_layout.addRow("风险水平:", self.risk_level_combo)
        
        # 性别限制
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["无限制", "仅男性", "仅女性"])
        basic_pop_layout.addRow("性别限制:", self.gender_combo)
        
        # 人群规模
        self.pop_size_spin = QSpinBox()
        self.pop_size_spin.setRange(0, 999999999)
        self.pop_size_spin.setSpecialValueText("未指定")
        self.pop_size_spin.setSuffix(" 人")
        basic_pop_layout.addRow("目标人群规模:", self.pop_size_spin)
        
        # 地理区域
        self.region_edit = QLineEdit()
        self.region_edit.setPlaceholderText("如：深圳市南山区")
        basic_pop_layout.addRow("地理区域:", self.region_edit)
        
        layout.addWidget(basic_pop_group)
        
        # 排除标准组
        exclusion_group = QGroupBox("排除标准")
        exclusion_layout = QVBoxLayout(exclusion_group)
        
        self.exclusion_list = QListWidget()
        self.exclusion_list.addItems([
            "既往结直肠癌病史",
            "炎症性肠病",
            "家族性腺瘤性息肉病",
            "严重合并症",
            "预期寿命<10年"
        ])
        
        # 设置为可多选
        for i in range(self.exclusion_list.count()):
            item = self.exclusion_list.item(i)
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            item.setCheckState(Qt.CheckState.Unchecked)
        
        exclusion_layout.addWidget(self.exclusion_list)
        
        layout.addWidget(exclusion_group)
        
        # 包含标准组
        inclusion_group = QGroupBox("包含标准")
        inclusion_layout = QVBoxLayout(inclusion_group)
        
        self.inclusion_list = QListWidget()
        self.inclusion_list.addItems([
            "年龄符合筛查范围",
            "无症状个体",
            "知情同意",
            "能够配合筛查",
            "居住在目标区域"
        ])
        
        # 设置为可多选
        for i in range(self.inclusion_list.count()):
            item = self.inclusion_list.item(i)
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            item.setCheckState(Qt.CheckState.Checked)
        
        inclusion_layout.addWidget(self.inclusion_list)
        
        layout.addWidget(inclusion_group)
        layout.addStretch()
        
        return widget
    
    def _create_preview_panel(self) -> QWidget:
        """创建预览面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 预览标题
        title_label = QLabel("策略预览")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 验证状态
        self.validation_label = QLabel("策略状态：未验证")
        layout.addWidget(self.validation_label)
        
        # 预览文本
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        layout.addWidget(self.preview_text)
        
        # 验证错误列表
        errors_label = QLabel("验证错误:")
        layout.addWidget(errors_label)
        
        self.errors_list = QListWidget()
        self.errors_list.setMaximumHeight(150)
        layout.addWidget(self.errors_list)
        
        return widget
    
    def _create_button_layout(self) -> QHBoxLayout:
        """创建底部按钮布局"""
        layout = QHBoxLayout()
        
        # 验证按钮
        self.validate_btn = QPushButton("验证策略")
        self.validate_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(self.validate_btn)
        
        # 保存按钮
        self.save_btn = QPushButton("保存策略")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(self.save_btn)
        
        # 重置按钮
        self.reset_btn = QPushButton("重置")
        layout.addWidget(self.reset_btn)
        
        layout.addStretch()
        
        # 导入/导出按钮
        self.import_btn = QPushButton("导入策略")
        layout.addWidget(self.import_btn)
        
        self.export_btn = QPushButton("导出策略")
        layout.addWidget(self.export_btn)
        
        return layout

    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 基本信息变更信号
        self.name_edit.textChanged.connect(self._on_basic_info_changed)
        self.description_edit.textChanged.connect(self._on_basic_info_changed)
        self.version_edit.textChanged.connect(self._on_basic_info_changed)
        self.author_edit.textChanged.connect(self._on_basic_info_changed)

        # 模板配置信号
        self.is_template_check.toggled.connect(self._on_template_toggled)
        self.template_category_combo.currentTextChanged.connect(self._on_basic_info_changed)

        # 预算配置信号
        self.budget_spin.valueChanged.connect(self._on_basic_info_changed)
        self.cost_threshold_spin.valueChanged.connect(self._on_basic_info_changed)

        # 间隔配置信号
        self.add_interval_btn.clicked.connect(self._add_interval)
        self.remove_interval_btn.clicked.connect(self._remove_interval)
        self.intervals_table.itemSelectionChanged.connect(self._on_interval_selection_changed)

        # 间隔详细配置信号
        self.tool_type_combo.currentIndexChanged.connect(self._on_interval_config_changed)
        self.start_age_spin.valueChanged.connect(self._on_interval_config_changed)
        self.end_age_spin.valueChanged.connect(self._on_interval_config_changed)
        self.frequency_combo.currentIndexChanged.connect(self._on_frequency_changed)
        self.custom_frequency_spin.valueChanged.connect(self._on_interval_config_changed)
        self.priority_spin.valueChanged.connect(self._on_interval_config_changed)
        self.sequence_spin.valueChanged.connect(self._on_interval_config_changed)

        # 目标人群配置信号
        self.pop_start_age_spin.valueChanged.connect(self._on_population_changed)
        self.pop_end_age_spin.valueChanged.connect(self._on_population_changed)
        self.risk_level_combo.currentTextChanged.connect(self._on_population_changed)
        self.gender_combo.currentTextChanged.connect(self._on_population_changed)
        self.pop_size_spin.valueChanged.connect(self._on_population_changed)
        self.region_edit.textChanged.connect(self._on_population_changed)

        # 按钮信号
        self.validate_btn.clicked.connect(self._validate_strategy)
        self.save_btn.clicked.connect(self._save_strategy)
        self.reset_btn.clicked.connect(self._reset_strategy)
        self.import_btn.clicked.connect(self._import_strategy)
        self.export_btn.clicked.connect(self._export_strategy)

    def _create_default_strategy(self) -> None:
        """创建默认策略"""
        self.current_strategy = ScreeningStrategy(
            name="新建筛查策略",
            description="请配置筛查策略的详细信息",
            author="",
            version="1.0"
        )
        self._update_ui_from_strategy()

    def _update_ui_from_strategy(self) -> None:
        """从策略对象更新界面"""
        if not self.current_strategy:
            return

        # 更新基本信息
        self.name_edit.setText(self.current_strategy.name)
        self.description_edit.setPlainText(self.current_strategy.description)
        self.version_edit.setText(self.current_strategy.version)
        self.author_edit.setText(self.current_strategy.author)

        # 更新策略配置
        self.is_template_check.setChecked(self.current_strategy.is_template)
        if self.current_strategy.template_category:
            index = self.template_category_combo.findText(self.current_strategy.template_category)
            if index >= 0:
                self.template_category_combo.setCurrentIndex(index)

        if self.current_strategy.budget_constraint:
            self.budget_spin.setValue(self.current_strategy.budget_constraint)

        if self.current_strategy.cost_effectiveness_threshold:
            self.cost_threshold_spin.setValue(self.current_strategy.cost_effectiveness_threshold)

        # 更新目标人群
        pop = self.current_strategy.target_population
        self.pop_start_age_spin.setValue(pop.age_range[0])
        self.pop_end_age_spin.setValue(pop.age_range[1])

        # 设置风险水平
        risk_map = {"average": "平均风险", "high": "高风险", "very_high": "极高风险", "low": "低风险"}
        risk_text = risk_map.get(pop.risk_level, "平均风险")
        index = self.risk_level_combo.findText(risk_text)
        if index >= 0:
            self.risk_level_combo.setCurrentIndex(index)

        # 设置性别限制
        gender_map = {None: "无限制", "male": "仅男性", "female": "仅女性"}
        gender_text = gender_map.get(pop.gender_restriction, "无限制")
        index = self.gender_combo.findText(gender_text)
        if index >= 0:
            self.gender_combo.setCurrentIndex(index)

        if pop.population_size:
            self.pop_size_spin.setValue(pop.population_size)

        if pop.geographic_region:
            self.region_edit.setText(pop.geographic_region)

        # 更新间隔表格
        self._update_intervals_table()

        # 更新预览
        self._update_preview()

    def _update_intervals_table(self) -> None:
        """更新间隔表格"""
        if not self.current_strategy:
            return

        self.intervals_table.setRowCount(len(self.current_strategy.intervals))

        for row, interval in enumerate(self.current_strategy.intervals):
            # 筛查工具
            tool_item = QTableWidgetItem(interval.tool_type.display_name)
            tool_item.setData(Qt.ItemDataRole.UserRole, interval.tool_type)
            self.intervals_table.setItem(row, 0, tool_item)

            # 开始年龄
            start_age_item = QTableWidgetItem(str(interval.start_age))
            self.intervals_table.setItem(row, 1, start_age_item)

            # 结束年龄
            end_age_item = QTableWidgetItem(str(interval.end_age))
            self.intervals_table.setItem(row, 2, end_age_item)

            # 频率
            freq_item = QTableWidgetItem(interval.frequency.display_name)
            self.intervals_table.setItem(row, 3, freq_item)

            # 自定义年数
            custom_freq = interval.custom_frequency_years if interval.custom_frequency_years else ""
            custom_item = QTableWidgetItem(str(custom_freq))
            self.intervals_table.setItem(row, 4, custom_item)

            # 优先级
            priority_item = QTableWidgetItem(str(interval.priority))
            self.intervals_table.setItem(row, 5, priority_item)

    @pyqtSlot()
    def _on_basic_info_changed(self) -> None:
        """基本信息变更处理"""
        if not self.current_strategy:
            return

        self.current_strategy.name = self.name_edit.text()
        self.current_strategy.description = self.description_edit.toPlainText()
        self.current_strategy.version = self.version_edit.text()
        self.current_strategy.author = self.author_edit.text()
        self.current_strategy.modified_date = datetime.now()

        # 更新预算配置
        if self.budget_spin.value() > 0:
            self.current_strategy.budget_constraint = self.budget_spin.value()
        else:
            self.current_strategy.budget_constraint = None

        if self.cost_threshold_spin.value() > 0:
            self.current_strategy.cost_effectiveness_threshold = self.cost_threshold_spin.value()
        else:
            self.current_strategy.cost_effectiveness_threshold = None

        self._update_preview()
        self.strategy_modified.emit(self.current_strategy)

    @pyqtSlot(bool)
    def _on_template_toggled(self, checked: bool) -> None:
        """模板选项切换处理"""
        self.template_category_combo.setEnabled(checked)
        if self.current_strategy:
            self.current_strategy.is_template = checked
            if checked and self.template_category_combo.currentText():
                self.current_strategy.template_category = self.template_category_combo.currentText()
            else:
                self.current_strategy.template_category = None
            self._update_preview()

    @pyqtSlot()
    def _add_interval(self) -> None:
        """添加筛查间隔"""
        if not self.current_strategy:
            return

        try:
            # 获取当前配置
            tool_type = self.tool_type_combo.currentData()
            start_age = self.start_age_spin.value()
            end_age = self.end_age_spin.value()
            frequency = self.frequency_combo.currentData()
            custom_freq = self.custom_frequency_spin.value() if frequency == ScreeningFrequency.CUSTOM else None
            priority = self.priority_spin.value()
            sequence = self.sequence_spin.value()

            # 创建新间隔
            interval = ScreeningInterval(
                tool_type=tool_type,
                start_age=start_age,
                end_age=end_age,
                frequency=frequency,
                custom_frequency_years=custom_freq,
                priority=priority,
                sequence_order=sequence
            )

            # 添加到策略
            self.current_strategy.add_interval(interval)

            # 更新界面
            self._update_intervals_table()
            self._update_preview()

            logger.info(f"添加筛查间隔: {tool_type.display_name}, {start_age}-{end_age}岁")

        except Exception as e:
            QMessageBox.warning(self, "添加失败", f"添加筛查间隔失败: {str(e)}")
            logger.error(f"添加筛查间隔失败: {e}")

    @pyqtSlot()
    def _remove_interval(self) -> None:
        """删除筛查间隔"""
        if not self.current_strategy:
            return

        current_row = self.intervals_table.currentRow()
        if current_row < 0 or current_row >= len(self.current_strategy.intervals):
            return

        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            "确定要删除选中的筛查间隔吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 删除间隔
            interval = self.current_strategy.intervals[current_row]
            self.current_strategy.remove_interval(interval)

            # 更新界面
            self._update_intervals_table()
            self._update_preview()

            logger.info(f"删除筛查间隔: 第{current_row + 1}行")

    @pyqtSlot()
    def _on_interval_selection_changed(self) -> None:
        """间隔选择变更处理"""
        current_row = self.intervals_table.currentRow()
        self.remove_interval_btn.setEnabled(current_row >= 0)

    @pyqtSlot()
    def _on_interval_config_changed(self) -> None:
        """间隔配置变更处理"""
        # 这里可以添加实时验证逻辑
        pass

    @pyqtSlot()
    def _on_frequency_changed(self) -> None:
        """频率变更处理"""
        frequency = self.frequency_combo.currentData()
        self.custom_frequency_spin.setEnabled(frequency == ScreeningFrequency.CUSTOM)

    @pyqtSlot()
    def _on_population_changed(self) -> None:
        """目标人群配置变更处理"""
        if not self.current_strategy:
            return

        # 更新年龄范围
        start_age = self.pop_start_age_spin.value()
        end_age = self.pop_end_age_spin.value()
        self.current_strategy.target_population.age_range = (start_age, end_age)

        # 更新风险水平
        risk_map = {"平均风险": "average", "高风险": "high", "极高风险": "very_high", "低风险": "low"}
        risk_text = self.risk_level_combo.currentText()
        self.current_strategy.target_population.risk_level = risk_map.get(risk_text, "average")

        # 更新性别限制
        gender_map = {"无限制": None, "仅男性": "male", "仅女性": "female"}
        gender_text = self.gender_combo.currentText()
        self.current_strategy.target_population.gender_restriction = gender_map.get(gender_text)

        # 更新人群规模
        if self.pop_size_spin.value() > 0:
            self.current_strategy.target_population.population_size = self.pop_size_spin.value()
        else:
            self.current_strategy.target_population.population_size = None

        # 更新地理区域
        self.current_strategy.target_population.geographic_region = self.region_edit.text()

        self._update_preview()

    def _update_preview(self) -> None:
        """更新策略预览"""
        if not self.current_strategy:
            return

        preview_text = f"""策略名称: {self.current_strategy.name}
策略描述: {self.current_strategy.description}
版本: {self.current_strategy.version}
作者: {self.current_strategy.author}
创建时间: {self.current_strategy.created_date.strftime('%Y-%m-%d %H:%M:%S')}

目标人群:
  年龄范围: {self.current_strategy.target_population.age_range[0]}-{self.current_strategy.target_population.age_range[1]}岁
  风险水平: {self.current_strategy.target_population.risk_level}
  性别限制: {self.current_strategy.target_population.gender_restriction or '无'}
  人群规模: {self.current_strategy.target_population.population_size or '未指定'}
  地理区域: {self.current_strategy.target_population.geographic_region or '未指定'}

筛查间隔配置:
"""

        for i, interval in enumerate(self.current_strategy.intervals, 1):
            preview_text += f"""  {i}. {interval.tool_type.display_name}
     年龄范围: {interval.start_age}-{interval.end_age}岁
     筛查频率: {interval.frequency.display_name}
     优先级: {interval.priority}

"""

        if self.current_strategy.budget_constraint:
            preview_text += f"\n预算约束: {self.current_strategy.budget_constraint:,.0f} 元"

        if self.current_strategy.cost_effectiveness_threshold:
            preview_text += f"\n成本效益阈值: {self.current_strategy.cost_effectiveness_threshold:,.0f} 元/QALY"

        self.preview_text.setPlainText(preview_text)

    @pyqtSlot()
    def _validate_strategy(self) -> None:
        """验证策略"""
        if not self.current_strategy:
            return

        self.validation_errors = self.current_strategy.validate()

        # 更新验证状态
        if self.validation_errors:
            self.validation_label.setText("策略状态：验证失败")
            self.validation_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.validation_label.setText("策略状态：验证通过")
            self.validation_label.setStyleSheet("color: green; font-weight: bold;")

        # 更新错误列表
        self.errors_list.clear()
        for error in self.validation_errors:
            self.errors_list.addItem(error)

        # 发送验证信号
        self.strategy_validated.emit(len(self.validation_errors) == 0, self.validation_errors)

        logger.info(f"策略验证完成，错误数量: {len(self.validation_errors)}")

    @pyqtSlot()
    def _save_strategy(self) -> None:
        """保存策略"""
        if not self.current_strategy:
            return

        # 先验证策略
        self._validate_strategy()

        if self.validation_errors:
            reply = QMessageBox.question(
                self, "保存确认",
                "策略验证失败，是否仍要保存？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return

        try:
            # 这里应该调用策略保存服务
            # 目前只是发送信号
            self.strategy_created.emit(self.current_strategy)

            QMessageBox.information(self, "保存成功", "策略已成功保存！")
            logger.info(f"策略保存成功: {self.current_strategy.name}")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存策略失败: {str(e)}")
            logger.error(f"保存策略失败: {e}")

    @pyqtSlot()
    def _reset_strategy(self) -> None:
        """重置策略"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有配置吗？这将清除当前的所有设置。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self._create_default_strategy()
            logger.info("策略已重置")

    @pyqtSlot()
    def _import_strategy(self) -> None:
        """导入策略"""
        # 这里应该实现策略导入功能
        QMessageBox.information(self, "功能开发中", "策略导入功能正在开发中...")

    @pyqtSlot()
    def _export_strategy(self) -> None:
        """导出策略"""
        # 这里应该实现策略导出功能
        QMessageBox.information(self, "功能开发中", "策略导出功能正在开发中...")

    def set_strategy(self, strategy: ScreeningStrategy) -> None:
        """设置当前策略"""
        self.current_strategy = strategy
        self._update_ui_from_strategy()

    def get_strategy(self) -> Optional[ScreeningStrategy]:
        """获取当前策略"""
        return self.current_strategy
