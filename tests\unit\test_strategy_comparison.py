"""
筛查策略比较和可视化组件测试

测试策略比较组件的功能。
"""

import pytest
import sys
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 确保PyQt6可用
try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    from PyQt6.QtTest import QTest
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

from src.modules.screening.strategy import (
    ScreeningStrategy, ScreeningInterval, TargetPopulation, ScreeningFrequency
)
from src.modules.screening.enums import ScreeningToolType

if PYQT_AVAILABLE:
    from src.interfaces.desktop.widgets.strategy_comparison import StrategyComparisonWidget


@pytest.mark.skipif(not PYQT_AVAILABLE, reason="PyQt6 not available")
class TestStrategyComparisonWidget:
    """测试筛查策略比较组件"""
    
    @pytest.fixture(autouse=True)
    def setup_qt_app(self):
        """设置Qt应用程序"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        yield
        # 清理在teardown中进行
    
    @pytest.fixture
    def widget(self):
        """创建策略比较组件"""
        widget = StrategyComparisonWidget()
        yield widget
        widget.close()
    
    @pytest.fixture
    def sample_strategies(self):
        """创建示例策略列表"""
        strategies = []
        
        # 策略1：FIT年度筛查
        strategy1 = ScreeningStrategy(
            name="FIT年度筛查策略",
            description="基于FIT的年度筛查方案",
            version="1.0",
            author="测试作者1"
        )
        interval1 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL
        )
        strategy1.add_interval(interval1)
        strategies.append(strategy1)
        
        # 策略2：结肠镜10年筛查
        strategy2 = ScreeningStrategy(
            name="结肠镜10年筛查策略",
            description="基于结肠镜的10年筛查方案",
            version="1.0",
            author="测试作者2"
        )
        interval2 = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.CUSTOM,
            custom_frequency_years=10.0
        )
        strategy2.add_interval(interval2)
        strategies.append(strategy2)
        
        # 策略3：组合筛查策略
        strategy3 = ScreeningStrategy(
            name="组合筛查策略",
            description="FIT和结肠镜组合筛查方案",
            version="1.0",
            author="测试作者3"
        )
        # 添加FIT间隔
        fit_interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=65,
            frequency=ScreeningFrequency.ANNUAL
        )
        strategy3.add_interval(fit_interval)
        # 添加结肠镜间隔
        colonoscopy_interval = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=65,
            end_age=75,
            frequency=ScreeningFrequency.CUSTOM,
            custom_frequency_years=5.0
        )
        strategy3.add_interval(colonoscopy_interval)
        strategies.append(strategy3)
        
        return strategies
    
    def test_widget_initialization(self, widget):
        """测试组件初始化"""
        assert widget is not None
        assert len(widget.strategies) == 0
        assert widget.validator is not None
        assert widget.template_manager is not None
    
    def test_ui_components_exist(self, widget):
        """测试UI组件存在性"""
        # 检查主要组件是否存在
        assert hasattr(widget, 'strategy_table')
        assert hasattr(widget, 'comparison_tabs')
        assert hasattr(widget, 'basic_comparison_table')
        assert hasattr(widget, 'detail_comparison_table')
        assert hasattr(widget, 'chart_view')
        assert hasattr(widget, 'diff_analysis_text')
        
        # 检查按钮
        assert hasattr(widget, 'add_strategy_btn')
        assert hasattr(widget, 'remove_strategy_btn')
        assert hasattr(widget, 'load_template_btn')
        assert hasattr(widget, 'export_btn')
        
        # 检查下拉框
        assert hasattr(widget, 'comparison_mode_combo')
        assert hasattr(widget, 'chart_type_combo')
    
    def test_add_strategy(self, widget, sample_strategies):
        """测试添加策略"""
        strategy = sample_strategies[0]
        
        # 添加策略
        widget.add_strategy(strategy)
        
        assert len(widget.strategies) == 1
        assert widget.strategies[0] == strategy
        assert widget.strategy_table.rowCount() == 1
    
    def test_add_duplicate_strategy(self, widget, sample_strategies):
        """测试添加重复策略"""
        strategy = sample_strategies[0]
        
        # 添加策略两次
        widget.add_strategy(strategy)
        widget.add_strategy(strategy)
        
        # 应该只有一个策略
        assert len(widget.strategies) == 1
    
    def test_remove_strategy(self, widget, sample_strategies):
        """测试移除策略"""
        strategy = sample_strategies[0]
        
        # 添加策略
        widget.add_strategy(strategy)
        assert len(widget.strategies) == 1
        
        # 移除策略
        result = widget.remove_strategy(strategy)
        
        assert result is True
        assert len(widget.strategies) == 0
        assert widget.strategy_table.rowCount() == 0
    
    def test_remove_nonexistent_strategy(self, widget, sample_strategies):
        """测试移除不存在的策略"""
        strategy = sample_strategies[0]
        
        # 尝试移除不存在的策略
        result = widget.remove_strategy(strategy)
        
        assert result is False
        assert len(widget.strategies) == 0
    
    def test_clear_strategies(self, widget, sample_strategies):
        """测试清空所有策略"""
        # 添加多个策略
        for strategy in sample_strategies:
            widget.add_strategy(strategy)
        
        assert len(widget.strategies) == len(sample_strategies)
        
        # 清空策略
        widget.clear_strategies()
        
        assert len(widget.strategies) == 0
        assert widget.strategy_table.rowCount() == 0
    
    def test_update_strategy_table(self, widget, sample_strategies):
        """测试更新策略表格"""
        # 添加策略
        for strategy in sample_strategies:
            widget.add_strategy(strategy)
        
        # 检查表格内容
        assert widget.strategy_table.rowCount() == len(sample_strategies)
        
        # 检查第一行内容
        assert widget.strategy_table.item(0, 0).text() == sample_strategies[0].name
        assert widget.strategy_table.item(0, 1).text() == sample_strategies[0].version
        assert widget.strategy_table.item(0, 2).text() == sample_strategies[0].author
    
    def test_comparison_with_insufficient_strategies(self, widget, sample_strategies):
        """测试策略数量不足时的比较"""
        # 只添加一个策略
        widget.add_strategy(sample_strategies[0])
        
        # 比较表格应该为空或显示提示
        assert widget.basic_comparison_table.rowCount() == 0 or \
               widget.basic_comparison_table.columnCount() <= 1
    
    def test_comparison_with_multiple_strategies(self, widget, sample_strategies):
        """测试多个策略的比较"""
        # 添加多个策略
        for strategy in sample_strategies[:2]:
            widget.add_strategy(strategy)
        
        # 应该有比较结果
        assert widget.basic_comparison_table.columnCount() == 2
        assert widget.basic_comparison_table.rowCount() > 0
    
    def test_basic_comparison_content(self, widget, sample_strategies):
        """测试基本信息比较内容"""
        # 添加两个策略
        widget.add_strategy(sample_strategies[0])
        widget.add_strategy(sample_strategies[1])
        
        # 检查比较表格内容
        assert widget.basic_comparison_table.columnCount() == 2
        
        # 检查策略名称行
        name_row = 0  # 假设策略名称在第一行
        if widget.basic_comparison_table.rowCount() > name_row:
            assert widget.basic_comparison_table.item(name_row, 0).text() == sample_strategies[0].name
            assert widget.basic_comparison_table.item(name_row, 1).text() == sample_strategies[1].name
    
    def test_chart_type_change(self, widget, sample_strategies):
        """测试图表类型变更"""
        # 添加策略
        for strategy in sample_strategies:
            widget.add_strategy(strategy)
        
        # 测试不同图表类型
        chart_types = ["年龄范围比较", "筛查频率比较", "工具类型分布", "成本效益比较"]
        
        for chart_type in chart_types:
            widget.chart_type_combo.setCurrentText(chart_type)
            # 检查图表是否更新（这里主要测试不出错）
            assert widget.chart_view.chart() is not None
    
    def test_diff_analysis_update(self, widget, sample_strategies):
        """测试差异分析更新"""
        # 添加两个策略
        widget.add_strategy(sample_strategies[0])
        widget.add_strategy(sample_strategies[1])
        
        # 检查差异分析文本
        diff_text = widget.diff_analysis_text.toPlainText()
        assert len(diff_text) > 0
        assert "差异分析" in diff_text
        assert sample_strategies[0].name in diff_text
        assert sample_strategies[1].name in diff_text
    
    def test_strategy_selection_change(self, widget, sample_strategies):
        """测试策略选择变更"""
        # 添加策略
        widget.add_strategy(sample_strategies[0])
        
        # 初始状态移除按钮应该禁用
        assert not widget.remove_strategy_btn.isEnabled()
        
        # 选择策略
        widget.strategy_table.selectRow(0)
        widget._on_strategy_selection_changed()
        
        # 移除按钮应该启用
        assert widget.remove_strategy_btn.isEnabled()
    
    def test_comparison_mode_change(self, widget, sample_strategies):
        """测试比较模式变更"""
        # 添加策略
        for strategy in sample_strategies[:2]:
            widget.add_strategy(strategy)
        
        # 测试不同比较模式
        modes = ["基本信息比较", "详细配置比较", "成本效益比较", "验证结果比较"]
        
        for mode in modes:
            widget.comparison_mode_combo.setCurrentText(mode)
            # 主要测试不出错
    
    @patch('PyQt6.QtWidgets.QMessageBox.information')
    def test_add_strategy_button_click(self, mock_info, widget):
        """测试添加策略按钮点击"""
        widget._add_strategy()
        
        # 应该显示信息对话框
        mock_info.assert_called_once()
    
    @patch('PyQt6.QtWidgets.QMessageBox.information')
    def test_load_template_button_click(self, mock_info, widget):
        """测试加载模板按钮点击"""
        with patch.object(widget.template_manager, 'list_templates', return_value=[]):
            widget._load_from_template()
            
            # 应该显示没有模板的信息
            mock_info.assert_called_once()
    
    @patch('PyQt6.QtWidgets.QMessageBox.question')
    def test_remove_strategy_button_click(self, mock_question, widget, sample_strategies):
        """测试移除策略按钮点击"""
        # 设置返回值为Yes
        mock_question.return_value = Mock()
        with patch.object(Mock(), 'StandardButton') as mock_button:
            mock_button.Yes = Mock()
            with patch('PyQt6.QtWidgets.QMessageBox.StandardButton', mock_button):
                # 添加策略并选择
                widget.add_strategy(sample_strategies[0])
                widget.strategy_table.selectRow(0)
                
                initial_count = len(widget.strategies)
                widget._remove_strategy()
                
                # 应该调用确认对话框
                mock_question.assert_called_once()
    
    @patch('PyQt6.QtWidgets.QFileDialog.getSaveFileName')
    def test_export_comparison(self, mock_save_dialog, widget, sample_strategies):
        """测试导出比较结果"""
        # 设置文件对话框返回值
        temp_file = tempfile.NamedTemporaryFile(suffix='.html', delete=False)
        mock_save_dialog.return_value = (temp_file.name, "HTML文件 (*.html)")
        
        # 添加策略
        for strategy in sample_strategies[:2]:
            widget.add_strategy(strategy)
        
        # 导出比较结果
        widget._export_comparison()
        
        # 检查文件是否创建
        assert Path(temp_file.name).exists()
        
        # 清理临时文件
        Path(temp_file.name).unlink()
    
    def test_export_to_html(self, widget, sample_strategies):
        """测试导出HTML格式"""
        # 添加策略
        for strategy in sample_strategies[:2]:
            widget.add_strategy(strategy)
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.html', delete=False) as temp_file:
            widget._export_to_html(temp_file.name)
            
            # 检查文件内容
            with open(temp_file.name, 'r', encoding='utf-8') as f:
                content = f.read()
                assert "<html>" in content
                assert "筛查策略比较报告" in content
                assert sample_strategies[0].name in content
                assert sample_strategies[1].name in content
            
            # 清理临时文件
            Path(temp_file.name).unlink()
    
    def test_export_to_text(self, widget, sample_strategies):
        """测试导出文本格式"""
        # 添加策略
        for strategy in sample_strategies[:2]:
            widget.add_strategy(strategy)
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            widget._export_to_text(temp_file.name)
            
            # 检查文件内容
            with open(temp_file.name, 'r', encoding='utf-8') as f:
                content = f.read()
                assert "筛查策略比较报告" in content
                assert sample_strategies[0].name in content
                assert sample_strategies[1].name in content
            
            # 清理临时文件
            Path(temp_file.name).unlink()
    
    def test_signal_emissions(self, widget, sample_strategies):
        """测试信号发射"""
        # 创建信号接收器
        comparison_updated_received = []
        strategy_selected_received = []
        
        def on_comparison_updated(strategies):
            comparison_updated_received.append(strategies)
        
        def on_strategy_selected(strategy):
            strategy_selected_received.append(strategy)
        
        # 连接信号
        widget.comparison_updated.connect(on_comparison_updated)
        widget.strategy_selected.connect(on_strategy_selected)
        
        # 添加策略应该触发比较更新信号
        widget.add_strategy(sample_strategies[0])
        widget.add_strategy(sample_strategies[1])
        
        # 检查信号是否被触发
        assert len(comparison_updated_received) > 0
        
        # 选择策略应该触发策略选择信号
        widget.strategy_table.selectRow(0)
        widget._on_strategy_selection_changed()
        
        # 注意：由于测试环境限制，信号可能不会自动触发
        # 这里主要测试信号连接不出错
    
    def test_empty_state_handling(self, widget):
        """测试空状态处理"""
        # 没有策略时的状态
        assert len(widget.strategies) == 0
        assert widget.strategy_table.rowCount() == 0
        assert not widget.remove_strategy_btn.isEnabled()
        
        # 比较结果应该为空
        assert widget.basic_comparison_table.rowCount() == 0
        assert widget.diff_analysis_text.toPlainText() == ""
