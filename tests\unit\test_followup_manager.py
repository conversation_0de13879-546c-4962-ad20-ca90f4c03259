"""
后续诊断管理器单元测试

测试后续诊断流程触发、预约管理和诊断结果处理功能。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock

from src.core.individual import Individual
from src.core.enums import DiseaseState, Gender
from src.modules.screening import (
    FollowupManager, DiagnosticAppointment, DiagnosticResult,
    DiagnosticProcedure, AppointmentStatus, DetailedScreeningResult,
    ScreeningResultType, FollowupAction, ScreeningToolType
)


class TestFollowupManager:
    """后续诊断管理器测试类"""

    @pytest.fixture
    def healthcare_config(self):
        """医疗系统配置"""
        return {
            'diagnostic_capacity': {
                'diagnostic_colonoscopy': 100,
                'diagnostic_sigmoidoscopy': 50,
                'specialist_consultation': 200
            },
            'waiting_times': {
                'diagnostic_colonoscopy': 14,
                'diagnostic_sigmoidoscopy': 7,
                'specialist_consultation': 21
            },
            'facility_locations': {
                'hospital_a': {'capacity': 50, 'location': 'downtown'},
                'clinic_b': {'capacity': 30, 'location': 'suburb'}
            }
        }

    @pytest.fixture
    def followup_manager(self, healthcare_config):
        """创建后续诊断管理器"""
        return FollowupManager(healthcare_config)

    @pytest.fixture
    def individual(self):
        """测试个体"""
        return Individual(
            birth_year=1970,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.NORMAL
        )

    @pytest.fixture
    def positive_screening_result(self, individual):
        """阳性筛查结果"""
        return DetailedScreeningResult(
            individual_id=individual.individual_id,
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.8,
            followup_action=FollowupAction.DIAGNOSTIC_COLONOSCOPY
        )

    @pytest.fixture
    def diagnostic_appointment(self, individual, positive_screening_result):
        """诊断预约"""
        return DiagnosticAppointment(
            individual_id=individual.individual_id,
            procedure_type=DiagnosticProcedure.COLONOSCOPY,
            scheduled_date=datetime.now() + timedelta(days=14),
            referring_screening=positive_screening_result,
            waiting_time_days=14,
            priority_level=3,
            cost=1200.0
        )

    def test_followup_manager_initialization(self, healthcare_config):
        """测试后续诊断管理器初始化"""
        manager = FollowupManager(healthcare_config)
        
        assert manager.diagnostic_capacity == healthcare_config['diagnostic_capacity']
        assert manager.waiting_times == healthcare_config['waiting_times']
        assert manager.facility_locations == healthcare_config['facility_locations']
        assert len(manager.appointment_queue) == 0
        assert len(manager.completed_appointments) == 0

    def test_schedule_followup_colonoscopy(
        self, followup_manager, positive_screening_result, individual
    ):
        """测试安排后续肠镜检查"""
        appointment = followup_manager.schedule_followup(
            positive_screening_result, individual
        )
        
        assert appointment is not None
        assert appointment.individual_id == individual.individual_id
        assert appointment.procedure_type == DiagnosticProcedure.COLONOSCOPY
        assert appointment.referring_screening == positive_screening_result
        assert appointment.waiting_time_days > 0
        assert appointment.cost > 0
        assert appointment in followup_manager.appointment_queue

    def test_schedule_followup_no_action_needed(self, followup_manager, individual):
        """测试无需后续行动的情况"""
        negative_result = DetailedScreeningResult(
            individual_id=individual.individual_id,
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.NEGATIVE,
            test_date=datetime.now(),
            confidence_score=0.8,
            followup_action=FollowupAction.NONE
        )
        
        appointment = followup_manager.schedule_followup(negative_result, individual)
        assert appointment is None

    def test_determine_diagnostic_procedure(self, followup_manager):
        """测试确定诊断程序类型"""
        # 诊断性肠镜
        result = DetailedScreeningResult(
            individual_id="test",
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.8,
            followup_action=FollowupAction.DIAGNOSTIC_COLONOSCOPY
        )
        
        procedure = followup_manager._determine_diagnostic_procedure(result)
        assert procedure == DiagnosticProcedure.COLONOSCOPY

        # 专科转诊
        result.followup_action = FollowupAction.SPECIALIST_REFERRAL
        procedure = followup_manager._determine_diagnostic_procedure(result)
        assert procedure == DiagnosticProcedure.SPECIALIST_CONSULTATION

        # 立即治疗
        result.followup_action = FollowupAction.IMMEDIATE_TREATMENT
        procedure = followup_manager._determine_diagnostic_procedure(result)
        assert procedure == DiagnosticProcedure.COLONOSCOPY

    def test_calculate_priority_level(self, followup_manager, individual):
        """测试优先级计算"""
        # 高置信度结果
        high_confidence_result = DetailedScreeningResult(
            individual_id=individual.individual_id,
            tool_type=ScreeningToolType.FIT,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.9,
            followup_action=FollowupAction.DIAGNOSTIC_COLONOSCOPY
        )
        
        priority = followup_manager._calculate_priority_level(
            high_confidence_result, individual
        )
        assert priority >= 2  # 基础1 + 高置信度1

        # 立即治疗需求
        immediate_result = DetailedScreeningResult(
            individual_id=individual.individual_id,
            tool_type=ScreeningToolType.COLONOSCOPY,
            result_type=ScreeningResultType.POSITIVE,
            test_date=datetime.now(),
            confidence_score=0.9,
            followup_action=FollowupAction.IMMEDIATE_TREATMENT
        )
        
        priority = followup_manager._calculate_priority_level(
            immediate_result, individual
        )
        assert priority >= 4  # 基础1 + 高置信度1 + 立即治疗2

    def test_check_diagnostic_capacity(self, followup_manager):
        """测试诊断容量检查"""
        # 容量充足
        assert followup_manager._check_diagnostic_capacity(
            DiagnosticProcedure.COLONOSCOPY
        ) is True

        # 模拟容量不足
        followup_manager.diagnostic_capacity['diagnostic_colonoscopy'] = 0
        assert followup_manager._check_diagnostic_capacity(
            DiagnosticProcedure.COLONOSCOPY
        ) is False

    def test_calculate_waiting_time(self, followup_manager):
        """测试等待时间计算"""
        # 低优先级
        waiting_time = followup_manager._calculate_waiting_time(
            DiagnosticProcedure.COLONOSCOPY, 1
        )
        assert waiting_time == 21  # 14 * 1.5

        # 高优先级
        waiting_time = followup_manager._calculate_waiting_time(
            DiagnosticProcedure.COLONOSCOPY, 5
        )
        assert waiting_time == 7  # 14 * 0.5

    def test_calculate_diagnostic_cost(self, followup_manager):
        """测试诊断成本计算"""
        cost = followup_manager._calculate_diagnostic_cost(
            DiagnosticProcedure.COLONOSCOPY
        )
        assert cost == 1200.0

        cost = followup_manager._calculate_diagnostic_cost(
            DiagnosticProcedure.SPECIALIST_CONSULTATION
        )
        assert cost == 200.0

    def test_process_diagnostic_result_with_findings(
        self, followup_manager, individual, diagnostic_appointment
    ):
        """测试处理有发现的诊断结果"""
        # 先将个体状态设置为低风险腺瘤，然后诊断发现高风险腺瘤
        individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)

        # 创建诊断结果
        diagnostic_result = DiagnosticResult(
            appointment=diagnostic_appointment,
            findings=["高风险腺瘤"],
            confirmed_disease_state=DiseaseState.HIGH_RISK_ADENOMA,
            treatment_recommended=True,
            followup_interval_months=6
        )

        # 添加预约到队列
        followup_manager.appointment_queue.append(diagnostic_appointment)

        # 处理诊断结果
        result_info = followup_manager.process_diagnostic_result(
            diagnostic_result, individual
        )

        # 验证结果
        assert result_info["individual_id"] == individual.individual_id
        assert result_info["findings"] == ["高风险腺瘤"]
        assert result_info["treatment_recommended"] is True
        assert "更新疾病状态为: high_risk_adenoma" in result_info["actions_taken"]
        assert "模拟病变摘除" in result_info["actions_taken"]

        # 验证预约状态更新
        assert diagnostic_appointment.status == AppointmentStatus.COMPLETED
        assert diagnostic_appointment not in followup_manager.appointment_queue
        assert diagnostic_appointment in followup_manager.completed_appointments

        # 验证个体状态更新（病变摘除后应为正常）
        assert individual.current_disease_state == DiseaseState.NORMAL

    def test_process_diagnostic_result_no_findings(
        self, followup_manager, individual, diagnostic_appointment
    ):
        """测试处理无发现的诊断结果"""
        diagnostic_result = DiagnosticResult(
            appointment=diagnostic_appointment,
            findings=[],
            treatment_recommended=False,
            followup_interval_months=12
        )
        
        followup_manager.appointment_queue.append(diagnostic_appointment)
        
        result_info = followup_manager.process_diagnostic_result(
            diagnostic_result, individual
        )
        
        assert result_info["findings"] == []
        assert result_info["treatment_recommended"] is False
        assert diagnostic_appointment.status == AppointmentStatus.COMPLETED

    def test_simulate_lesion_removal(self, followup_manager, individual):
        """测试病变摘除模拟"""
        # 设置个体为腺瘤状态
        individual.transition_to_state(DiseaseState.HIGH_RISK_ADENOMA)
        
        diagnostic_result = DiagnosticResult(
            appointment=Mock(),
            findings=["高风险腺瘤"],
            confirmed_disease_state=DiseaseState.HIGH_RISK_ADENOMA
        )
        
        # 模拟病变摘除
        followup_manager._simulate_lesion_removal(individual, diagnostic_result)
        
        # 验证状态重置为正常
        assert individual.current_disease_state == DiseaseState.NORMAL
        assert diagnostic_result.additional_data['lesion_removed'] is True
        assert 'removal_date' in diagnostic_result.additional_data

    def test_initiate_treatment(self, followup_manager, individual):
        """测试启动治疗流程"""
        # 腺瘤治疗
        diagnostic_result = DiagnosticResult(
            appointment=Mock(),
            findings=["高风险腺瘤"],
            confirmed_disease_state=DiseaseState.HIGH_RISK_ADENOMA
        )
        
        treatment = followup_manager._initiate_treatment(individual, diagnostic_result)
        assert treatment == "内镜下摘除"

        # 癌症治疗
        diagnostic_result.confirmed_disease_state = DiseaseState.CLINICAL_CANCER
        treatment = followup_manager._initiate_treatment(individual, diagnostic_result)
        assert treatment == "癌症综合治疗"

    def test_add_diagnostic_history(self, followup_manager, individual):
        """测试添加诊断历史"""
        diagnostic_result = DiagnosticResult(
            appointment=DiagnosticAppointment(
                individual_id=individual.individual_id,
                procedure_type=DiagnosticProcedure.COLONOSCOPY,
                scheduled_date=datetime.now(),
                referring_screening=Mock(),
                actual_date=datetime.now()
            ),
            findings=["正常"],
            confirmed_disease_state=DiseaseState.NORMAL
        )
        
        followup_manager._add_diagnostic_history(individual, diagnostic_result)
        
        assert hasattr(individual, 'diagnostic_history')
        assert len(individual.diagnostic_history) == 1
        assert individual.diagnostic_history[0]['procedure'] == 'diagnostic_colonoscopy'
        assert individual.diagnostic_history[0]['findings'] == ["正常"]

    def test_get_appointment_statistics(self, followup_manager, individual):
        """测试获取预约统计信息"""
        # 添加一些预约
        appointment1 = DiagnosticAppointment(
            individual_id=individual.individual_id,
            procedure_type=DiagnosticProcedure.COLONOSCOPY,
            scheduled_date=datetime.now(),
            referring_screening=Mock(),
            waiting_time_days=14,
            cost=1200.0
        )
        
        appointment2 = DiagnosticAppointment(
            individual_id=individual.individual_id,
            procedure_type=DiagnosticProcedure.SPECIALIST_CONSULTATION,
            scheduled_date=datetime.now(),
            referring_screening=Mock(),
            waiting_time_days=21,
            cost=200.0
        )
        
        followup_manager.appointment_queue.extend([appointment1, appointment2])
        
        stats = followup_manager.get_appointment_statistics()
        
        assert stats['total_appointments'] == 2
        assert stats['pending_appointments'] == 2
        assert stats['completed_appointments'] == 0
        assert 'diagnostic_colonoscopy' in stats['procedure_statistics']
        assert 'specialist_consultation' in stats['procedure_statistics']
        assert stats['procedure_statistics']['diagnostic_colonoscopy']['count'] == 1
        assert stats['procedure_statistics']['diagnostic_colonoscopy']['total_cost'] == 1200.0

    def test_diagnostic_appointment_validation(self):
        """测试诊断预约数据验证"""
        # 有效预约
        valid_appointment = DiagnosticAppointment(
            individual_id="test",
            procedure_type=DiagnosticProcedure.COLONOSCOPY,
            scheduled_date=datetime.now(),
            referring_screening=Mock(),
            waiting_time_days=14,
            priority_level=3,
            cost=1200.0
        )
        # 应该不抛出异常

        # 无效等待时间
        with pytest.raises(ValueError, match="等待时间不能为负数"):
            DiagnosticAppointment(
                individual_id="test",
                procedure_type=DiagnosticProcedure.COLONOSCOPY,
                scheduled_date=datetime.now(),
                referring_screening=Mock(),
                waiting_time_days=-1  # 无效值
            )

        # 无效优先级
        with pytest.raises(ValueError, match="优先级必须在1-5之间"):
            DiagnosticAppointment(
                individual_id="test",
                procedure_type=DiagnosticProcedure.COLONOSCOPY,
                scheduled_date=datetime.now(),
                referring_screening=Mock(),
                priority_level=6  # 无效值
            )

    def test_diagnostic_result_validation(self):
        """测试诊断结果数据验证"""
        # 有效诊断结果
        valid_result = DiagnosticResult(
            appointment=Mock(),
            findings=["正常"],
            quality_score=0.95,
            followup_interval_months=12
        )
        # 应该不抛出异常

        # 无效质量分数
        with pytest.raises(ValueError, match="质量分数必须在0-1之间"):
            DiagnosticResult(
                appointment=Mock(),
                findings=["正常"],
                quality_score=1.5  # 无效值
            )

        # 无效随访间隔
        with pytest.raises(ValueError, match="随访间隔不能为负数"):
            DiagnosticResult(
                appointment=Mock(),
                findings=["正常"],
                followup_interval_months=-1  # 无效值
            )
