"""
Economics module for cost modeling and health economics analysis.

This module provides comprehensive cost modeling capabilities for the
colorectal cancer screening microsimulation model, including:
- Screening cost configuration and calculation
- Treatment cost modeling by cancer stage
- Cost adjustments for inflation and discounting
- Sensitivity analysis tools
- Economic data import and validation
"""

from .screening_costs import ScreeningCostModel
from .treatment_costs import TreatmentCostModel
from .cost_adjustments import CostAdjustmentEngine
from .sensitivity_analysis import SensitivityAnalyzer

__all__ = [
    'ScreeningCostModel',
    'TreatmentCostModel',
    'CostAdjustmentEngine',
    'SensitivityAnalyzer'
]
