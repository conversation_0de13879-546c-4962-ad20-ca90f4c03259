"""
粪便免疫化学检测 (FIT) 工具实现

实现FIT筛查工具的特异性检测逻辑和性能参数。
"""

import random
from typing import Dict, Any, Optional
import logging

from src.core.enums import DiseaseState, AnatomicalLocation
from src.core.individual import Individual
from ..enums import ScreeningToolType, ScreeningResult, InvasivenessLevel, OperatorSkillLevel
from ..screening_tool import ScreeningTool, ScreeningPerformance, ScreeningCharacteristics


logger = logging.getLogger(__name__)


class FITTool(ScreeningTool):
    """粪便免疫化学检测 (FIT) 工具实现"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化FIT工具
        
        Args:
            config: 配置参数字典
        """
        # 默认FIT性能参数（基于文献数据）
        default_performance = ScreeningPerformance(
            sensitivity_by_state={
                DiseaseState.NORMAL: 0.0,
                DiseaseState.LOW_RISK_ADENOMA: 0.15,
                DiseaseState.HIGH_RISK_ADENOMA: 0.35,
                DiseaseState.SMALL_SERRATED: 0.10,
                DiseaseState.LARGE_SERRATED: 0.25,
                DiseaseState.PRECLINICAL_CANCER: 0.75,
                DiseaseState.CLINICAL_CANCER_STAGE_I: 0.85,
                DiseaseState.CLINICAL_CANCER_STAGE_II: 0.90,
                DiseaseState.CLINICAL_CANCER_STAGE_III: 0.95,
                DiseaseState.CLINICAL_CANCER_STAGE_IV: 0.98,
                DiseaseState.CLINICAL_CANCER: 0.90,  # 向后兼容
            },
            specificity=0.95,
            detection_threshold=100.0,  # ng/mL血红蛋白
            location_sensitivity_modifiers={
                AnatomicalLocation.PROXIMAL_COLON: 0.8,  # 近端结肠敏感性较低
                AnatomicalLocation.DISTAL_COLON: 1.0,    # 标准敏感性
                AnatomicalLocation.RECTUM: 1.2           # 直肠敏感性较高
            }
            # 注释掉的高级调整（暂时不使用）
            # operator_dependency=1.1,
            # age_sensitivity_adjustment={...},
            # gender_sensitivity_modifiers={...}
        )
        
        # 默认FIT特性
        default_characteristics = ScreeningCharacteristics(
            invasiveness=InvasivenessLevel.NON_INVASIVE,
            operator_skill_required=OperatorSkillLevel.LOW,
            preparation_required=False,
            turnaround_time_days=3,
            can_detect_proximal=True,
            can_detect_distal=True,
            can_detect_rectal=True,
            requires_sedation=False,
            radiation_exposure=False,
            sample_collection_required=True
        )
        
        # 合并用户配置
        if config:
            # 处理performance子配置
            performance_config = config.get("performance", {})
            if "sensitivity_by_state" in performance_config:
                default_performance.sensitivity_by_state.update(
                    performance_config["sensitivity_by_state"]
                )
            if "specificity" in performance_config:
                default_performance.specificity = performance_config["specificity"]
            if "detection_threshold" in performance_config:
                default_performance.detection_threshold = performance_config["detection_threshold"]

            # 处理直接配置（向后兼容）
            if "sensitivity_by_state" in config:
                default_performance.sensitivity_by_state.update(
                    config["sensitivity_by_state"]
                )
            if "specificity" in config:
                default_performance.specificity = config["specificity"]
            if "detection_threshold" in config:
                default_performance.detection_threshold = config["detection_threshold"]
        
        super().__init__(
            tool_type=ScreeningToolType.FIT,
            performance=default_performance,
            characteristics=default_characteristics,
            config=config
        )
        
        # FIT特异性配置
        self.hemoglobin_threshold = self.performance.detection_threshold
        self.false_positive_rate = 1.0 - self.performance.specificity
    
    def perform_screening(
        self,
        individual: Individual,
        **kwargs
    ) -> ScreeningResult:
        """
        执行FIT筛查检查
        
        Args:
            individual: 被筛查个体
            **kwargs: 其他参数（如解剖位置、随机种子等）
            
        Returns:
            ScreeningResult: 筛查结果
        """
        try:
            # 获取个体当前疾病状态
            current_state = individual.current_disease_state
            anatomical_location = kwargs.get("anatomical_location")
            
            # 计算检测概率
            detection_probability = self.calculate_detection_probability(
                individual=individual,
                current_state=current_state,
                anatomical_location=anatomical_location
            )
            
            # 计算特异性
            specificity = self.calculate_specificity(individual)
            
            # 模拟筛查结果
            if current_state == DiseaseState.NORMAL:
                # 正常状态：基于特异性判断是否假阳性
                if random.random() > specificity:
                    result = ScreeningResult.POSITIVE  # 假阳性
                else:
                    result = ScreeningResult.NEGATIVE  # 真阴性
            else:
                # 疾病状态：基于敏感性判断是否检出
                if random.random() < detection_probability:
                    result = ScreeningResult.POSITIVE  # 真阳性
                else:
                    result = ScreeningResult.NEGATIVE  # 假阴性
            
            # 考虑样本质量问题
            sample_adequacy_rate = kwargs.get("sample_adequacy_rate", 0.95)
            if random.random() > sample_adequacy_rate:
                result = ScreeningResult.INADEQUATE
            
            # 记录筛查日志
            logger.debug(
                f"FIT筛查结果 - 个体ID: {getattr(individual, 'id', 'unknown')}, "
                f"状态: {current_state}, 结果: {result}, "
                f"检测概率: {detection_probability:.3f}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"FIT筛查执行失败: {e}")
            return ScreeningResult.FAILED
    
    def calculate_hemoglobin_concentration(
        self,
        individual: Individual,
        current_state: DiseaseState,
        anatomical_location: Optional[AnatomicalLocation] = None
    ) -> float:
        """
        计算粪便血红蛋白浓度 (ng/mL)
        
        Args:
            individual: 个体对象
            current_state: 当前疾病状态
            anatomical_location: 解剖位置
            
        Returns:
            float: 血红蛋白浓度
        """
        # 基础血红蛋白浓度（基于疾病状态）
        base_concentrations = {
            DiseaseState.NORMAL: 5.0,
            DiseaseState.LOW_RISK_ADENOMA: 25.0,
            DiseaseState.HIGH_RISK_ADENOMA: 80.0,
            DiseaseState.SMALL_SERRATED: 15.0,
            DiseaseState.LARGE_SERRATED: 50.0,
            DiseaseState.PRECLINICAL_CANCER: 200.0,
            DiseaseState.CLINICAL_CANCER_STAGE_I: 350.0,
            DiseaseState.CLINICAL_CANCER_STAGE_II: 500.0,
            DiseaseState.CLINICAL_CANCER_STAGE_III: 750.0,
            DiseaseState.CLINICAL_CANCER_STAGE_IV: 1000.0,
            DiseaseState.CLINICAL_CANCER: 400.0,  # 向后兼容
        }
        
        base_concentration = base_concentrations.get(current_state, 5.0)
        
        # 应用解剖位置调整
        location_factor = self._calculate_location_adjustment(anatomical_location)

        # 添加随机变异
        random_factor = random.lognormvariate(0, 0.5)  # 对数正态分布变异

        final_concentration = (
            base_concentration *
            location_factor *
            random_factor
        )

        # 注释掉的高级调整（暂时不使用）
        # age_factor = self._calculate_age_adjustment(individual.get_current_age())
        # gender_factor = self._calculate_gender_adjustment(individual.gender)
        
        return max(0.0, final_concentration)
    
    def is_positive_by_threshold(
        self,
        hemoglobin_concentration: float
    ) -> bool:
        """
        基于血红蛋白浓度阈值判断是否阳性
        
        Args:
            hemoglobin_concentration: 血红蛋白浓度 (ng/mL)
            
        Returns:
            bool: 是否阳性
        """
        return hemoglobin_concentration >= self.hemoglobin_threshold
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取FIT工具性能指标"""
        return {
            "tool_type": "FIT",
            "specificity": self.performance.specificity,
            "sensitivity_cancer": self.performance.sensitivity_by_state.get(
                DiseaseState.CLINICAL_CANCER_STAGE_I, 0.0
            ),
            "sensitivity_high_risk_adenoma": self.performance.sensitivity_by_state.get(
                DiseaseState.HIGH_RISK_ADENOMA, 0.0
            ),
            "detection_threshold_ng_ml": self.hemoglobin_threshold,
            "turnaround_time_days": self.characteristics.turnaround_time_days,
            "invasiveness": self.characteristics.invasiveness.value,
            "preparation_required": self.characteristics.preparation_required
        }
    
    def __str__(self) -> str:
        return f"FIT工具 (阈值: {self.hemoglobin_threshold} ng/mL, 特异性: {self.performance.specificity:.1%})"
