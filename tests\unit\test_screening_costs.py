"""
筛查成本系统单元测试
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, mock_open

from src.modules.economics.screening_costs import (
    ScreeningCostModel,
    ScreeningTool,
    CostComponent,
    CostBreakdown,
    RegionalAdjustment,
    ScreeningCostConfig
)


class TestCostBreakdown:
    """成本分解测试"""
    
    def test_total_direct_cost_calculation(self):
        """测试总直接成本计算"""
        cost_breakdown = CostBreakdown(
            professional_fee=100.0,
            facility_fee=50.0,
            equipment_cost=30.0,
            material_cost=20.0,
            processing_cost=40.0,
            interpretation_cost=10.0
        )
        
        assert cost_breakdown.total_direct_cost == 250.0
    
    def test_zero_costs(self):
        """测试零成本情况"""
        cost_breakdown = CostBreakdown()
        assert cost_breakdown.total_direct_cost == 0.0


class TestRegionalAdjustment:
    """地区调整测试"""
    
    def test_apply_adjustment(self):
        """测试地区调整应用"""
        base_cost = CostBreakdown(
            professional_fee=100.0,
            facility_fee=50.0,
            equipment_cost=30.0,
            material_cost=20.0,
            processing_cost=40.0,
            interpretation_cost=10.0
        )
        
        adjustment = RegionalAdjustment(
            region_name="beijing",
            cost_multiplier=1.2,
            labor_cost_index=1.5,
            facility_cost_index=1.3,
            equipment_cost_index=1.1
        )
        
        adjusted_cost = adjustment.apply_adjustment(base_cost)
        
        # 验证调整后的成本
        assert adjusted_cost.professional_fee == 150.0  # 100 * 1.5
        assert adjusted_cost.facility_fee == 65.0       # 50 * 1.3
        assert adjusted_cost.equipment_cost == 33.0     # 30 * 1.1
        assert adjusted_cost.material_cost == 24.0      # 20 * 1.2
        assert adjusted_cost.processing_cost == 48.0    # 40 * 1.2
        assert adjusted_cost.interpretation_cost == 15.0 # 10 * 1.5


class TestScreeningCostModel:
    """筛查成本模型测试"""
    
    @pytest.fixture
    def sample_config_data(self):
        """示例配置数据"""
        return {
            'screening_costs': {
                'version': '2023.1',
                'currency': 'CNY',
                'base_year': 2023,
                'region': 'China',
                'fit': {
                    'professional_fee': 15.0,
                    'facility_fee': 10.0,
                    'equipment_cost': 5.0,
                    'material_cost': 8.0,
                    'processing_cost': 12.0,
                    'interpretation_cost': 5.0,
                    'source': 'Test data',
                    'confidence_interval': [40.0, 65.0],
                    'regional_adjustments': {
                        'beijing': {
                            'cost_multiplier': 1.3,
                            'labor_cost_index': 1.5,
                            'facility_cost_index': 1.4,
                            'equipment_cost_index': 1.2
                        }
                    }
                },
                'colonoscopy': {
                    'professional_fee': 300.0,
                    'facility_fee': 200.0,
                    'equipment_cost': 50.0,
                    'material_cost': 30.0,
                    'processing_cost': 80.0,
                    'interpretation_cost': 40.0
                }
            }
        }
    
    @pytest.fixture
    def cost_model(self, sample_config_data):
        """创建成本模型实例"""
        model = ScreeningCostModel()
        model._parse_config_data(sample_config_data)
        return model
    
    def test_initialization(self):
        """测试初始化"""
        model = ScreeningCostModel()
        assert len(model.cost_configs) == 0
        assert model.default_currency == "CNY"
        assert model.default_base_year == 2023
    
    def test_parse_config_data(self, cost_model, sample_config_data):
        """测试配置数据解析"""
        assert len(cost_model.cost_configs) == 2
        assert ScreeningTool.FIT in cost_model.cost_configs
        assert ScreeningTool.COLONOSCOPY in cost_model.cost_configs
        
        # 验证FIT配置
        fit_config = cost_model.cost_configs[ScreeningTool.FIT]
        assert fit_config.base_costs.professional_fee == 15.0
        assert fit_config.base_costs.total_direct_cost == 55.0
        assert 'beijing' in fit_config.regional_adjustments
    
    def test_get_screening_cost_basic(self, cost_model):
        """测试基础筛查成本获取"""
        cost = cost_model.get_screening_cost(ScreeningTool.FIT)
        assert cost == 55.0  # 总直接成本
        
        # 测试数量
        cost_multiple = cost_model.get_screening_cost(ScreeningTool.FIT, quantity=3)
        assert cost_multiple == 165.0  # 55 * 3
    
    def test_get_screening_cost_with_region(self, cost_model):
        """测试带地区调整的筛查成本"""
        cost_beijing = cost_model.get_screening_cost(ScreeningTool.FIT, region='beijing')
        cost_base = cost_model.get_screening_cost(ScreeningTool.FIT)
        
        # 北京成本应该高于基础成本
        assert cost_beijing > cost_base
    
    def test_get_screening_cost_unknown_tool(self, cost_model):
        """测试未知工具错误"""
        with pytest.raises(ValueError, match="未配置筛查工具成本"):
            cost_model.get_screening_cost(ScreeningTool.STOOL_DNA)
    
    def test_get_cost_breakdown(self, cost_model):
        """测试成本分解获取"""
        breakdown = cost_model.get_cost_breakdown(ScreeningTool.FIT)
        assert isinstance(breakdown, CostBreakdown)
        assert breakdown.professional_fee == 15.0
        assert breakdown.total_direct_cost == 55.0
    
    def test_calculate_batch_costs(self, cost_model):
        """测试批量成本计算"""
        requests = [
            {'tool': ScreeningTool.FIT, 'quantity': 2, 'patient_id': 'P001'},
            {'tool': 'colonoscopy', 'quantity': 1, 'region': 'beijing'},
            {'tool': ScreeningTool.FIT, 'quantity': 3, 'region': 'beijing'}
        ]
        
        results = cost_model.calculate_batch_costs(requests)
        
        # 验证结果结构
        assert 'total_cost' in results
        assert 'cost_by_tool' in results
        assert 'cost_by_region' in results
        assert 'individual_costs' in results
        assert 'summary' in results
        
        # 验证汇总信息
        assert results['summary']['total_screenings'] == 6  # 2 + 1 + 3
        assert len(results['summary']['unique_tools']) == 2
        assert len(results['individual_costs']) == 3
    
    def test_add_cost_config(self, cost_model):
        """测试添加成本配置"""
        new_config = ScreeningCostConfig(
            tool=ScreeningTool.STOOL_DNA,
            base_costs=CostBreakdown(professional_fee=50.0, material_cost=200.0)
        )
        
        cost_model.add_cost_config(ScreeningTool.STOOL_DNA, new_config)
        assert ScreeningTool.STOOL_DNA in cost_model.cost_configs
    
    def test_validate_cost_config_valid(self, cost_model):
        """测试有效配置验证"""
        result = cost_model.validate_cost_config(ScreeningTool.FIT)
        assert result['valid'] is True
        assert len(result['errors']) == 0
    
    def test_validate_cost_config_invalid(self):
        """测试无效配置验证"""
        model = ScreeningCostModel()
        
        # 测试未配置的工具
        result = model.validate_cost_config(ScreeningTool.FIT)
        assert result['valid'] is False
        assert len(result['errors']) > 0
    
    def test_get_available_tools(self, cost_model):
        """测试获取可用工具"""
        tools = cost_model.get_available_tools()
        assert len(tools) == 2
        assert ScreeningTool.FIT in tools
        assert ScreeningTool.COLONOSCOPY in tools
    
    def test_get_available_regions(self, cost_model):
        """测试获取可用地区"""
        regions = cost_model.get_available_regions(ScreeningTool.FIT)
        assert 'beijing' in regions
        
        # 测试无地区调整的工具
        regions_colonoscopy = cost_model.get_available_regions(ScreeningTool.COLONOSCOPY)
        assert len(regions_colonoscopy) == 0
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('yaml.safe_load')
    def test_load_cost_config_yaml(self, mock_yaml_load, mock_file, sample_config_data):
        """测试YAML配置文件加载"""
        mock_yaml_load.return_value = sample_config_data
        
        model = ScreeningCostModel()
        model.load_cost_config('test_config.yaml')
        
        mock_file.assert_called_once()
        mock_yaml_load.assert_called_once()
        assert len(model.cost_configs) == 2
    
    def test_load_cost_config_invalid_format(self):
        """测试无效格式配置文件"""
        model = ScreeningCostModel()
        
        with pytest.raises(ValueError, match="不支持的配置文件格式"):
            model.load_cost_config('test_config.txt')
