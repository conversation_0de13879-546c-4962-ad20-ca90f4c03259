#!/usr/bin/env python3
"""
筛查工具配置系统演示

展示筛查工具系统的主要功能，包括：
- 工具创建和配置
- 性能参数计算
- 成本分析
- 配置管理
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.enums import DiseaseState, AnatomicalLocation, Gender
from src.core.individual import Individual
from src.modules.screening import (
    ScreeningToolType, ScreeningResult, ScreeningToolFactory,
    ScreeningToolConfigManager, ScreeningToolConfigTemplates,
    ScreeningCostModel, CostItem, CostComponent, CostCategory
)


def create_sample_individual():
    """创建样本个体"""
    individual = Individual(
        birth_year=1970,
        gender=Gender.MALE
    )
    individual.current_disease_state = DiseaseState.HIGH_RISK_ADENOMA
    return individual


def demo_basic_tool_usage():
    """演示基础工具使用"""
    print("=== 基础工具使用演示 ===")
    
    # 创建个体
    individual = create_sample_individual()
    print(f"个体信息: {individual}")
    
    # 创建FIT工具
    fit_tool = ScreeningToolFactory.create_tool(ScreeningToolType.FIT)
    print(f"\nFIT工具: {fit_tool}")
    
    # 计算检测概率
    detection_prob = fit_tool.calculate_detection_probability(
        individual,
        DiseaseState.HIGH_RISK_ADENOMA,
        AnatomicalLocation.DISTAL_COLON
    )
    print(f"FIT检测高风险腺瘤的概率: {detection_prob:.3f}")
    
    # 执行筛查
    result = fit_tool.perform_screening(individual)
    print(f"FIT筛查结果: {result.display_name}")
    
    # 创建结肠镜工具
    colonoscopy_tool = ScreeningToolFactory.create_tool(ScreeningToolType.COLONOSCOPY)
    print(f"\n结肠镜工具: {colonoscopy_tool}")
    
    # 计算检测概率
    detection_prob_colonoscopy = colonoscopy_tool.calculate_detection_probability(
        individual,
        DiseaseState.HIGH_RISK_ADENOMA,
        AnatomicalLocation.DISTAL_COLON
    )
    print(f"结肠镜检测高风险腺瘤的概率: {detection_prob_colonoscopy:.3f}")
    
    print()


def demo_configuration_management():
    """演示配置管理"""
    print("=== 配置管理演示 ===")
    
    # 创建临时配置目录
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        config_manager = ScreeningToolConfigManager(temp_dir)
        
        # 创建FIT配置模板
        fit_template = ScreeningToolConfigTemplates.create_fit_template()
        print("创建FIT配置模板")
        
        # 保存配置
        config_path = config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            fit_template,
            "demo_fit_config.yaml"
        )
        print(f"配置已保存到: {config_path}")
        
        # 从配置创建工具
        fit_tool = config_manager.create_tool_from_config(
            ScreeningToolType.FIT,
            "demo_fit_config.yaml"
        )
        print(f"从配置创建的FIT工具: {fit_tool}")
        
        # 列出可用配置
        configurations = config_manager.list_available_configurations()
        print(f"可用配置数量: {len(configurations)}")
        for config in configurations:
            print(f"  - {config['file_name']}: {config['tool_name']}")
    
    print()


def demo_cost_analysis():
    """演示成本分析"""
    print("=== 成本分析演示 ===")
    
    # 创建个体
    individual = create_sample_individual()
    
    # 创建FIT成本模型
    fit_cost_items = [
        CostItem(
            component=CostComponent.PROCEDURE_FEE,
            category=CostCategory.DIRECT_MEDICAL,
            base_cost=25.0,
            description="FIT检查费用"
        ),
        CostItem(
            component=CostComponent.TIME_COST,
            category=CostCategory.INDIRECT,
            base_cost=50.0,
            description="时间成本"
        )
    ]
    
    fit_cost_model = ScreeningCostModel(
        tool_type=ScreeningToolType.FIT,
        cost_items=fit_cost_items
    )
    
    # 计算成本
    fit_cost = fit_cost_model.calculate_total_cost(individual)
    print(f"FIT总成本: {fit_cost['total_cost']:.2f}元")
    print(f"  - 直接医疗成本: {fit_cost['direct_medical_total']:.2f}元")
    print(f"  - 间接成本: {fit_cost['indirect_total']:.2f}元")
    
    # 创建结肠镜成本模型
    colonoscopy_cost_items = [
        CostItem(
            component=CostComponent.PROCEDURE_FEE,
            category=CostCategory.DIRECT_MEDICAL,
            base_cost=800.0,
            description="结肠镜检查费用"
        ),
        CostItem(
            component=CostComponent.ANESTHESIA_COST,
            category=CostCategory.DIRECT_MEDICAL,
            base_cost=200.0,
            description="麻醉费用"
        ),
        CostItem(
            component=CostComponent.TIME_COST,
            category=CostCategory.INDIRECT,
            base_cost=400.0,
            description="时间成本"
        )
    ]
    
    colonoscopy_cost_model = ScreeningCostModel(
        tool_type=ScreeningToolType.COLONOSCOPY,
        cost_items=colonoscopy_cost_items
    )
    
    # 计算成本
    colonoscopy_cost = colonoscopy_cost_model.calculate_total_cost(individual)
    print(f"\n结肠镜总成本: {colonoscopy_cost['total_cost']:.2f}元")
    print(f"  - 直接医疗成本: {colonoscopy_cost['direct_medical_total']:.2f}元")
    print(f"  - 间接成本: {colonoscopy_cost['indirect_total']:.2f}元")
    
    print()


def demo_tool_comparison():
    """演示工具比较"""
    print("=== 工具比较演示 ===")
    
    # 创建个体
    individual = create_sample_individual()
    
    # 比较不同工具的性能
    tools = {
        "FIT": ScreeningToolFactory.create_tool(ScreeningToolType.FIT),
        "结肠镜": ScreeningToolFactory.create_tool(ScreeningToolType.COLONOSCOPY),
        "乙状结肠镜": ScreeningToolFactory.create_tool(ScreeningToolType.SIGMOIDOSCOPY)
    }
    
    print("高风险腺瘤检测概率比较:")
    for tool_name, tool in tools.items():
        prob = tool.calculate_detection_probability(
            individual,
            DiseaseState.HIGH_RISK_ADENOMA,
            AnatomicalLocation.DISTAL_COLON
        )
        print(f"  {tool_name}: {prob:.3f}")
    
    print("\n工具特性比较:")
    for tool_name, tool in tools.items():
        print(f"  {tool_name}:")
        print(f"    - 侵入性: {tool.characteristics.invasiveness.display_name}")
        print(f"    - 需要准备: {'是' if tool.characteristics.preparation_required else '否'}")
        print(f"    - 需要镇静: {'是' if tool.characteristics.requires_sedation else '否'}")
        print(f"    - 周转时间: {tool.characteristics.turnaround_time_days}天")
    
    print()


def demo_location_specific_detection():
    """演示位置特异性检测"""
    print("=== 位置特异性检测演示 ===")
    
    # 创建个体
    individual = create_sample_individual()
    
    # 创建乙状结肠镜工具（无法检测近端结肠）
    sigmoidoscopy_tool = ScreeningToolFactory.create_tool(ScreeningToolType.SIGMOIDOSCOPY)
    
    locations = [
        (AnatomicalLocation.PROXIMAL_COLON, "近端结肠"),
        (AnatomicalLocation.DISTAL_COLON, "远端结肠"),
        (AnatomicalLocation.RECTUM, "直肠")
    ]
    
    print("乙状结肠镜在不同位置的检测能力:")
    for location, location_name in locations:
        prob = sigmoidoscopy_tool.calculate_detection_probability(
            individual,
            DiseaseState.HIGH_RISK_ADENOMA,
            location
        )
        print(f"  {location_name}: {prob:.3f}")
    
    print()


def main():
    """主函数"""
    print("筛查工具配置系统演示")
    print("=" * 50)
    
    try:
        demo_basic_tool_usage()
        demo_configuration_management()
        demo_cost_analysis()
        demo_tool_comparison()
        demo_location_specific_detection()
        
        print("演示完成！")
        print("\n系统功能总结:")
        print("✅ 筛查工具创建和配置")
        print("✅ 疾病状态特异性性能参数")
        print("✅ 解剖位置特异性检测")
        print("✅ 成本建模和分析")
        print("✅ 配置文件管理")
        print("✅ 工具性能比较")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
