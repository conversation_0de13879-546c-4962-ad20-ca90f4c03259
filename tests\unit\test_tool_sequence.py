"""
顺序筛查工具管理测试

测试筛查工具序列的调度、执行和状态跟踪功能。
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from src.core.individual import Individual
from src.core.enums import Gender, DiseaseState
from src.modules.screening.tool_sequence import (
    ToolSequence, ScheduledScreening, SequenceConstraint,
    SchedulingStatus, TriggerCondition
)
from src.modules.screening.strategy import ScreeningStrategy, ScreeningInterval, ScreeningFrequency
from src.modules.screening.enums import ScreeningToolType, ScreeningResult


class TestScheduledScreening:
    """测试计划筛查事件"""
    
    def test_scheduled_screening_creation(self):
        """测试计划筛查创建"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        scheduled = ScheduledScreening(
            individual_id="test_id",
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        assert scheduled.individual_id == "test_id"
        assert scheduled.tool_type == ScreeningToolType.FIT
        assert scheduled.scheduled_time == 2025.0
        assert scheduled.status == SchedulingStatus.PENDING
        assert scheduled.priority == 1
    
    def test_is_due(self):
        """测试到期检查"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        scheduled = ScheduledScreening(
            individual_id="test_id",
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        assert not scheduled.is_due(2024.0)
        assert scheduled.is_due(2025.0)
        assert scheduled.is_due(2026.0)
    
    def test_can_execute(self):
        """测试执行条件检查"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        scheduled = ScheduledScreening(
            individual_id="test_id",
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval,
            status=SchedulingStatus.SCHEDULED
        )
        
        assert not scheduled.can_execute(2024.0)  # 未到期
        assert scheduled.can_execute(2025.0)     # 到期且状态正确
        
        scheduled.status = SchedulingStatus.COMPLETED
        assert not scheduled.can_execute(2025.0)  # 状态不正确
    
    def test_mark_completed(self):
        """测试标记完成"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        scheduled = ScheduledScreening(
            individual_id="test_id",
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        result = Mock()
        scheduled.mark_completed(result, 2025.5)
        
        assert scheduled.status == SchedulingStatus.COMPLETED
        assert scheduled.result == result
        assert scheduled.execution_time == 2025.5
    
    def test_mark_failed(self):
        """测试标记失败"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        scheduled = ScheduledScreening(
            individual_id="test_id",
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        scheduled.mark_failed("测试错误", 2025.5)
        
        assert scheduled.status == SchedulingStatus.FAILED
        assert scheduled.error_message == "测试错误"
        assert scheduled.execution_time == 2025.5
    
    def test_to_dict(self):
        """测试字典转换"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        scheduled = ScheduledScreening(
            individual_id="test_id",
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval,
            trigger_condition=TriggerCondition.TIME_INTERVAL
        )
        
        data = scheduled.to_dict()
        
        assert data["individual_id"] == "test_id"
        assert data["tool_type"] == "fecal_immunochemical_test"
        assert data["scheduled_time"] == 2025.0
        assert data["trigger_condition"] == "time_interval"


class TestSequenceConstraint:
    """测试序列约束"""
    
    def test_constraint_creation(self):
        """测试约束创建"""
        constraint = SequenceConstraint(
            predecessor_tool=ScreeningToolType.FIT,
            successor_tool=ScreeningToolType.COLONOSCOPY,
            min_time_gap=0.0,
            max_time_gap=0.5,
            required_result=ScreeningResult.POSITIVE
        )
        
        assert constraint.predecessor_tool == ScreeningToolType.FIT
        assert constraint.successor_tool == ScreeningToolType.COLONOSCOPY
        assert constraint.min_time_gap == 0.0
        assert constraint.max_time_gap == 0.5
        assert constraint.required_result == ScreeningResult.POSITIVE
    
    def test_validate_timing(self):
        """测试时间约束验证"""
        constraint = SequenceConstraint(
            predecessor_tool=ScreeningToolType.FIT,
            successor_tool=ScreeningToolType.COLONOSCOPY,
            min_time_gap=0.1,
            max_time_gap=0.5
        )
        
        assert not constraint.validate_timing(2025.0, 2025.05)  # 间隔太短
        assert constraint.validate_timing(2025.0, 2025.2)       # 间隔合适
        assert not constraint.validate_timing(2025.0, 2025.6)   # 间隔太长
    
    def test_validate_conditions(self):
        """测试条件约束验证"""
        constraint = SequenceConstraint(
            predecessor_tool=ScreeningToolType.FIT,
            successor_tool=ScreeningToolType.COLONOSCOPY,
            required_result=ScreeningResult.POSITIVE
        )
        
        # 模拟阳性结果
        positive_result = Mock()
        positive_result.result_type = ScreeningResult.POSITIVE
        
        # 模拟阴性结果
        negative_result = Mock()
        negative_result.result_type = ScreeningResult.NEGATIVE
        
        assert constraint.validate_conditions(positive_result, {})
        assert not constraint.validate_conditions(negative_result, {})


class TestToolSequence:
    """测试工具序列管理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.strategy = ScreeningStrategy(name="测试策略")
        
        # 添加FIT筛查间隔
        fit_interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL
        )
        self.strategy.add_interval(fit_interval)
        
        # 添加结肠镜筛查间隔
        colonoscopy_interval = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.CUSTOM,
            custom_frequency_years=10.0
        )
        self.strategy.add_interval(colonoscopy_interval)
        
        self.tool_sequence = ToolSequence(self.strategy)
        
        # 创建测试个体
        self.individual = Individual(
            birth_year=1970,
            gender=Gender.MALE,
            individual_id="test_individual"
        )
    
    def test_tool_sequence_creation(self):
        """测试工具序列管理器创建"""
        assert self.tool_sequence.strategy == self.strategy
        assert len(self.tool_sequence.execution_queue) == 0
        assert len(self.tool_sequence.completed_screenings) == 0
        assert len(self.tool_sequence.constraints) > 0  # 应该有默认约束
    
    def test_add_constraint(self):
        """测试添加约束"""
        initial_count = len(self.tool_sequence.constraints)
        
        constraint = SequenceConstraint(
            predecessor_tool=ScreeningToolType.SIGMOIDOSCOPY,
            successor_tool=ScreeningToolType.COLONOSCOPY
        )
        
        self.tool_sequence.add_constraint(constraint)
        
        assert len(self.tool_sequence.constraints) == initial_count + 1
        assert constraint in self.tool_sequence.constraints
    
    def test_schedule_screening(self):
        """测试安排筛查"""
        interval = self.strategy.intervals[0]  # FIT间隔
        
        scheduled = self.tool_sequence.schedule_screening(
            individual=self.individual,
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval,
            trigger_condition=TriggerCondition.TIME_INTERVAL
        )
        
        assert scheduled.individual_id == self.individual.individual_id
        assert scheduled.tool_type == ScreeningToolType.FIT
        assert scheduled.status == SchedulingStatus.SCHEDULED
        assert len(self.tool_sequence.execution_queue) == 1
        assert self.tool_sequence.execution_statistics["total_scheduled"] == 1
    
    @patch('src.modules.screening.tool_sequence.ScreeningToolFactory.create_tool')
    def test_execute_due_screenings(self, mock_create_tool):
        """测试执行到期筛查"""
        # 模拟筛查工具
        mock_tool = Mock()
        mock_result = Mock()
        mock_result.result_type = ScreeningResult.NEGATIVE
        mock_tool.perform_screening.return_value = mock_result
        mock_create_tool.return_value = mock_tool
        
        # 安排筛查
        interval = self.strategy.intervals[0]
        self.tool_sequence.schedule_screening(
            individual=self.individual,
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        # 执行到期筛查
        executed = self.tool_sequence.execute_due_screenings(2025.0)
        
        assert len(executed) == 1
        assert executed[0].status == SchedulingStatus.COMPLETED
        assert executed[0].result == mock_result
        assert len(self.tool_sequence.execution_queue) == 0
        assert len(self.tool_sequence.completed_screenings) == 1
        assert self.tool_sequence.execution_statistics["total_completed"] == 1
    
    @patch('src.modules.screening.tool_sequence.ScreeningToolFactory.create_tool')
    def test_execute_screening_failure(self, mock_create_tool):
        """测试筛查执行失败"""
        # 模拟筛查工具抛出异常
        mock_tool = Mock()
        mock_tool.perform_screening.side_effect = Exception("筛查失败")
        mock_create_tool.return_value = mock_tool
        
        # 安排筛查
        interval = self.strategy.intervals[0]
        self.tool_sequence.schedule_screening(
            individual=self.individual,
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        # 执行到期筛查
        executed = self.tool_sequence.execute_due_screenings(2025.0)
        
        assert len(executed) == 1
        assert executed[0].status == SchedulingStatus.FAILED
        assert executed[0].error_message == "筛查失败"
        assert self.tool_sequence.execution_statistics["total_failed"] == 1
    
    def test_schedule_next_screening(self):
        """测试安排下一次筛查"""
        # 设置个体年龄在筛查范围内
        current_time = 2025.0
        
        self.tool_sequence.schedule_next_screening(self.individual, current_time)
        
        # 应该为符合条件的间隔安排筛查
        assert len(self.tool_sequence.execution_queue) > 0
    
    def test_get_individual_history(self):
        """测试获取个体历史"""
        interval = self.strategy.intervals[0]
        
        # 安排筛查
        self.tool_sequence.schedule_screening(
            individual=self.individual,
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        history = self.tool_sequence.get_individual_history(self.individual.individual_id)
        
        assert len(history) == 1
        assert history[0].individual_id == self.individual.individual_id
    
    def test_get_pending_screenings(self):
        """测试获取待执行筛查"""
        interval = self.strategy.intervals[0]
        
        # 安排筛查
        self.tool_sequence.schedule_screening(
            individual=self.individual,
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        pending = self.tool_sequence.get_pending_screenings()
        
        assert len(pending) == 1
        assert pending[0].status == SchedulingStatus.SCHEDULED
    
    def test_cancel_screening(self):
        """测试取消筛查"""
        interval = self.strategy.intervals[0]
        
        # 安排筛查
        scheduled = self.tool_sequence.schedule_screening(
            individual=self.individual,
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=interval
        )
        
        # 取消筛查
        result = self.tool_sequence.cancel_screening(scheduled.scheduling_id)
        
        assert result is True
        assert scheduled.status == SchedulingStatus.CANCELLED
        assert self.tool_sequence.execution_statistics["total_cancelled"] == 1
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        stats = self.tool_sequence.get_statistics()
        
        assert "total_scheduled" in stats
        assert "total_completed" in stats
        assert "total_failed" in stats
        assert "total_cancelled" in stats
        assert isinstance(stats, dict)
    
    @patch('src.modules.screening.tool_sequence.ScreeningToolFactory.create_tool')
    def test_positive_result_followup(self, mock_create_tool):
        """测试阳性结果后续行动"""
        # 模拟FIT阳性结果
        mock_tool = Mock()
        mock_result = Mock()
        mock_result.result_type = ScreeningResult.POSITIVE
        mock_tool.perform_screening.return_value = mock_result
        mock_create_tool.return_value = mock_tool
        
        # 安排FIT筛查
        fit_interval = self.strategy.get_intervals_by_tool(ScreeningToolType.FIT)[0]
        self.tool_sequence.schedule_screening(
            individual=self.individual,
            tool_type=ScreeningToolType.FIT,
            scheduled_time=2025.0,
            interval_config=fit_interval
        )
        
        # 执行筛查
        executed = self.tool_sequence.execute_due_screenings(2025.0)
        
        # 检查是否触发了结肠镜检查
        assert len(executed) == 1
        assert executed[0].result.result_type == ScreeningResult.POSITIVE
        
        # 应该有新的结肠镜筛查被安排
        pending = self.tool_sequence.get_pending_screenings()
        colonoscopy_screenings = [
            s for s in pending 
            if s.tool_type == ScreeningToolType.COLONOSCOPY
        ]
        assert len(colonoscopy_screenings) > 0
