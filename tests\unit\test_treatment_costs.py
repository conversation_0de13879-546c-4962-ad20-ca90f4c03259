"""
治疗成本系统单元测试
"""

import pytest
from unittest.mock import patch, mock_open

from src.modules.economics.treatment_costs import (
    TreatmentCostModel,
    CancerStage,
    TreatmentModality,
    ComplicationType,
    TreatmentCost,
    TreatmentProtocol,
    ComplicationCost
)


class TestTreatmentCost:
    """治疗成本结构测试"""
    
    def test_total_cost_calculation(self):
        """测试总成本计算"""
        treatment_cost = TreatmentCost(
            initial_cost=10000.0,
            monthly_cost=2000.0,
            followup_cost=500.0,
            complication_cost=1500.0
        )
        
        assert treatment_cost.total_cost == 14000.0
    
    def test_zero_costs(self):
        """测试零成本情况"""
        treatment_cost = TreatmentCost()
        assert treatment_cost.total_cost == 0.0


class TestTreatmentCostModel:
    """治疗成本模型测试"""
    
    @pytest.fixture
    def sample_config_data(self):
        """示例配置数据"""
        return {
            'treatment_costs': {
                'version': '2023.1',
                'currency': 'CNY',
                'base_year': 2023,
                'stage_specific_costs': {
                    'stage_i': {
                        'initial_cost': 45000.0,
                        'monthly_cost': 2000.0,
                        'followup_cost': 1500.0,
                        'complication_cost': 8000.0
                    },
                    'stage_ii': {
                        'initial_cost': 55000.0,
                        'monthly_cost': 8000.0,
                        'followup_cost': 2000.0,
                        'complication_cost': 12000.0
                    }
                },
                'modality_costs': {
                    'surgery': 35000.0,
                    'chemotherapy': 8000.0,
                    'radiotherapy': 15000.0
                },
                'complication_costs': {
                    'surgical_complications': {
                        'treatment_cost': 15000.0,
                        'duration_days': 7.0,
                        'probability': 0.05
                    },
                    'chemotherapy_toxicity': {
                        'treatment_cost': 8000.0,
                        'duration_days': 3.0,
                        'probability': 0.15
                    }
                }
            }
        }
    
    @pytest.fixture
    def treatment_model(self, sample_config_data):
        """创建治疗成本模型实例"""
        model = TreatmentCostModel()
        model._parse_treatment_config(sample_config_data)
        return model
    
    def test_initialization(self):
        """测试初始化"""
        model = TreatmentCostModel()
        assert len(model.stage_specific_costs) == 0
        assert len(model.modality_costs) == 0
        assert len(model.complication_costs) == 0
        assert model.default_currency == "CNY"
        assert model.default_base_year == 2023
    
    def test_parse_treatment_config(self, treatment_model):
        """测试配置数据解析"""
        # 验证分期成本
        assert len(treatment_model.stage_specific_costs) == 2
        assert CancerStage.STAGE_I in treatment_model.stage_specific_costs
        assert CancerStage.STAGE_II in treatment_model.stage_specific_costs
        
        stage_i_cost = treatment_model.stage_specific_costs[CancerStage.STAGE_I]
        assert stage_i_cost.initial_cost == 45000.0
        assert stage_i_cost.monthly_cost == 2000.0
        
        # 验证治疗方式成本
        assert len(treatment_model.modality_costs) == 3
        assert TreatmentModality.SURGERY in treatment_model.modality_costs
        assert treatment_model.modality_costs[TreatmentModality.SURGERY] == 35000.0
        
        # 验证并发症成本
        assert len(treatment_model.complication_costs) == 2
        assert ComplicationType.SURGICAL_COMPLICATIONS in treatment_model.complication_costs
    
    def test_calculate_treatment_cost_basic(self, treatment_model):
        """测试基础治疗成本计算"""
        cost = treatment_model.calculate_treatment_cost(CancerStage.STAGE_I)
        
        # 基础成本：初始成本 + 12个月月度成本 + 随访成本
        expected_cost = 45000.0 + (2000.0 * 12.0) + 1500.0
        assert cost == expected_cost
    
    def test_calculate_treatment_cost_with_duration(self, treatment_model):
        """测试带持续时间的治疗成本计算"""
        cost = treatment_model.calculate_treatment_cost(
            CancerStage.STAGE_I, 
            treatment_duration_months=6.0
        )
        
        expected_cost = 45000.0 + (2000.0 * 6.0) + 1500.0
        assert cost == expected_cost
    
    def test_calculate_treatment_cost_with_modalities(self, treatment_model):
        """测试带治疗方式的成本计算"""
        cost = treatment_model.calculate_treatment_cost(
            CancerStage.STAGE_I,
            treatment_modalities=[TreatmentModality.SURGERY, TreatmentModality.CHEMOTHERAPY]
        )
        
        # 基础成本 + 手术成本 + 化疗成本
        expected_cost = (45000.0 + (2000.0 * 12.0) + 1500.0) + 35000.0 + 8000.0
        assert cost == expected_cost
    
    def test_calculate_treatment_cost_with_complications(self, treatment_model):
        """测试带并发症的成本计算"""
        cost = treatment_model.calculate_treatment_cost(
            CancerStage.STAGE_I,
            complications=[ComplicationType.SURGICAL_COMPLICATIONS]
        )
        
        # 基础成本 + 并发症成本
        expected_cost = (45000.0 + (2000.0 * 12.0) + 1500.0) + 15000.0
        assert cost == expected_cost
    
    def test_calculate_lifetime_treatment_cost(self, treatment_model):
        """测试终生治疗成本计算"""
        result = treatment_model.calculate_lifetime_treatment_cost(
            cancer_stage=CancerStage.STAGE_I,
            diagnosis_age=60,
            life_expectancy=80,
            recurrence_probability=0.05
        )
        
        # 验证结果结构
        assert 'initial_treatment_cost' in result
        assert 'followup_cost' in result
        assert 'recurrence_cost' in result
        assert 'total_lifetime_cost' in result
        assert 'years_of_followup' in result
        
        # 验证随访年数
        assert result['years_of_followup'] == 20
        
        # 验证总成本大于初始成本
        assert result['total_lifetime_cost'] > result['initial_treatment_cost']
    
    def test_calculate_expected_complication_cost(self, treatment_model):
        """测试预期并发症成本计算"""
        cost = treatment_model.calculate_expected_complication_cost(
            CancerStage.STAGE_I,
            [TreatmentModality.SURGERY, TreatmentModality.CHEMOTHERAPY]
        )
        
        # 预期成本 = 手术并发症成本 * 概率 + 化疗毒性成本 * 概率
        expected_cost = (15000.0 * 0.05) + (8000.0 * 0.15)
        assert cost == expected_cost
    
    def test_add_treatment_protocol(self, treatment_model):
        """测试添加治疗方案"""
        protocol = TreatmentProtocol(
            stage=CancerStage.STAGE_III,
            modalities=[TreatmentModality.SURGERY, TreatmentModality.CHEMOTHERAPY],
            duration_months=12.0,
            costs=TreatmentCost(initial_cost=60000.0),
            success_rate=0.8
        )
        
        treatment_model.add_treatment_protocol(CancerStage.STAGE_III, protocol)
        assert CancerStage.STAGE_III in treatment_model.treatment_protocols
        assert len(treatment_model.treatment_protocols[CancerStage.STAGE_III]) == 1
    
    def test_update_stage_cost(self, treatment_model):
        """测试更新分期成本"""
        new_cost = TreatmentCost(
            initial_cost=50000.0,
            monthly_cost=2500.0,
            followup_cost=1800.0
        )
        
        treatment_model.update_stage_cost(CancerStage.STAGE_I, new_cost)
        updated_cost = treatment_model.stage_specific_costs[CancerStage.STAGE_I]
        assert updated_cost.initial_cost == 50000.0
        assert updated_cost.monthly_cost == 2500.0
    
    def test_get_available_stages(self, treatment_model):
        """测试获取可用分期"""
        stages = treatment_model.get_available_stages()
        assert len(stages) == 2
        assert CancerStage.STAGE_I in stages
        assert CancerStage.STAGE_II in stages
    
    def test_get_available_modalities(self, treatment_model):
        """测试获取可用治疗方式"""
        modalities = treatment_model.get_available_modalities()
        assert len(modalities) == 3
        assert TreatmentModality.SURGERY in modalities
        assert TreatmentModality.CHEMOTHERAPY in modalities
        assert TreatmentModality.RADIOTHERAPY in modalities
    
    def test_validate_treatment_config_valid(self, treatment_model):
        """测试有效配置验证"""
        result = treatment_model.validate_treatment_config(CancerStage.STAGE_I)
        assert result['valid'] is True
        assert len(result['errors']) == 0
    
    def test_validate_treatment_config_invalid(self):
        """测试无效配置验证"""
        model = TreatmentCostModel()
        
        # 测试未配置的分期
        result = model.validate_treatment_config(CancerStage.STAGE_I)
        assert result['valid'] is False
        assert len(result['errors']) > 0
    
    def test_calculate_cost_by_modality(self, treatment_model):
        """测试按治疗方式计算成本"""
        result = treatment_model.calculate_cost_by_modality(
            [TreatmentModality.SURGERY, TreatmentModality.CHEMOTHERAPY],
            duration_months=6.0
        )
        
        # 验证结果结构
        assert 'surgery' in result
        assert 'chemotherapy' in result
        assert 'total' in result
        
        # 验证成本计算
        assert result['surgery'] == 35000.0  # 手术不受持续时间影响
        assert result['chemotherapy'] == 4000.0  # 化疗按比例调整 (8000 * 6/12)
        assert result['total'] == 39000.0
    
    def test_get_treatment_protocol_basic(self, treatment_model):
        """测试获取治疗方案"""
        # 添加一个测试方案
        protocol = TreatmentProtocol(
            stage=CancerStage.STAGE_I,
            modalities=[TreatmentModality.SURGERY],
            duration_months=1.0,
            costs=TreatmentCost(initial_cost=45000.0),
            success_rate=0.95
        )
        treatment_model.add_treatment_protocol(CancerStage.STAGE_I, protocol)
        
        # 获取方案
        result = treatment_model.get_treatment_protocol(CancerStage.STAGE_I)
        assert result is not None
        assert result.stage == CancerStage.STAGE_I
        assert result.success_rate == 0.95
    
    def test_get_treatment_protocol_age_consideration(self, treatment_model):
        """测试年龄考虑的治疗方案选择"""
        # 添加包含化疗的方案
        protocol_with_chemo = TreatmentProtocol(
            stage=CancerStage.STAGE_I,
            modalities=[TreatmentModality.SURGERY, TreatmentModality.CHEMOTHERAPY],
            duration_months=6.0,
            costs=TreatmentCost(initial_cost=50000.0),
            success_rate=0.97
        )
        treatment_model.add_treatment_protocol(CancerStage.STAGE_I, protocol_with_chemo)
        
        # 高龄患者应该避免化疗
        result = treatment_model.get_treatment_protocol(
            CancerStage.STAGE_I, 
            patient_age=85
        )
        
        # 由于没有其他方案，应该返回默认方案
        assert result is not None
