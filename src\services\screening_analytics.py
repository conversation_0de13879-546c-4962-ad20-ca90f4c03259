"""
筛查结果统计分析服务

实现筛查结果的统计分析、性能指标计算、趋势分析和质量监控功能。
"""

import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
from enum import Enum
import statistics

from src.modules.screening.result_processor import (
    ScreeningResult, ScreeningResultType, FalseResultType
)
from src.modules.screening.enums import ScreeningToolType

logger = logging.getLogger(__name__)


class QualityAlertSeverity(Enum):
    """质量警报严重程度枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class QualityAlert:
    """质量警报数据结构"""
    alert_type: str
    message: str
    severity: QualityAlertSeverity
    timestamp: datetime = field(default_factory=datetime.now)
    affected_count: int = 0
    threshold_value: Optional[float] = None
    actual_value: Optional[float] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    sensitivity: float
    specificity: float
    positive_predictive_value: float
    negative_predictive_value: float
    accuracy: float
    true_positives: int
    true_negatives: int
    false_positives: int
    false_negatives: int
    total_tests: int


@dataclass
class ScreeningStatistics:
    """筛查统计数据结构"""
    total_screenings: int
    positive_rate: float
    negative_rate: float
    indeterminate_rate: float
    inadequate_rate: float
    average_confidence_score: float
    average_quality_score: float
    average_cost: float
    total_cost: float


class ScreeningAnalytics:
    """筛查结果统计分析服务"""
    
    def __init__(self, quality_thresholds: Optional[Dict[str, float]] = None):
        """
        初始化筛查分析服务
        
        Args:
            quality_thresholds: 质量阈值配置
        """
        self.quality_thresholds = quality_thresholds or {
            'max_positivity_rate': 0.15,
            'max_false_positive_rate': 0.10,
            'min_sensitivity': 0.70,
            'min_specificity': 0.85,
            'min_ppv': 0.05,
            'min_confidence_score': 0.70,
            'min_quality_score': 0.80
        }
        
        logger.info("初始化筛查结果统计分析服务")

    def generate_screening_report(
        self, 
        screening_results: List[ScreeningResult],
        time_period: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """
        生成筛查结果分析报告
        
        Args:
            screening_results: 筛查结果列表
            time_period: 时间段过滤 (开始时间, 结束时间)
            
        Returns:
            分析报告字典
        """
        # 过滤时间段
        if time_period:
            start_time, end_time = time_period
            screening_results = [
                r for r in screening_results 
                if start_time <= r.test_date <= end_time
            ]
        
        if not screening_results:
            return {"error": "没有筛查结果数据"}
        
        report = {
            'report_metadata': {
                'generation_time': datetime.now(),
                'total_results': len(screening_results),
                'time_period': time_period,
                'analysis_version': '1.0'
            },
            'summary_statistics': self._calculate_summary_stats(screening_results),
            'performance_metrics': self._calculate_performance_metrics(screening_results),
            'temporal_trends': self._analyze_temporal_trends(screening_results),
            'tool_comparison': self._compare_screening_tools(screening_results),
            'quality_indicators': self._assess_quality_indicators(screening_results),
            'cost_analysis': self._analyze_costs(screening_results),
            'false_result_analysis': self._analyze_false_results(screening_results)
        }
        
        return report

    def _calculate_summary_stats(
        self, 
        screening_results: List[ScreeningResult]
    ) -> ScreeningStatistics:
        """计算汇总统计信息"""
        total = len(screening_results)
        
        if total == 0:
            return ScreeningStatistics(0, 0, 0, 0, 0, 0, 0, 0, 0)
        
        # 按结果类型统计
        positive_count = sum(1 for r in screening_results 
                           if r.result_type == ScreeningResultType.POSITIVE)
        negative_count = sum(1 for r in screening_results 
                           if r.result_type == ScreeningResultType.NEGATIVE)
        indeterminate_count = sum(1 for r in screening_results 
                                if r.result_type == ScreeningResultType.INDETERMINATE)
        inadequate_count = sum(1 for r in screening_results 
                             if r.result_type == ScreeningResultType.INADEQUATE)
        
        # 计算平均值
        avg_confidence = statistics.mean(r.confidence_score for r in screening_results)
        avg_quality = statistics.mean(r.quality_score for r in screening_results)
        avg_cost = statistics.mean(r.cost for r in screening_results)
        total_cost = sum(r.cost for r in screening_results)
        
        return ScreeningStatistics(
            total_screenings=total,
            positive_rate=positive_count / total,
            negative_rate=negative_count / total,
            indeterminate_rate=indeterminate_count / total,
            inadequate_rate=inadequate_count / total,
            average_confidence_score=avg_confidence,
            average_quality_score=avg_quality,
            average_cost=avg_cost,
            total_cost=total_cost
        )

    def _calculate_performance_metrics(
        self, 
        screening_results: List[ScreeningResult]
    ) -> PerformanceMetrics:
        """计算筛查性能指标"""
        # 统计真阳性、真阴性、假阳性、假阴性
        tp = sum(1 for r in screening_results if r.is_true_positive is True)
        tn = sum(1 for r in screening_results if r.is_true_negative is True)
        fp = sum(1 for r in screening_results 
                if r.result_type == ScreeningResultType.POSITIVE and r.is_true_positive is False)
        fn = sum(1 for r in screening_results 
                if r.result_type == ScreeningResultType.NEGATIVE and r.is_true_negative is False)
        
        total = len(screening_results)
        
        # 计算性能指标
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        ppv = tp / (tp + fp) if (tp + fp) > 0 else 0
        npv = tn / (tn + fn) if (tn + fn) > 0 else 0
        accuracy = (tp + tn) / total if total > 0 else 0
        
        return PerformanceMetrics(
            sensitivity=sensitivity,
            specificity=specificity,
            positive_predictive_value=ppv,
            negative_predictive_value=npv,
            accuracy=accuracy,
            true_positives=tp,
            true_negatives=tn,
            false_positives=fp,
            false_negatives=fn,
            total_tests=total
        )

    def _analyze_temporal_trends(
        self, 
        screening_results: List[ScreeningResult]
    ) -> Dict[str, Any]:
        """分析时间趋势"""
        if not screening_results:
            return {}
        
        # 按月分组
        monthly_data = defaultdict(list)
        for result in screening_results:
            month_key = result.test_date.strftime('%Y-%m')
            monthly_data[month_key].append(result)
        
        trends = {
            'monthly_volume': {},
            'monthly_positivity_rate': {},
            'monthly_quality_score': {},
            'monthly_cost': {}
        }
        
        for month, results in monthly_data.items():
            total = len(results)
            positive = sum(1 for r in results 
                          if r.result_type == ScreeningResultType.POSITIVE)
            avg_quality = statistics.mean(r.quality_score for r in results)
            total_cost = sum(r.cost for r in results)
            
            trends['monthly_volume'][month] = total
            trends['monthly_positivity_rate'][month] = positive / total if total > 0 else 0
            trends['monthly_quality_score'][month] = avg_quality
            trends['monthly_cost'][month] = total_cost
        
        return trends

    def _compare_screening_tools(
        self, 
        screening_results: List[ScreeningResult]
    ) -> Dict[str, Any]:
        """比较不同筛查工具的性能"""
        tool_data = defaultdict(list)
        
        for result in screening_results:
            tool_data[result.tool_type.value].append(result)
        
        comparison = {}
        
        for tool_type, results in tool_data.items():
            if not results:
                continue
                
            stats = self._calculate_summary_stats(results)
            metrics = self._calculate_performance_metrics(results)
            
            comparison[tool_type] = {
                'total_tests': len(results),
                'positivity_rate': stats.positive_rate,
                'sensitivity': metrics.sensitivity,
                'specificity': metrics.specificity,
                'ppv': metrics.positive_predictive_value,
                'npv': metrics.negative_predictive_value,
                'average_cost': stats.average_cost,
                'average_quality_score': stats.average_quality_score
            }
        
        return comparison

    def _assess_quality_indicators(
        self, 
        screening_results: List[ScreeningResult]
    ) -> Dict[str, Any]:
        """评估质量指标"""
        if not screening_results:
            return {}
        
        # 计算各种质量指标
        total = len(screening_results)
        
        # 样本不足率
        inadequate_rate = sum(1 for r in screening_results 
                            if r.result_type == ScreeningResultType.INADEQUATE) / total
        
        # 低质量结果比例
        low_quality_rate = sum(1 for r in screening_results 
                             if r.quality_score < self.quality_thresholds['min_quality_score']) / total
        
        # 低置信度结果比例
        low_confidence_rate = sum(1 for r in screening_results 
                                if r.confidence_score < self.quality_thresholds['min_confidence_score']) / total
        
        # 处理时间统计
        processing_times = [r.processing_time for r in screening_results if r.processing_time > 0]
        avg_processing_time = statistics.mean(processing_times) if processing_times else 0
        
        return {
            'inadequate_sample_rate': inadequate_rate,
            'low_quality_rate': low_quality_rate,
            'low_confidence_rate': low_confidence_rate,
            'average_processing_time': avg_processing_time,
            'quality_score_distribution': self._calculate_distribution(
                [r.quality_score for r in screening_results]
            ),
            'confidence_score_distribution': self._calculate_distribution(
                [r.confidence_score for r in screening_results]
            )
        }

    def _analyze_costs(self, screening_results: List[ScreeningResult]) -> Dict[str, Any]:
        """分析成本"""
        if not screening_results:
            return {}
        
        costs = [r.cost for r in screening_results]
        
        # 基础成本统计
        cost_analysis = {
            'total_cost': sum(costs),
            'average_cost': statistics.mean(costs),
            'median_cost': statistics.median(costs),
            'cost_range': (min(costs), max(costs))
        }
        
        # 按工具类型分析成本
        tool_costs = defaultdict(list)
        for result in screening_results:
            tool_costs[result.tool_type.value].append(result.cost)
        
        cost_by_tool = {}
        for tool, tool_cost_list in tool_costs.items():
            cost_by_tool[tool] = {
                'total_cost': sum(tool_cost_list),
                'average_cost': statistics.mean(tool_cost_list),
                'test_count': len(tool_cost_list)
            }
        
        cost_analysis['cost_by_tool'] = cost_by_tool
        
        # 假结果额外成本
        false_result_costs = []
        for result in screening_results:
            if result.false_result_impact:
                false_result_costs.append(result.false_result_impact.additional_cost)
        
        if false_result_costs:
            cost_analysis['false_result_costs'] = {
                'total_additional_cost': sum(false_result_costs),
                'average_additional_cost': statistics.mean(false_result_costs),
                'count': len(false_result_costs)
            }
        
        return cost_analysis

    def _analyze_false_results(
        self, 
        screening_results: List[ScreeningResult]
    ) -> Dict[str, Any]:
        """分析假结果"""
        false_results = [
            r for r in screening_results 
            if r.false_result_impact and 
            r.false_result_impact.result_type != FalseResultType.TRUE_RESULT
        ]
        
        if not false_results:
            return {"message": "没有假结果数据"}
        
        false_positives = [
            r for r in false_results 
            if r.false_result_impact.result_type == FalseResultType.FALSE_POSITIVE
        ]
        
        false_negatives = [
            r for r in false_results 
            if r.false_result_impact.result_type == FalseResultType.FALSE_NEGATIVE
        ]
        
        analysis = {
            'total_false_results': len(false_results),
            'false_positive_count': len(false_positives),
            'false_negative_count': len(false_negatives),
            'false_result_rate': len(false_results) / len(screening_results)
        }
        
        # 假阳性分析
        if false_positives:
            fp_psychological_impact = [r.false_result_impact.psychological_impact_score 
                                     for r in false_positives]
            fp_anxiety_duration = [r.false_result_impact.anxiety_duration_days 
                                 for r in false_positives]
            
            analysis['false_positive_analysis'] = {
                'average_psychological_impact': statistics.mean(fp_psychological_impact),
                'average_anxiety_duration': statistics.mean(fp_anxiety_duration),
                'total_unnecessary_procedures': sum(
                    r.false_result_impact.unnecessary_procedures for r in false_positives
                )
            }
        
        # 假阴性分析
        if false_negatives:
            fn_detection_delays = [r.false_result_impact.delay_in_detection_days 
                                 for r in false_negatives]
            
            analysis['false_negative_analysis'] = {
                'average_detection_delay': statistics.mean(fn_detection_delays),
                'total_detection_delay': sum(fn_detection_delays)
            }
        
        return analysis

    def _calculate_distribution(self, values: List[float]) -> Dict[str, float]:
        """计算数值分布"""
        if not values:
            return {}
        
        return {
            'mean': statistics.mean(values),
            'median': statistics.median(values),
            'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
            'min': min(values),
            'max': max(values),
            'q25': statistics.quantiles(values, n=4)[0] if len(values) >= 4 else min(values),
            'q75': statistics.quantiles(values, n=4)[2] if len(values) >= 4 else max(values)
        }

    def monitor_screening_quality(
        self,
        screening_results: List[ScreeningResult]
    ) -> List[QualityAlert]:
        """
        监控筛查质量并生成警报

        Args:
            screening_results: 筛查结果列表

        Returns:
            质量警报列表
        """
        alerts = []

        if not screening_results:
            return alerts

        total = len(screening_results)

        # 检查阳性率
        positive_count = sum(1 for r in screening_results
                           if r.result_type == ScreeningResultType.POSITIVE)
        positivity_rate = positive_count / total

        if positivity_rate > self.quality_thresholds['max_positivity_rate']:
            alerts.append(QualityAlert(
                alert_type="HIGH_POSITIVITY_RATE",
                message=f"阳性率异常高: {positivity_rate:.2%}",
                severity=QualityAlertSeverity.WARNING,
                affected_count=positive_count,
                threshold_value=self.quality_thresholds['max_positivity_rate'],
                actual_value=positivity_rate
            ))

        # 检查假阳性率
        false_positives = sum(1 for r in screening_results
                            if r.result_type == ScreeningResultType.POSITIVE and
                            r.is_true_positive is False)
        false_positive_rate = false_positives / total if total > 0 else 0

        if false_positive_rate > self.quality_thresholds['max_false_positive_rate']:
            alerts.append(QualityAlert(
                alert_type="HIGH_FALSE_POSITIVE_RATE",
                message=f"假阳性率过高: {false_positive_rate:.2%}",
                severity=QualityAlertSeverity.ERROR,
                affected_count=false_positives,
                threshold_value=self.quality_thresholds['max_false_positive_rate'],
                actual_value=false_positive_rate
            ))

        # 检查敏感性
        metrics = self._calculate_performance_metrics(screening_results)
        if metrics.sensitivity < self.quality_thresholds['min_sensitivity']:
            alerts.append(QualityAlert(
                alert_type="LOW_SENSITIVITY",
                message=f"敏感性过低: {metrics.sensitivity:.2%}",
                severity=QualityAlertSeverity.ERROR,
                threshold_value=self.quality_thresholds['min_sensitivity'],
                actual_value=metrics.sensitivity
            ))

        # 检查特异性
        if metrics.specificity < self.quality_thresholds['min_specificity']:
            alerts.append(QualityAlert(
                alert_type="LOW_SPECIFICITY",
                message=f"特异性过低: {metrics.specificity:.2%}",
                severity=QualityAlertSeverity.ERROR,
                threshold_value=self.quality_thresholds['min_specificity'],
                actual_value=metrics.specificity
            ))

        # 检查阳性预测值
        if metrics.positive_predictive_value < self.quality_thresholds['min_ppv']:
            alerts.append(QualityAlert(
                alert_type="LOW_PPV",
                message=f"阳性预测值过低: {metrics.positive_predictive_value:.2%}",
                severity=QualityAlertSeverity.WARNING,
                threshold_value=self.quality_thresholds['min_ppv'],
                actual_value=metrics.positive_predictive_value
            ))

        # 检查样本不足率
        inadequate_count = sum(1 for r in screening_results
                             if r.result_type == ScreeningResultType.INADEQUATE)
        inadequate_rate = inadequate_count / total

        if inadequate_rate > 0.05:  # 5%阈值
            alerts.append(QualityAlert(
                alert_type="HIGH_INADEQUATE_RATE",
                message=f"样本不足率过高: {inadequate_rate:.2%}",
                severity=QualityAlertSeverity.WARNING,
                affected_count=inadequate_count,
                threshold_value=0.05,
                actual_value=inadequate_rate
            ))

        # 检查低质量结果比例
        low_quality_count = sum(1 for r in screening_results
                              if r.quality_score < self.quality_thresholds['min_quality_score'])
        low_quality_rate = low_quality_count / total

        if low_quality_rate > 0.10:  # 10%阈值
            alerts.append(QualityAlert(
                alert_type="HIGH_LOW_QUALITY_RATE",
                message=f"低质量结果比例过高: {low_quality_rate:.2%}",
                severity=QualityAlertSeverity.WARNING,
                affected_count=low_quality_count,
                threshold_value=0.10,
                actual_value=low_quality_rate
            ))

        # 检查平均置信度
        avg_confidence = statistics.mean(r.confidence_score for r in screening_results)
        if avg_confidence < self.quality_thresholds['min_confidence_score']:
            alerts.append(QualityAlert(
                alert_type="LOW_AVERAGE_CONFIDENCE",
                message=f"平均置信度过低: {avg_confidence:.2f}",
                severity=QualityAlertSeverity.WARNING,
                threshold_value=self.quality_thresholds['min_confidence_score'],
                actual_value=avg_confidence
            ))

        return alerts

    def generate_quality_dashboard(
        self,
        screening_results: List[ScreeningResult]
    ) -> Dict[str, Any]:
        """
        生成质量监控仪表板数据

        Args:
            screening_results: 筛查结果列表

        Returns:
            仪表板数据
        """
        if not screening_results:
            return {"error": "没有筛查结果数据"}

        # 获取质量警报
        alerts = self.monitor_screening_quality(screening_results)

        # 计算关键指标
        metrics = self._calculate_performance_metrics(screening_results)
        stats = self._calculate_summary_stats(screening_results)

        # 按严重程度分组警报
        alerts_by_severity = defaultdict(list)
        for alert in alerts:
            alerts_by_severity[alert.severity.value].append(alert)

        dashboard = {
            'overview': {
                'total_tests': len(screening_results),
                'positivity_rate': stats.positive_rate,
                'average_quality_score': stats.average_quality_score,
                'average_confidence_score': stats.average_confidence_score,
                'total_alerts': len(alerts)
            },
            'performance_summary': {
                'sensitivity': metrics.sensitivity,
                'specificity': metrics.specificity,
                'ppv': metrics.positive_predictive_value,
                'npv': metrics.negative_predictive_value,
                'accuracy': metrics.accuracy
            },
            'quality_alerts': {
                'total_count': len(alerts),
                'by_severity': dict(alerts_by_severity),
                'recent_alerts': sorted(alerts, key=lambda x: x.timestamp, reverse=True)[:10]
            },
            'trend_indicators': self._calculate_trend_indicators(screening_results),
            'recommendations': self._generate_quality_recommendations(alerts, metrics, stats)
        }

        return dashboard

    def _calculate_trend_indicators(
        self,
        screening_results: List[ScreeningResult]
    ) -> Dict[str, str]:
        """计算趋势指标"""
        if len(screening_results) < 10:
            return {"message": "数据不足以计算趋势"}

        # 按时间排序
        sorted_results = sorted(screening_results, key=lambda x: x.test_date)

        # 分成前半部分和后半部分
        mid_point = len(sorted_results) // 2
        first_half = sorted_results[:mid_point]
        second_half = sorted_results[mid_point:]

        # 计算趋势
        trends = {}

        # 阳性率趋势
        first_pos_rate = sum(1 for r in first_half
                           if r.result_type == ScreeningResultType.POSITIVE) / len(first_half)
        second_pos_rate = sum(1 for r in second_half
                            if r.result_type == ScreeningResultType.POSITIVE) / len(second_half)

        if second_pos_rate > first_pos_rate * 1.1:
            trends['positivity_rate'] = "上升"
        elif second_pos_rate < first_pos_rate * 0.9:
            trends['positivity_rate'] = "下降"
        else:
            trends['positivity_rate'] = "稳定"

        # 质量分数趋势
        first_quality = statistics.mean(r.quality_score for r in first_half)
        second_quality = statistics.mean(r.quality_score for r in second_half)

        if second_quality > first_quality * 1.05:
            trends['quality_score'] = "改善"
        elif second_quality < first_quality * 0.95:
            trends['quality_score'] = "恶化"
        else:
            trends['quality_score'] = "稳定"

        return trends

    def _generate_quality_recommendations(
        self,
        alerts: List[QualityAlert],
        metrics: PerformanceMetrics,
        stats: ScreeningStatistics
    ) -> List[str]:
        """生成质量改进建议"""
        recommendations = []

        # 基于警报的建议
        for alert in alerts:
            if alert.alert_type == "HIGH_POSITIVITY_RATE":
                recommendations.append("考虑审查筛查标准或提高筛查工具特异性")
            elif alert.alert_type == "HIGH_FALSE_POSITIVE_RATE":
                recommendations.append("需要改进筛查工具的特异性或加强质量控制")
            elif alert.alert_type == "LOW_SENSITIVITY":
                recommendations.append("考虑提高筛查工具敏感性或调整筛查策略")
            elif alert.alert_type == "HIGH_INADEQUATE_RATE":
                recommendations.append("加强样本采集培训和质量控制流程")
            elif alert.alert_type == "HIGH_LOW_QUALITY_RATE":
                recommendations.append("审查检测流程和设备维护情况")

        # 基于性能指标的建议
        if metrics.sensitivity < 0.8:
            recommendations.append("敏感性偏低，建议评估筛查工具性能或调整阈值")

        if metrics.specificity < 0.9:
            recommendations.append("特异性偏低，可能导致过多假阳性，建议优化筛查流程")

        if stats.inadequate_rate > 0.03:
            recommendations.append("样本不足率较高，建议加强采样技术培训")

        # 去重
        recommendations = list(set(recommendations))

        return recommendations[:10]  # 最多返回10条建议
