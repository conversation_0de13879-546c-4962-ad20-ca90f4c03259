"""
依从性统计分析服务

提供依从性数据的统计分析、趋势分析和可视化功能，
支持人群级别和个体级别的依从性评估。
"""

import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from collections import defaultdict, Counter
import math

from ..modules.screening.compliance_model import ScreeningEvent, ComplianceModel
from ..modules.screening.temporal_compliance import TemporalComplianceModel, CompliancePattern
from ..modules.screening.enums import ScreeningToolType, ScreeningResult
from ..core.individual import Individual
# from ..core.population import Population  # 暂时注释，避免循环导入


@dataclass
class ComplianceStatistics:
    """依从性统计数据"""
    
    total_individuals: int
    compliant_individuals: int
    compliance_rate: float
    mean_interval_days: float
    median_interval_days: float
    std_interval_days: float
    tool_specific_rates: Dict[str, float] = field(default_factory=dict)
    demographic_breakdown: Dict[str, Dict[str, float]] = field(default_factory=dict)


@dataclass
class ComplianceTrend:
    """依从性趋势数据"""
    
    time_period: str
    compliance_rate: float
    sample_size: int
    confidence_interval: Tuple[float, float]
    trend_direction: str  # 'increasing', 'decreasing', 'stable'


class ComplianceAnalytics:
    """
    依从性分析服务类
    
    提供全面的依从性统计分析、趋势分析和报告生成功能。
    """
    
    def __init__(
        self, 
        compliance_model: ComplianceModel,
        temporal_model: Optional[TemporalComplianceModel] = None
    ):
        """
        初始化依从性分析服务
        
        Args:
            compliance_model: 依从性模型实例
            temporal_model: 时间趋势模型实例（可选）
        """
        self.compliance_model = compliance_model
        self.temporal_model = temporal_model
        self._cache = {}  # 缓存分析结果
    
    def generate_compliance_report(self, population) -> Dict[str, Any]:
        """
        生成人群依从性分析报告
        
        Args:
            population: 人群对象（包含individuals列表）
            
        Returns:
            依从性分析报告字典
        """
        # 收集所有筛查事件
        all_screening_events = self._collect_screening_events(population)
        
        if not all_screening_events:
            return {
                'status': 'no_data',
                'message': '无筛查数据可供分析',
                'population_size': len(getattr(population, 'individuals', []))
            }
        
        # 计算总体依从性统计
        overall_stats = self._calculate_overall_statistics(
            population, all_screening_events
        )
        
        # 按工具类型分析
        tool_analysis = self._analyze_by_tool_type(all_screening_events)
        
        # 按人口统计学特征分析
        demographic_analysis = self._analyze_by_demographics(
            population, all_screening_events
        )
        
        # 时间趋势分析
        temporal_trends = self._analyze_temporal_trends(all_screening_events)
        
        # 生成建议
        recommendations = self._generate_population_recommendations(
            overall_stats, tool_analysis, demographic_analysis
        )
        
        report = {
            'report_date': datetime.now().isoformat(),
            'population_size': len(getattr(population, 'individuals', [])),
            'analysis_period': self._get_analysis_period(all_screening_events),
            'overall_statistics': overall_stats.__dict__,
            'tool_analysis': tool_analysis,
            'demographic_analysis': demographic_analysis,
            'temporal_trends': temporal_trends,
            'recommendations': recommendations,
            'data_quality': self._assess_data_quality(all_screening_events)
        }
        
        return report
    
    def _collect_screening_events(self, population) -> List[ScreeningEvent]:
        """收集人群的所有筛查事件"""
        # 这里需要从人群对象中提取筛查历史
        # 由于当前Individual类没有筛查历史属性，我们返回空列表
        # 在实际实现中，需要从数据库或其他存储中获取
        return []
    
    def _calculate_overall_statistics(
        self, 
        population,
        screening_events: List[ScreeningEvent]
    ) -> ComplianceStatistics:
        """计算总体依从性统计"""
        total_individuals = len(getattr(population, 'individuals', []))
        
        if not screening_events:
            return ComplianceStatistics(
                total_individuals=total_individuals,
                compliant_individuals=0,
                compliance_rate=0.0,
                mean_interval_days=0.0,
                median_interval_days=0.0,
                std_interval_days=0.0
            )
        
        # 按个体分组筛查事件
        individual_events = defaultdict(list)
        for event in screening_events:
            individual_events[event.individual_id].append(event)
        
        # 计算依从个体数量（至少有一次筛查）
        compliant_individuals = len(individual_events)
        compliance_rate = compliant_individuals / total_individuals if total_individuals > 0 else 0.0
        
        # 计算筛查间隔统计
        all_intervals = []
        for events in individual_events.values():
            if len(events) >= 2:
                sorted_events = sorted(events, key=lambda x: x.date)
                for i in range(1, len(sorted_events)):
                    interval = (sorted_events[i].date - sorted_events[i-1].date).days
                    all_intervals.append(interval)
        
        if all_intervals:
            mean_interval = statistics.mean(all_intervals)
            median_interval = statistics.median(all_intervals)
            std_interval = statistics.stdev(all_intervals) if len(all_intervals) > 1 else 0.0
        else:
            mean_interval = median_interval = std_interval = 0.0
        
        # 按工具类型计算依从性率
        tool_specific_rates = self._calculate_tool_specific_rates(screening_events)
        
        return ComplianceStatistics(
            total_individuals=total_individuals,
            compliant_individuals=compliant_individuals,
            compliance_rate=compliance_rate,
            mean_interval_days=mean_interval,
            median_interval_days=median_interval,
            std_interval_days=std_interval,
            tool_specific_rates=tool_specific_rates
        )
    
    def _calculate_tool_specific_rates(
        self, 
        screening_events: List[ScreeningEvent]
    ) -> Dict[str, float]:
        """计算工具特异性依从性率"""
        tool_counts = Counter(event.tool_type.value for event in screening_events)
        total_events = len(screening_events)
        
        if total_events == 0:
            return {}
        
        return {
            tool: count / total_events 
            for tool, count in tool_counts.items()
        }
    
    def _analyze_by_tool_type(
        self, 
        screening_events: List[ScreeningEvent]
    ) -> Dict[str, Any]:
        """按筛查工具类型分析依从性"""
        tool_analysis = {}
        
        # 按工具类型分组
        tool_events = defaultdict(list)
        for event in screening_events:
            tool_events[event.tool_type.value].append(event)
        
        for tool_type, events in tool_events.items():
            # 计算该工具的统计信息
            individual_counts = len(set(event.individual_id for event in events))
            event_counts = len(events)
            
            # 计算结果分布
            result_distribution = Counter(event.result.value for event in events)
            result_percentages = {
                result: count / event_counts * 100 
                for result, count in result_distribution.items()
            }
            
            # 计算平均间隔
            intervals = self._calculate_intervals_for_events(events)
            avg_interval = statistics.mean(intervals) if intervals else 0
            
            tool_analysis[tool_type] = {
                'unique_individuals': individual_counts,
                'total_events': event_counts,
                'average_interval_days': avg_interval,
                'result_distribution': result_percentages,
                'positive_rate': result_percentages.get('positive', 0.0)
            }
        
        return tool_analysis
    
    def _calculate_intervals_for_events(
        self, 
        events: List[ScreeningEvent]
    ) -> List[float]:
        """计算事件间隔"""
        # 按个体分组
        individual_events = defaultdict(list)
        for event in events:
            individual_events[event.individual_id].append(event)
        
        intervals = []
        for individual_events_list in individual_events.values():
            if len(individual_events_list) >= 2:
                sorted_events = sorted(individual_events_list, key=lambda x: x.date)
                for i in range(1, len(sorted_events)):
                    interval = (sorted_events[i].date - sorted_events[i-1].date).days
                    intervals.append(interval)
        
        return intervals
    
    def _analyze_by_demographics(
        self, 
        population,
        screening_events: List[ScreeningEvent]
    ) -> Dict[str, Any]:
        """按人口统计学特征分析依从性"""
        demographic_analysis = {}
        
        # 创建个体ID到个体对象的映射
        individual_map = {ind.individual_id: ind for ind in getattr(population, 'individuals', [])}
        
        # 按性别分析
        gender_analysis = self._analyze_by_gender(individual_map, screening_events)
        demographic_analysis['gender'] = gender_analysis
        
        # 按年龄组分析
        age_analysis = self._analyze_by_age_group(individual_map, screening_events)
        demographic_analysis['age_group'] = age_analysis
        
        return demographic_analysis
    
    def _analyze_by_gender(
        self, 
        individual_map: Dict[str, Individual], 
        screening_events: List[ScreeningEvent]
    ) -> Dict[str, Any]:
        """按性别分析依从性"""
        gender_events = defaultdict(list)
        
        for event in screening_events:
            individual = individual_map.get(event.individual_id)
            if individual:
                gender_events[individual.gender.value].append(event)
        
        gender_analysis = {}
        for gender, events in gender_events.items():
            unique_individuals = len(set(event.individual_id for event in events))
            total_events = len(events)
            
            gender_analysis[gender] = {
                'unique_individuals': unique_individuals,
                'total_events': total_events,
                'events_per_individual': total_events / unique_individuals if unique_individuals > 0 else 0
            }
        
        return gender_analysis
    
    def _analyze_by_age_group(
        self, 
        individual_map: Dict[str, Individual], 
        screening_events: List[ScreeningEvent]
    ) -> Dict[str, Any]:
        """按年龄组分析依从性"""
        age_groups = {
            '50-59': (50, 59),
            '60-69': (60, 69),
            '70-75': (70, 75)
        }
        
        age_group_events = defaultdict(list)
        
        for event in screening_events:
            individual = individual_map.get(event.individual_id)
            if individual:
                age = individual.get_current_age()
                for group_name, (min_age, max_age) in age_groups.items():
                    if min_age <= age <= max_age:
                        age_group_events[group_name].append(event)
                        break
        
        age_analysis = {}
        for age_group, events in age_group_events.items():
            unique_individuals = len(set(event.individual_id for event in events))
            total_events = len(events)
            
            age_analysis[age_group] = {
                'unique_individuals': unique_individuals,
                'total_events': total_events,
                'events_per_individual': total_events / unique_individuals if unique_individuals > 0 else 0
            }
        
        return age_analysis
    
    def _analyze_temporal_trends(
        self, 
        screening_events: List[ScreeningEvent]
    ) -> List[ComplianceTrend]:
        """分析时间趋势"""
        if not screening_events:
            return []
        
        # 按年份分组事件
        yearly_events = defaultdict(list)
        for event in screening_events:
            year = event.date.year
            yearly_events[year].append(event)
        
        trends = []
        for year in sorted(yearly_events.keys()):
            events = yearly_events[year]
            unique_individuals = len(set(event.individual_id for event in events))
            
            # 简化的依从性率计算（基于事件数量）
            compliance_rate = len(events) / max(unique_individuals, 1)
            
            # 简化的置信区间计算
            ci_lower = max(0, compliance_rate - 0.1)
            ci_upper = min(1, compliance_rate + 0.1)
            
            trend = ComplianceTrend(
                time_period=str(year),
                compliance_rate=compliance_rate,
                sample_size=unique_individuals,
                confidence_interval=(ci_lower, ci_upper),
                trend_direction='stable'  # 简化处理
            )
            trends.append(trend)
        
        return trends
    
    def _generate_population_recommendations(
        self,
        overall_stats: ComplianceStatistics,
        tool_analysis: Dict[str, Any],
        demographic_analysis: Dict[str, Any]
    ) -> List[str]:
        """生成人群级别的依从性改善建议"""
        recommendations = []
        
        # 基于总体依从性率的建议
        if overall_stats.compliance_rate < 0.5:
            recommendations.append("总体依从性率较低，需要加强宣传教育和便民措施")
        elif overall_stats.compliance_rate < 0.7:
            recommendations.append("依从性率有待提高，建议优化筛查流程")
        else:
            recommendations.append("依从性率良好，继续维持当前策略")
        
        # 基于工具分析的建议
        if tool_analysis:
            fit_positive_rate = tool_analysis.get('fecal_immunochemical_test', {}).get('positive_rate', 0)
            if fit_positive_rate > 10:
                recommendations.append("FIT阳性率较高，需要加强后续诊断依从性管理")
        
        # 基于人口统计学的建议
        if 'gender' in demographic_analysis:
            gender_data = demographic_analysis['gender']
            if len(gender_data) >= 2:
                male_events = gender_data.get('male', {}).get('events_per_individual', 0)
                female_events = gender_data.get('female', {}).get('events_per_individual', 0)
                
                if abs(male_events - female_events) > 0.5:
                    recommendations.append("性别间依从性存在差异，需要针对性干预")
        
        return recommendations
    
    def _assess_data_quality(self, screening_events: List[ScreeningEvent]) -> Dict[str, Any]:
        """评估数据质量"""
        if not screening_events:
            return {'status': 'no_data', 'completeness': 0.0}
        
        # 检查数据完整性
        complete_events = sum(
            1 for event in screening_events 
            if event.individual_id and event.date and event.tool_type and event.result
        )
        
        completeness = complete_events / len(screening_events)
        
        # 检查时间范围
        dates = [event.date for event in screening_events if event.date]
        if dates:
            date_range_days = (max(dates) - min(dates)).days
        else:
            date_range_days = 0
        
        return {
            'status': 'good' if completeness > 0.9 else 'fair' if completeness > 0.7 else 'poor',
            'completeness': completeness,
            'total_events': len(screening_events),
            'date_range_days': date_range_days,
            'unique_individuals': len(set(event.individual_id for event in screening_events))
        }
    
    def _get_analysis_period(self, screening_events: List[ScreeningEvent]) -> Dict[str, str]:
        """获取分析时间段"""
        if not screening_events:
            return {'start_date': '', 'end_date': ''}
        
        dates = [event.date for event in screening_events if event.date]
        if dates:
            return {
                'start_date': min(dates).isoformat(),
                'end_date': max(dates).isoformat()
            }
        
        return {'start_date': '', 'end_date': ''}
    
    def predict_compliance_improvement(
        self, 
        population,
        intervention: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        预测干预措施对依从性的改善效果
        
        Args:
            population: 目标人群
            intervention: 干预措施配置
            
        Returns:
            改善效果预测
        """
        # 获取基线依从性
        baseline_report = self.generate_compliance_report(population)
        baseline_rate = baseline_report.get('overall_statistics', {}).get('compliance_rate', 0.0)
        
        # 根据干预类型预测改善效果
        intervention_type = intervention.get('type', 'education')
        intervention_intensity = intervention.get('intensity', 'medium')
        
        # 简化的改善效果模型
        improvement_factors = {
            'education': {'low': 0.05, 'medium': 0.10, 'high': 0.15},
            'reminder': {'low': 0.08, 'medium': 0.15, 'high': 0.25},
            'incentive': {'low': 0.10, 'medium': 0.20, 'high': 0.30},
            'access_improvement': {'low': 0.12, 'medium': 0.25, 'high': 0.40}
        }
        
        improvement_factor = improvement_factors.get(intervention_type, {}).get(intervention_intensity, 0.10)
        predicted_rate = min(1.0, baseline_rate + improvement_factor)
        
        # 计算受影响的人群数量
        affected_population = int(len(getattr(population, 'individuals', [])) * intervention.get('coverage', 0.8))
        
        return {
            'baseline_rate': baseline_rate,
            'predicted_rate': predicted_rate,
            'absolute_improvement': predicted_rate - baseline_rate,
            'relative_improvement': (predicted_rate - baseline_rate) / baseline_rate if baseline_rate > 0 else 0,
            'affected_population': affected_population,
            'intervention_details': intervention,
            'confidence_level': 0.7  # 简化的置信度
        }
