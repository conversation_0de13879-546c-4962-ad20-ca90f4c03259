"""
筛查策略设计器界面组件测试

测试策略设计器的界面功能、用户交互和数据绑定。
"""

import pytest
import sys
from unittest.mock import Mock, patch, MagicMock

# 确保PyQt6可用
try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    from PyQt6.QtTest import QTest
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

from src.modules.screening.strategy import (
    ScreeningStrategy, ScreeningInterval, TargetPopulation, ScreeningFrequency
)
from src.modules.screening.enums import ScreeningToolType

if PYQT_AVAILABLE:
    from src.interfaces.desktop.widgets.strategy_designer import StrategyDesignerWidget


@pytest.mark.skipif(not PYQT_AVAILABLE, reason="PyQt6 not available")
class TestStrategyDesignerWidget:
    """测试筛查策略设计器界面组件"""
    
    @pytest.fixture(autouse=True)
    def setup_qt_app(self):
        """设置Qt应用程序"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        yield
        # 清理在teardown中进行
    
    @pytest.fixture
    def widget(self):
        """创建策略设计器组件"""
        widget = StrategyDesignerWidget()
        yield widget
        widget.close()
    
    def test_widget_initialization(self, widget):
        """测试组件初始化"""
        assert widget is not None
        assert widget.current_strategy is not None
        assert widget.current_strategy.name == "新建筛查策略"
        assert isinstance(widget.validation_errors, list)
    
    def test_ui_components_exist(self, widget):
        """测试UI组件存在性"""
        # 检查主要组件是否存在
        assert hasattr(widget, 'config_tabs')
        assert hasattr(widget, 'name_edit')
        assert hasattr(widget, 'description_edit')
        assert hasattr(widget, 'intervals_table')
        assert hasattr(widget, 'preview_text')
        assert hasattr(widget, 'validation_label')
        assert hasattr(widget, 'errors_list')
        
        # 检查按钮
        assert hasattr(widget, 'validate_btn')
        assert hasattr(widget, 'save_btn')
        assert hasattr(widget, 'reset_btn')
        assert hasattr(widget, 'add_interval_btn')
        assert hasattr(widget, 'remove_interval_btn')
    
    def test_basic_info_input(self, widget):
        """测试基本信息输入"""
        # 设置策略名称
        widget.name_edit.setText("测试策略")
        assert widget.current_strategy.name == "测试策略"
        
        # 设置描述
        widget.description_edit.setPlainText("这是一个测试策略")
        assert widget.current_strategy.description == "这是一个测试策略"
        
        # 设置版本
        widget.version_edit.setText("2.0")
        assert widget.current_strategy.version == "2.0"
        
        # 设置作者
        widget.author_edit.setText("测试作者")
        assert widget.current_strategy.author == "测试作者"
    
    def test_template_configuration(self, widget):
        """测试模板配置"""
        # 测试模板选项
        assert not widget.template_category_combo.isEnabled()
        
        widget.is_template_check.setChecked(True)
        assert widget.template_category_combo.isEnabled()
        assert widget.current_strategy.is_template
        
        # 设置模板分类
        widget.template_category_combo.setCurrentText("国家指南")
        assert widget.current_strategy.template_category == "国家指南"
    
    def test_budget_configuration(self, widget):
        """测试预算配置"""
        # 设置预算约束
        widget.budget_spin.setValue(1000000)
        assert widget.current_strategy.budget_constraint == 1000000
        
        # 设置成本效益阈值
        widget.cost_threshold_spin.setValue(50000)
        assert widget.current_strategy.cost_effectiveness_threshold == 50000
    
    def test_population_configuration(self, widget):
        """测试目标人群配置"""
        # 设置年龄范围
        widget.pop_start_age_spin.setValue(45)
        widget.pop_end_age_spin.setValue(80)
        assert widget.current_strategy.target_population.age_range == (45, 80)
        
        # 设置风险水平
        widget.risk_level_combo.setCurrentText("高风险")
        assert widget.current_strategy.target_population.risk_level == "high"
        
        # 设置性别限制
        widget.gender_combo.setCurrentText("仅女性")
        assert widget.current_strategy.target_population.gender_restriction == "female"
        
        # 设置人群规模
        widget.pop_size_spin.setValue(50000)
        assert widget.current_strategy.target_population.population_size == 50000
        
        # 设置地理区域
        widget.region_edit.setText("深圳市")
        assert widget.current_strategy.target_population.geographic_region == "深圳市"
    
    def test_interval_configuration(self, widget):
        """测试筛查间隔配置"""
        # 设置间隔参数
        widget.tool_type_combo.setCurrentIndex(0)  # 选择第一个工具
        widget.start_age_spin.setValue(50)
        widget.end_age_spin.setValue(70)
        widget.frequency_combo.setCurrentIndex(0)  # 选择第一个频率
        widget.priority_spin.setValue(2)
        widget.sequence_spin.setValue(1)
        
        # 添加间隔
        initial_count = len(widget.current_strategy.intervals)
        widget._add_interval()
        
        # 检查间隔是否添加成功
        assert len(widget.current_strategy.intervals) == initial_count + 1
        
        # 检查表格是否更新
        assert widget.intervals_table.rowCount() == len(widget.current_strategy.intervals)
    
    def test_custom_frequency(self, widget):
        """测试自定义频率"""
        # 选择自定义频率
        custom_index = -1
        for i in range(widget.frequency_combo.count()):
            if widget.frequency_combo.itemData(i) == ScreeningFrequency.CUSTOM:
                custom_index = i
                break
        
        if custom_index >= 0:
            widget.frequency_combo.setCurrentIndex(custom_index)
            assert widget.custom_frequency_spin.isEnabled()
            
            # 设置自定义频率值
            widget.custom_frequency_spin.setValue(5.0)
            
            # 添加间隔
            widget._add_interval()
            
            # 检查最后添加的间隔
            if widget.current_strategy.intervals:
                last_interval = widget.current_strategy.intervals[-1]
                assert last_interval.frequency == ScreeningFrequency.CUSTOM
                assert last_interval.custom_frequency_years == 5.0
    
    def test_interval_removal(self, widget):
        """测试间隔删除"""
        # 先添加一个间隔
        widget._add_interval()
        initial_count = len(widget.current_strategy.intervals)
        
        # 选择第一行
        widget.intervals_table.selectRow(0)
        assert widget.remove_interval_btn.isEnabled()
        
        # 模拟用户确认删除
        with patch('PyQt6.QtWidgets.QMessageBox.question', return_value=Mock()):
            with patch.object(Mock(), 'StandardButton') as mock_button:
                mock_button.Yes = Mock()
                with patch('PyQt6.QtWidgets.QMessageBox.StandardButton', mock_button):
                    widget._remove_interval()
        
        # 注意：由于我们模拟了对话框，实际删除可能不会发生
        # 这里主要测试方法调用不出错
    
    def test_strategy_validation(self, widget):
        """测试策略验证"""
        # 验证空策略（应该有错误）
        widget._validate_strategy()
        assert len(widget.validation_errors) > 0
        assert "策略必须包含至少一个筛查间隔" in widget.validation_errors
        
        # 添加间隔后再验证
        widget._add_interval()
        widget._validate_strategy()
        
        # 检查验证状态标签
        assert widget.validation_label.text() in ["策略状态：验证通过", "策略状态：验证失败"]
    
    def test_preview_update(self, widget):
        """测试预览更新"""
        # 设置一些基本信息
        widget.name_edit.setText("测试预览策略")
        widget.description_edit.setPlainText("测试预览功能")
        
        # 更新预览
        widget._update_preview()
        
        # 检查预览文本是否包含设置的信息
        preview_text = widget.preview_text.toPlainText()
        assert "测试预览策略" in preview_text
        assert "测试预览功能" in preview_text
    
    def test_strategy_reset(self, widget):
        """测试策略重置"""
        # 修改策略
        widget.name_edit.setText("修改后的策略")
        widget.description_edit.setPlainText("修改后的描述")
        
        # 模拟用户确认重置
        with patch('PyQt6.QtWidgets.QMessageBox.question') as mock_question:
            mock_question.return_value = Mock()
            with patch.object(Mock(), 'StandardButton') as mock_button:
                mock_button.Yes = Mock()
                with patch('PyQt6.QtWidgets.QMessageBox.StandardButton', mock_button):
                    widget._reset_strategy()
        
        # 检查是否重置为默认值
        assert widget.current_strategy.name == "新建筛查策略"
    
    def test_set_and_get_strategy(self, widget):
        """测试设置和获取策略"""
        # 创建测试策略
        test_strategy = ScreeningStrategy(
            name="外部策略",
            description="从外部设置的策略",
            version="3.0",
            author="外部作者"
        )
        
        # 添加一个间隔
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL
        )
        test_strategy.add_interval(interval)
        
        # 设置策略
        widget.set_strategy(test_strategy)
        
        # 检查界面是否更新
        assert widget.name_edit.text() == "外部策略"
        assert widget.description_edit.toPlainText() == "从外部设置的策略"
        assert widget.version_edit.text() == "3.0"
        assert widget.author_edit.text() == "外部作者"
        assert widget.intervals_table.rowCount() == 1
        
        # 获取策略
        retrieved_strategy = widget.get_strategy()
        assert retrieved_strategy == test_strategy
    
    def test_signal_emissions(self, widget):
        """测试信号发射"""
        # 创建信号接收器
        strategy_created_received = []
        strategy_modified_received = []
        strategy_validated_received = []
        
        def on_strategy_created(strategy):
            strategy_created_received.append(strategy)
        
        def on_strategy_modified(strategy):
            strategy_modified_received.append(strategy)
        
        def on_strategy_validated(is_valid, errors):
            strategy_validated_received.append((is_valid, errors))
        
        # 连接信号
        widget.strategy_created.connect(on_strategy_created)
        widget.strategy_modified.connect(on_strategy_modified)
        widget.strategy_validated.connect(on_strategy_validated)
        
        # 触发修改信号
        widget.name_edit.setText("触发信号测试")
        assert len(strategy_modified_received) > 0
        
        # 触发验证信号
        widget._validate_strategy()
        assert len(strategy_validated_received) > 0
    
    def test_error_handling(self, widget):
        """测试错误处理"""
        # 测试添加无效间隔
        widget.start_age_spin.setValue(80)  # 开始年龄大于结束年龄
        widget.end_age_spin.setValue(50)
        
        with patch('PyQt6.QtWidgets.QMessageBox.warning') as mock_warning:
            widget._add_interval()
            # 应该显示警告对话框
            mock_warning.assert_called()
    
    def test_ui_state_consistency(self, widget):
        """测试界面状态一致性"""
        # 测试删除按钮状态
        assert not widget.remove_interval_btn.isEnabled()  # 初始状态应该禁用
        
        # 添加间隔后选择
        widget._add_interval()
        widget.intervals_table.selectRow(0)
        assert widget.remove_interval_btn.isEnabled()  # 选择后应该启用
        
        # 清除选择
        widget.intervals_table.clearSelection()
        # 注意：这里的状态可能需要手动触发信号
        widget._on_interval_selection_changed()
        assert not widget.remove_interval_btn.isEnabled()  # 清除选择后应该禁用
