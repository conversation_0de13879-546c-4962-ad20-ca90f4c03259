"""
筛查成本配置系统

实现筛查成本的配置、计算和管理功能，支持多种筛查工具的成本建模。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union
from enum import Enum
import logging
import yaml
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class ScreeningTool(Enum):
    """筛查工具类型"""
    FIT = "fit"  # 粪便免疫化学检测
    FOBT = "fobt"  # 粪便潜血检测
    COLONOSCOPY = "colonoscopy"  # 结肠镜检查
    SIGMOIDOSCOPY = "sigmoidoscopy"  # 乙状结肠镜检查
    CT_COLONOGRAPHY = "ct_colonography"  # CT结肠成像
    STOOL_DNA = "stool_dna"  # 粪便DNA检测


class CostComponent(Enum):
    """成本组件类型"""
    PROFESSIONAL_FEE = "professional_fee"  # 专业服务费
    FACILITY_FEE = "facility_fee"  # 机构使用费
    EQUIPMENT_COST = "equipment_cost"  # 设备成本
    MATERIAL_COST = "material_cost"  # 材料成本
    PROCESSING_COST = "processing_cost"  # 处理费用
    INTERPRETATION_COST = "interpretation_cost"  # 解读费用


@dataclass
class CostBreakdown:
    """成本分解结构"""
    professional_fee: float = 0.0
    facility_fee: float = 0.0
    equipment_cost: float = 0.0
    material_cost: float = 0.0
    processing_cost: float = 0.0
    interpretation_cost: float = 0.0
    
    @property
    def total_direct_cost(self) -> float:
        """计算总直接成本"""
        return (
            self.professional_fee + self.facility_fee + self.equipment_cost +
            self.material_cost + self.processing_cost + self.interpretation_cost
        )


@dataclass
class RegionalAdjustment:
    """地区成本调整因子"""
    region_name: str
    cost_multiplier: float = 1.0
    labor_cost_index: float = 1.0
    facility_cost_index: float = 1.0
    equipment_cost_index: float = 1.0
    
    def apply_adjustment(self, base_cost: CostBreakdown) -> CostBreakdown:
        """应用地区调整"""
        return CostBreakdown(
            professional_fee=base_cost.professional_fee * self.labor_cost_index,
            facility_fee=base_cost.facility_fee * self.facility_cost_index,
            equipment_cost=base_cost.equipment_cost * self.equipment_cost_index,
            material_cost=base_cost.material_cost * self.cost_multiplier,
            processing_cost=base_cost.processing_cost * self.cost_multiplier,
            interpretation_cost=base_cost.interpretation_cost * self.labor_cost_index
        )


@dataclass
class ScreeningCostConfig:
    """筛查成本配置"""
    tool: ScreeningTool
    base_costs: CostBreakdown
    currency: str = "CNY"
    base_year: int = 2023
    source: str = ""
    confidence_interval: Optional[tuple] = None
    regional_adjustments: Dict[str, RegionalAdjustment] = field(default_factory=dict)


class ScreeningCostModel:
    """筛查成本模型类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化筛查成本模型
        
        Args:
            config_file: 成本配置文件路径
        """
        self.cost_configs: Dict[ScreeningTool, ScreeningCostConfig] = {}
        self.default_currency = "CNY"
        self.default_base_year = 2023
        
        if config_file:
            self.load_cost_config(config_file)
    
    def load_cost_config(self, config_file: str) -> None:
        """
        加载成本配置文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            config_path = Path(config_file)
            
            if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            self._parse_config_data(config_data)
            logger.info(f"成功加载筛查成本配置: {config_file}")
            
        except Exception as e:
            logger.error(f"加载筛查成本配置失败: {e}")
            raise
    
    def _parse_config_data(self, config_data: Dict) -> None:
        """解析配置数据"""
        screening_costs = config_data.get('screening_costs', {})
        
        for tool_name, tool_config in screening_costs.items():
            if tool_name in ['version', 'currency', 'base_year', 'region']:
                continue
                
            try:
                tool = ScreeningTool(tool_name)
                
                # 解析基础成本
                base_costs = CostBreakdown(
                    professional_fee=tool_config.get('professional_fee', 0.0),
                    facility_fee=tool_config.get('facility_fee', 0.0),
                    equipment_cost=tool_config.get('equipment_cost', 0.0),
                    material_cost=tool_config.get('material_cost', 0.0),
                    processing_cost=tool_config.get('processing_cost', 0.0),
                    interpretation_cost=tool_config.get('interpretation_cost', 0.0)
                )
                
                # 解析地区调整
                regional_adjustments = {}
                if 'regional_adjustments' in tool_config:
                    for region, adjustment in tool_config['regional_adjustments'].items():
                        regional_adjustments[region] = RegionalAdjustment(
                            region_name=region,
                            cost_multiplier=adjustment.get('cost_multiplier', 1.0),
                            labor_cost_index=adjustment.get('labor_cost_index', 1.0),
                            facility_cost_index=adjustment.get('facility_cost_index', 1.0),
                            equipment_cost_index=adjustment.get('equipment_cost_index', 1.0)
                        )
                
                # 创建成本配置
                cost_config = ScreeningCostConfig(
                    tool=tool,
                    base_costs=base_costs,
                    currency=screening_costs.get('currency', self.default_currency),
                    base_year=screening_costs.get('base_year', self.default_base_year),
                    source=tool_config.get('source', ''),
                    confidence_interval=tool_config.get('confidence_interval'),
                    regional_adjustments=regional_adjustments
                )
                
                self.cost_configs[tool] = cost_config
                
            except ValueError as e:
                logger.warning(f"跳过未知的筛查工具: {tool_name}")
                continue
    
    def get_screening_cost(
        self,
        tool: ScreeningTool,
        region: Optional[str] = None,
        quantity: int = 1
    ) -> float:
        """
        获取筛查成本
        
        Args:
            tool: 筛查工具类型
            region: 地区名称（可选）
            quantity: 筛查数量
            
        Returns:
            筛查总成本
        """
        if tool not in self.cost_configs:
            raise ValueError(f"未配置筛查工具成本: {tool.value}")
        
        config = self.cost_configs[tool]
        base_costs = config.base_costs
        
        # 应用地区调整
        if region and region in config.regional_adjustments:
            adjusted_costs = config.regional_adjustments[region].apply_adjustment(base_costs)
        else:
            adjusted_costs = base_costs
        
        # 计算总成本
        unit_cost = adjusted_costs.total_direct_cost
        total_cost = unit_cost * quantity
        
        return total_cost
    
    def get_cost_breakdown(
        self,
        tool: ScreeningTool,
        region: Optional[str] = None
    ) -> CostBreakdown:
        """
        获取成本分解
        
        Args:
            tool: 筛查工具类型
            region: 地区名称（可选）
            
        Returns:
            成本分解对象
        """
        if tool not in self.cost_configs:
            raise ValueError(f"未配置筛查工具成本: {tool.value}")
        
        config = self.cost_configs[tool]
        base_costs = config.base_costs
        
        # 应用地区调整
        if region and region in config.regional_adjustments:
            return config.regional_adjustments[region].apply_adjustment(base_costs)
        else:
            return base_costs

    def calculate_batch_costs(
        self,
        screening_requests: List[Dict]
    ) -> Dict:
        """
        批量计算筛查成本

        Args:
            screening_requests: 筛查请求列表，每个请求包含:
                - tool: 筛查工具类型
                - region: 地区（可选）
                - quantity: 数量
                - patient_id: 患者ID（可选）

        Returns:
            批量成本计算结果
        """
        results = {
            'total_cost': 0.0,
            'cost_by_tool': {},
            'cost_by_region': {},
            'individual_costs': [],
            'summary': {
                'total_screenings': 0,
                'unique_tools': set(),
                'unique_regions': set()
            }
        }

        for request in screening_requests:
            try:
                tool = request['tool']
                if isinstance(tool, str):
                    tool = ScreeningTool(tool)

                region = request.get('region')
                quantity = request.get('quantity', 1)
                patient_id = request.get('patient_id')

                # 计算单次成本
                unit_cost = self.get_screening_cost(tool, region, 1)
                total_cost = unit_cost * quantity

                # 更新总成本
                results['total_cost'] += total_cost

                # 按工具统计
                tool_key = tool.value
                if tool_key not in results['cost_by_tool']:
                    results['cost_by_tool'][tool_key] = {
                        'total_cost': 0.0,
                        'count': 0,
                        'average_cost': 0.0
                    }
                results['cost_by_tool'][tool_key]['total_cost'] += total_cost
                results['cost_by_tool'][tool_key]['count'] += quantity

                # 按地区统计
                region_key = region or 'default'
                if region_key not in results['cost_by_region']:
                    results['cost_by_region'][region_key] = {
                        'total_cost': 0.0,
                        'count': 0,
                        'average_cost': 0.0
                    }
                results['cost_by_region'][region_key]['total_cost'] += total_cost
                results['cost_by_region'][region_key]['count'] += quantity

                # 记录个别成本
                individual_result = {
                    'tool': tool.value,
                    'region': region,
                    'quantity': quantity,
                    'unit_cost': unit_cost,
                    'total_cost': total_cost
                }
                if patient_id:
                    individual_result['patient_id'] = patient_id

                results['individual_costs'].append(individual_result)

                # 更新汇总信息
                results['summary']['total_screenings'] += quantity
                results['summary']['unique_tools'].add(tool.value)
                if region:
                    results['summary']['unique_regions'].add(region)

            except Exception as e:
                logger.error(f"批量成本计算错误: {e}, 请求: {request}")
                continue

        # 计算平均成本
        for tool_stats in results['cost_by_tool'].values():
            if tool_stats['count'] > 0:
                tool_stats['average_cost'] = tool_stats['total_cost'] / tool_stats['count']

        for region_stats in results['cost_by_region'].values():
            if region_stats['count'] > 0:
                region_stats['average_cost'] = region_stats['total_cost'] / region_stats['count']

        # 转换集合为列表
        results['summary']['unique_tools'] = list(results['summary']['unique_tools'])
        results['summary']['unique_regions'] = list(results['summary']['unique_regions'])

        return results

    def add_cost_config(
        self,
        tool: ScreeningTool,
        cost_config: ScreeningCostConfig
    ) -> None:
        """
        添加成本配置

        Args:
            tool: 筛查工具类型
            cost_config: 成本配置对象
        """
        self.cost_configs[tool] = cost_config
        logger.info(f"添加筛查工具成本配置: {tool.value}")

    def update_regional_adjustment(
        self,
        tool: ScreeningTool,
        region: str,
        adjustment: RegionalAdjustment
    ) -> None:
        """
        更新地区调整因子

        Args:
            tool: 筛查工具类型
            region: 地区名称
            adjustment: 地区调整对象
        """
        if tool not in self.cost_configs:
            raise ValueError(f"未配置筛查工具成本: {tool.value}")

        self.cost_configs[tool].regional_adjustments[region] = adjustment
        logger.info(f"更新地区调整因子: {tool.value} - {region}")

    def get_available_tools(self) -> List[ScreeningTool]:
        """获取可用的筛查工具列表"""
        return list(self.cost_configs.keys())

    def get_available_regions(self, tool: ScreeningTool) -> List[str]:
        """获取指定工具的可用地区列表"""
        if tool not in self.cost_configs:
            return []
        return list(self.cost_configs[tool].regional_adjustments.keys())

    def validate_cost_config(self, tool: ScreeningTool) -> Dict:
        """
        验证成本配置

        Args:
            tool: 筛查工具类型

        Returns:
            验证结果字典
        """
        if tool not in self.cost_configs:
            return {
                'valid': False,
                'errors': [f"未配置筛查工具成本: {tool.value}"]
            }

        config = self.cost_configs[tool]
        errors = []
        warnings = []

        # 验证基础成本
        base_costs = config.base_costs
        if base_costs.total_direct_cost <= 0:
            errors.append("总直接成本必须大于0")

        # 验证各成本组件
        cost_components = [
            ('professional_fee', base_costs.professional_fee),
            ('facility_fee', base_costs.facility_fee),
            ('equipment_cost', base_costs.equipment_cost),
            ('material_cost', base_costs.material_cost),
            ('processing_cost', base_costs.processing_cost),
            ('interpretation_cost', base_costs.interpretation_cost)
        ]

        for component_name, value in cost_components:
            if value < 0:
                errors.append(f"{component_name}不能为负数")

        # 验证货币和年份
        if not config.currency:
            warnings.append("未指定货币单位")

        if config.base_year < 1990 or config.base_year > 2030:
            warnings.append(f"基准年份可能不合理: {config.base_year}")

        # 验证置信区间
        if config.confidence_interval:
            lower, upper = config.confidence_interval
            if lower >= upper:
                errors.append("置信区间下限必须小于上限")
            if lower < 0:
                errors.append("置信区间下限不能为负数")

        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
