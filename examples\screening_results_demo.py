"""
筛查结果处理引擎演示

展示筛查结果处理引擎的主要功能，包括：
1. 筛查结果判定和处理
2. 假结果影响分析
3. 后续诊断流程管理
4. 统计分析和效果评估
"""

import sys
import os
import logging
from datetime import datetime
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.individual import Individual
from src.core.enums import DiseaseState, Gender
from src.core.population import Population
from src.modules.screening import (
    ScreeningResultProcessor, FollowupManager, ScreeningToolType,
    FITTool
)
from src.services.screening_analytics import ScreeningAnalytics
from src.modules.screening.effectiveness_metrics import EffectivenessMetrics

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_population(size: int = 100) -> Population:
    """创建样本人群"""
    individuals = []
    
    for i in range(size):
        # 创建不同年龄和疾病状态的个体
        birth_year = 1950 + (i % 25)  # 50-74岁
        gender = Gender.MALE if i % 2 == 0 else Gender.FEMALE
        
        # 大部分正常，少数有病变
        if i < 5:
            disease_state = DiseaseState.HIGH_RISK_ADENOMA
        elif i < 8:
            disease_state = DiseaseState.PRECLINICAL_CANCER
        elif i < 10:
            disease_state = DiseaseState.CLINICAL_CANCER
        else:
            disease_state = DiseaseState.NORMAL
            
        individual = Individual(
            birth_year=birth_year,
            gender=gender,
            initial_disease_state=disease_state
        )
        individuals.append(individual)
    
    return Population(initial_individuals=individuals)


def create_fit_tool() -> FITTool:
    """创建FIT筛查工具"""
    return FITTool()


def demonstrate_screening_results_processing():
    """演示筛查结果处理功能"""
    print("=" * 60)
    print("筛查结果处理引擎演示")
    print("=" * 60)
    
    # 1. 创建人群和筛查工具
    print("\n1. 创建样本人群和筛查工具...")
    population = create_sample_population(100)
    fit_tool = create_fit_tool()
    
    print(f"   - 人群规模: {len(population.individuals)}人")
    print(f"   - 筛查工具: {fit_tool.tool_type.value}")
    
    # 2. 配置筛查结果处理器
    print("\n2. 配置筛查结果处理器...")
    tool_config = {
        'sensitivity_by_state': {
            'normal': 0.0,
            'low_risk_adenoma': 0.2,
            'high_risk_adenoma': 0.4,
            'preclinical_cancer': 0.8,
            'clinical_cancer': 0.9
        },
        'specificity': 0.95,
        'indeterminate_rate': 0.02,
        'inadequate_rate': 0.01
    }
    
    processor = ScreeningResultProcessor(tool_config)
    print(f"   - 特异性: {tool_config['specificity']}")
    print(f"   - 不确定率: {tool_config['indeterminate_rate']}")
    
    # 3. 进行筛查并处理结果
    print("\n3. 进行筛查并处理结果...")
    screening_results = []
    
    for individual_id, individual in list(population.individuals.items())[:20]:  # 筛查前20个个体
        result = processor.process_screening(individual, fit_tool)
        screening_results.append(result)
    
    print(f"   - 完成筛查: {len(screening_results)}人")
    
    # 4. 分析筛查结果
    print("\n4. 筛查结果分析...")
    positive_results = [r for r in screening_results if r.result_type.value == 'positive']
    negative_results = [r for r in screening_results if r.result_type.value == 'negative']
    
    print(f"   - 阳性结果: {len(positive_results)}个")
    print(f"   - 阴性结果: {len(negative_results)}个")
    print(f"   - 阳性率: {len(positive_results)/len(screening_results)*100:.1f}%")
    
    # 5. 假结果分析
    print("\n5. 假结果分析...")
    false_positives = [r for r in screening_results 
                      if r.false_result_impact and 
                      r.false_result_impact.result_type.value == 'false_positive']
    false_negatives = [r for r in screening_results 
                      if r.false_result_impact and 
                      r.false_result_impact.result_type.value == 'false_negative']
    
    print(f"   - 假阳性: {len(false_positives)}个")
    print(f"   - 假阴性: {len(false_negatives)}个")
    
    if false_positives:
        avg_fp_cost = sum(r.false_result_impact.additional_cost for r in false_positives) / len(false_positives)
        print(f"   - 假阳性平均额外成本: ¥{avg_fp_cost:.0f}")
    
    # 6. 后续诊断管理
    print("\n6. 后续诊断管理...")
    healthcare_config = {
        'diagnostic_capacity': {'diagnostic_colonoscopy': 50},
        'waiting_times': {'diagnostic_colonoscopy': 14},
        'facility_locations': {}
    }
    
    followup_manager = FollowupManager(healthcare_config)
    
    appointments = []
    for result in positive_results:
        individual = population.get_individual(result.individual_id)
        if individual:
            appointment = followup_manager.schedule_followup(result, individual)
            if appointment:
                appointments.append(appointment)
    
    print(f"   - 安排诊断预约: {len(appointments)}个")
    if appointments:
        avg_waiting = sum(a.waiting_time_days for a in appointments) / len(appointments)
        print(f"   - 平均等待时间: {avg_waiting:.1f}天")
    
    # 7. 统计分析
    print("\n7. 统计分析...")
    analytics = ScreeningAnalytics()
    report = analytics.generate_screening_report(screening_results)
    
    if 'summary_statistics' in report:
        stats = report['summary_statistics']
        print(f"   - 平均置信度: {stats.average_confidence_score:.2f}")
        print(f"   - 平均质量分数: {stats.average_quality_score:.2f}")
        print(f"   - 总成本: ¥{stats.total_cost:.0f}")
    
    if 'performance_metrics' in report:
        metrics = report['performance_metrics']
        print(f"   - 敏感性: {metrics.sensitivity:.2%}")
        print(f"   - 特异性: {metrics.specificity:.2%}")
        print(f"   - 阳性预测值: {metrics.positive_predictive_value:.2%}")
    
    # 8. 效果评估
    print("\n8. 筛查效果评估...")
    effectiveness = EffectivenessMetrics(population)
    effectiveness.add_screening_results(screening_results)
    
    detection_metrics = effectiveness.calculate_detection_metrics()
    survival_metrics = effectiveness.calculate_survival_benefit()
    
    print(f"   - 腺瘤检出率: {detection_metrics.adenoma_detection_rate:.1f}/1000次筛查")
    print(f"   - 癌症检出率: {detection_metrics.preclinical_cancer_detection_rate + detection_metrics.clinical_cancer_detection_rate:.1f}/1000次筛查")
    print(f"   - 总生存获益: {survival_metrics.life_years_gained:.1f}年")
    print(f"   - 质量调整生存年: {survival_metrics.quality_adjusted_life_years:.1f}QALY")
    
    # 9. 质量监控
    print("\n9. 质量监控...")
    alerts = analytics.monitor_screening_quality(screening_results)
    
    if alerts:
        print(f"   - 质量警报: {len(alerts)}个")
        for alert in alerts[:3]:  # 显示前3个警报
            print(f"     * {alert.alert_type}: {alert.message}")
    else:
        print("   - 无质量警报，筛查质量良好")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)


if __name__ == "__main__":
    demonstrate_screening_results_processing()
