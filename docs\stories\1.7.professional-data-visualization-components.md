# Story 1.7: 专业数据可视化组件库

## Status

Draft

## Story

**As a** 研究人员和数据分析师，
**I want** 拥有专业的科学数据可视化组件库来展示复杂的模拟结果和分析数据，
**so that** 能够通过高质量的交互式图表进行数据探索、结果分析和科学报告生成。

## Acceptance Criteria

1. 实现科学线图组件（支持多条数据线、置信区间、导出功能）
2. 创建交互式柱状图（分组堆叠、动态排序、数据钻取功能）
3. 实现参数敏感性热力图（颜色渐变映射、交互式探索）
4. 创建成本效益散点图（气泡图、象限分析、策略比较）
5. 实现模拟进度监控组件（环形进度指示器、实时更新）
6. 添加大数据渲染优化（支持10万+数据点、OpenGL加速）
7. 实现图表联动筛选和交互特性（缩放、平移、选择）
8. 创建图表导出功能（PNG、SVG、PDF格式）
9. 添加专业色彩方案和科学图表样式
10. 确保符合ai-frontend-prompts.md的专业科学研究界面标准

## Tasks / Subtasks

- [ ] 任务1：实现科学线图组件 (AC: 1)
  - [ ] 创建src/interfaces/desktop/widgets/charts/scientific_line_chart.py
  - [ ] 实现ScientificLineChart类，基于QCustomPlot
  - [ ] 添加多条数据线支持（最多10条，不同颜色和样式）
  - [ ] 实现置信区间阴影显示（半透明填充）
  - [ ] 添加数据点悬停详情（QToolTip显示具体数值）
  - [ ] 实现可缩放和平移功能（鼠标滚轮、拖拽）
  - [ ] 创建交互式图例（点击隐藏/显示线条）
  - [ ] 添加网格线和坐标轴标签自定义
  - [ ] 实现图表导出功能（PNG、SVG、PDF按钮）

- [ ] 任务2：创建交互式柱状图组件 (AC: 2)
  - [ ] 创建src/interfaces/desktop/widgets/charts/interactive_bar_chart.py
  - [ ] 实现InteractiveBarChart类，支持分组和堆叠模式
  - [ ] 添加动态排序功能（升序/降序切换）
  - [ ] 实现数据筛选控件（QCheckBox多选）
  - [ ] 创建数据钻取功能（点击展开详情对话框）
  - [ ] 添加比较模式（并排显示多个数据集）
  - [ ] 实现动画过渡效果（数据更新时平滑过渡）
  - [ ] 添加数据标签和值显示选项

- [ ] 任务3：实现参数敏感性热力图 (AC: 3)
  - [ ] 创建src/interfaces/desktop/widgets/charts/sensitivity_heatmap.py
  - [ ] 实现SensitivityHeatmap类，基于QCustomPlot热力图
  - [ ] 添加参数名称作为行列标签
  - [ ] 实现颜色渐变映射敏感性强度（红-黄-绿色谱）
  - [ ] 创建QToolTip显示具体敏感性数值
  - [ ] 添加行列排序和分组功能
  - [ ] 实现颜色图例和数值范围显示
  - [ ] 添加热力图缩放和导航功能

- [ ] 任务4：创建成本效益散点图 (AC: 4)
  - [ ] 创建src/interfaces/desktop/widgets/charts/cost_effectiveness_scatter.py
  - [ ] 实现CostEffectivenessScatter类，支持气泡图模式
  - [ ] 设置X轴：增量效果，Y轴：增量成本
  - [ ] 实现气泡大小映射人群规模
  - [ ] 添加象限分割线（成本效益阈值线）
  - [ ] 创建数据点选择和高亮功能
  - [ ] 添加策略标签和交互式图例
  - [ ] 实现ICER计算和显示

- [ ] 任务5：实现模拟进度监控组件 (AC: 5)
  - [ ] 创建src/interfaces/desktop/widgets/progress/simulation_progress_monitor.py
  - [ ] 实现SimulationProgressMonitor类，包含环形进度指示器
  - [ ] 创建多层进度显示（总体进度/当前阶段进度）
  - [ ] 添加QTimer实时更新机制
  - [ ] 实现预估完成时间计算和显示
  - [ ] 创建暂停/恢复控制按钮
  - [ ] 添加进度历史记录和速度监控
  - [ ] 实现进度状态持久化

- [ ] 任务6：添加大数据渲染优化 (AC: 6)
  - [ ] 扩展src/interfaces/desktop/widgets/charts/base_chart.py
  - [ ] 实现OpenGL硬件加速渲染（QOpenGLWidget集成）
  - [ ] 添加数据虚拟化和分页机制（支持10万+数据点）
  - [ ] 实现LOD（细节层次）技术
  - [ ] 创建视口裁剪（只渲染可见部分）
  - [ ] 添加数据聚合和采样显示
  - [ ] 实现内存使用优化和垃圾回收
  - [ ] 创建性能监控和调试工具

- [ ] 任务7：实现图表联动和交互特性 (AC: 7)
  - [ ] 创建src/interfaces/desktop/widgets/charts/chart_interaction_manager.py
  - [ ] 实现ChartInteractionManager图表联动管理器
  - [ ] 添加图表间数据筛选联动（信号槽机制）
  - [ ] 实现统一的缩放和平移控制
  - [ ] 创建数据选择和高亮同步
  - [ ] 添加实时数据更新广播（QThread）
  - [ ] 实现多显示器支持
  - [ ] 创建交互状态持久化

- [ ] 任务8：创建图表导出系统 (AC: 8)
  - [ ] 创建src/interfaces/desktop/services/chart_export_service.py
  - [ ] 实现ChartExportService导出服务类
  - [ ] 添加PNG格式导出（高分辨率、可配置DPI）
  - [ ] 实现SVG矢量格式导出
  - [ ] 创建PDF格式导出（多页支持）
  - [ ] 添加批量导出功能
  - [ ] 实现导出预览和设置对话框
  - [ ] 创建导出模板和样式配置

- [ ] 任务9：添加专业色彩方案和样式 (AC: 9)
  - [ ] 创建src/interfaces/desktop/resources/styles/chart_themes.py
  - [ ] 实现专业科学研究色彩方案（#1A365D主色调等）
  - [ ] 添加色盲友好的颜色配置
  - [ ] 创建深色和浅色主题切换
  - [ ] 实现自定义字体配置（等宽字体支持）
  - [ ] 添加图表样式模板系统
  - [ ] 创建品牌化样式配置
  - [ ] 实现样式实时预览功能

## Dev Notes

### 架构上下文 [Source: architecture/frontend-architecture.md]

**图表组件位置**：
- 主要组件：src/interfaces/desktop/widgets/charts/
- 基础图表类：src/interfaces/desktop/widgets/chart_widget.py
- 样式资源：src/interfaces/desktop/resources/styles/
- 服务层：src/interfaces/desktop/services/

**技术栈要求** [Source: architecture/tech-stack.md]：
- **数据可视化**：Matplotlib 3.7+（科学图表绘制）
- **交互式图表**：Plotly 5.15+（交互式数据可视化）
- **桌面框架**：PyQt6 6.5+（原生性能，丰富UI组件）
- **数值计算**：NumPy 1.24+（高性能数值计算）
- **数据处理**：Pandas 2.0+（数据操作和分析）

### 数据模型规范 [Source: architecture/data-models.md]

**可视化数据类型**：
- **疾病状态**：DiseaseState枚举（normal, low_risk_adenoma, high_risk_adenoma等）
- **通路类型**：PathwayType（腺瘤-癌变85%，锯齿状腺瘤15%）
- **筛查结果**：ScreeningResult（敏感性、特异性、成本数据）
- **经济指标**：EconomicResult（成本、QALY、ICER计算）
- **校准数据**：CalibrationTarget（目标值、置信区间）

### 性能要求 [Source: docs/ai-frontend-prompts.md]

**大数据支持**：
- 支持10万+数据点渲染
- 使用OpenGL硬件加速
- 虚拟化长列表（QAbstractItemModel）
- 数据分页和懒加载
- 内存使用优化（及时清理）

**交互性能**：
- 图表联动筛选（信号槽机制）
- 缩放和平移（鼠标事件处理）
- 实时数据更新（QThread）
- 多显示器支持

### 专业设计标准 [Source: docs/ai-frontend-prompts.md]

**色彩方案**：
- 主色调：#1A365D（深蓝色）
- 辅助色：#4A90E2（浅蓝色）
- 强调色：#F5A623（橙色）
- 成功色：#2ECC71（绿色）
- 警告色：#F39C12（黄色）
- 错误色：#E74C3C（红色）

**字体配置**：
- 主要字体：系统默认字体（Windows: Segoe UI, macOS: SF Pro, Linux: Ubuntu）
- 等宽字体：Consolas/Monaco/Ubuntu Mono（数据显示）

### 项目结构对齐 [Source: architecture/unified-project-structure.md]

**文件位置**：
- 图表组件：src/interfaces/desktop/widgets/charts/
- 进度监控：src/interfaces/desktop/widgets/progress/
- 导出服务：src/interfaces/desktop/services/
- 样式资源：src/interfaces/desktop/resources/styles/
- 工具函数：src/interfaces/desktop/utils/

## Testing

### 测试文件位置 [Source: architecture/unified-project-structure.md]

- `tests/unit/widgets/charts/test_scientific_line_chart.py`
- `tests/unit/widgets/charts/test_interactive_bar_chart.py`
- `tests/unit/widgets/charts/test_sensitivity_heatmap.py`
- `tests/unit/widgets/charts/test_cost_effectiveness_scatter.py`
- `tests/unit/widgets/progress/test_simulation_progress_monitor.py`
- `tests/integration/test_chart_interaction_manager.py`
- `tests/performance/test_large_data_rendering.py`

### 测试标准

- 大数据渲染性能测试（10万+数据点）
- 图表交互功能测试（缩放、平移、选择）
- 导出功能测试（PNG、SVG、PDF格式）
- 内存使用和性能基准测试
- 多显示器支持测试
- 色彩方案和主题切换测试

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-31 | 1.0 | 初始故事创建，基于ai-frontend-prompts.md分析 | Bob (SM) |

## Dev Agent Record

*此部分将由开发代理在实施过程中填充*

### Agent Model Used

*待填充*

### Debug Log References

*待填充*

### Completion Notes List

*待填充*

### File List

*待填充*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填充*
