"""
癌症发病建模系统

实现基于年龄的癌症发病正态分布建模，支持性别特异性和解剖位置特异性发病率。
"""

import numpy as np
from typing import Dict, Optional, Tuple, List, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class CancerIncidenceParameters:
    """癌症发病参数"""
    base_incidence_rate: float      # 基础发病率
    peak_age: float                 # 发病高峰年龄
    age_spread: float               # 年龄分布宽度
    min_age: float = 30.0           # 最小发病年龄
    max_age: float = 100.0          # 最大发病年龄

    def __post_init__(self):
        """参数验证"""
        if self.base_incidence_rate <= 0:
            raise ValueError("基础发病率必须大于0")
        if self.peak_age <= 0:
            raise ValueError("发病高峰年龄必须大于0")
        if self.age_spread <= 0:
            raise ValueError("年龄分布宽度必须大于0")
        if self.min_age < 0:
            raise ValueError("最小发病年龄不能为负")
        if self.max_age <= self.min_age:
            raise ValueError("最大发病年龄必须大于最小发病年龄")


@dataclass
class IncidenceStatistics:
    """发病率统计信息"""
    gender: str
    location: str
    age_group_counts: Dict[str, int] = None
    total_incidences: int = 0
    mean_age_at_incidence: float = 0.0

    def __post_init__(self):
        if self.age_group_counts is None:
            self.age_group_counts = {
                "30-39": 0, "40-49": 0, "50-59": 0, "60-69": 0,
                "70-79": 0, "80-89": 0, "90+": 0
            }

    def update_incidence(self, age: float) -> None:
        """更新发病统计"""
        self.total_incidences += 1

        # 更新年龄组计数
        if age < 40:
            self.age_group_counts["30-39"] += 1
        elif age < 50:
            self.age_group_counts["40-49"] += 1
        elif age < 60:
            self.age_group_counts["50-59"] += 1
        elif age < 70:
            self.age_group_counts["60-69"] += 1
        elif age < 80:
            self.age_group_counts["70-79"] += 1
        elif age < 90:
            self.age_group_counts["80-89"] += 1
        else:
            self.age_group_counts["90+"] += 1

        # 更新平均发病年龄
        if self.total_incidences == 1:
            self.mean_age_at_incidence = age
        else:
            self.mean_age_at_incidence = (
                (self.mean_age_at_incidence * (self.total_incidences - 1) + age)
                / self.total_incidences
            )

    def get_age_distribution(self) -> Dict[str, float]:
        """获取年龄分布比例"""
        if self.total_incidences == 0:
            return {group: 0.0 for group in self.age_group_counts.keys()}

        return {
            group: count / self.total_incidences
            for group, count in self.age_group_counts.items()
        }


class CancerIncidenceModel:
    """癌症发病建模类

    基于年龄的癌症发病正态分布建模，支持性别和解剖位置特异性。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化癌症发病模型

        Args:
            config: 配置字典，包含各性别和位置的发病参数
        """
        self.config = config or {}
        self.parameters: Dict[str, Dict[str, CancerIncidenceParameters]] = {}
        self.statistics: Dict[str, Dict[str, IncidenceStatistics]] = {}
        self.random_state = np.random.RandomState(42)

        # 初始化默认参数
        self._initialize_default_parameters()

        # 如果提供了配置，则更新参数
        if config:
            self._load_configuration(config)

    def _initialize_default_parameters(self) -> None:
        """初始化默认的癌症发病参数"""
        # 基于故事中Dev Notes的癌症发病参数
        default_params = {
            "male": {
                "proximal_colon": CancerIncidenceParameters(
                    base_incidence_rate=0.0001,
                    peak_age=65.0,
                    age_spread=10.0,
                    min_age=30.0,
                    max_age=100.0
                ),
                "distal_colon": CancerIncidenceParameters(
                    base_incidence_rate=0.00008,
                    peak_age=62.0,
                    age_spread=12.0,
                    min_age=30.0,
                    max_age=100.0
                ),
                "rectum": CancerIncidenceParameters(
                    base_incidence_rate=0.00006,
                    peak_age=60.0,
                    age_spread=8.0,
                    min_age=30.0,
                    max_age=100.0
                )
            },
            "female": {
                "proximal_colon": CancerIncidenceParameters(
                    base_incidence_rate=0.00008,
                    peak_age=68.0,
                    age_spread=12.0,
                    min_age=30.0,
                    max_age=100.0
                ),
                "distal_colon": CancerIncidenceParameters(
                    base_incidence_rate=0.00006,
                    peak_age=65.0,
                    age_spread=14.0,
                    min_age=30.0,
                    max_age=100.0
                ),
                "rectum": CancerIncidenceParameters(
                    base_incidence_rate=0.00004,
                    peak_age=63.0,
                    age_spread=10.0,
                    min_age=30.0,
                    max_age=100.0
                )
            }
        }

        self.parameters.update(default_params)

        # 初始化统计对象
        for gender in ["male", "female"]:
            self.statistics[gender] = {}
            for location in ["proximal_colon", "distal_colon", "rectum"]:
                self.statistics[gender][location] = IncidenceStatistics(
                    gender=gender,
                    location=location
                )

    def _load_configuration(self, config: Dict[str, Any]) -> None:
        """从配置字典加载参数"""
        for gender, gender_config in config.items():
            if isinstance(gender_config, dict):
                if gender not in self.parameters:
                    self.parameters[gender] = {}
                    self.statistics[gender] = {}

                for location, params in gender_config.items():
                    if isinstance(params, dict):
                        try:
                            base_rate = float(params['base_incidence_rate'])
                            params_obj = CancerIncidenceParameters(
                                base_incidence_rate=base_rate,
                                peak_age=float(params['peak_age']),
                                age_spread=float(params['age_spread']),
                                min_age=float(params.get('min_age', 30.0)),
                                max_age=float(params.get('max_age', 100.0))
                            )
                            self.parameters[gender][location] = params_obj

                            # 初始化统计对象
                            if location not in self.statistics[gender]:
                                self.statistics[gender][location] = IncidenceStatistics(
                                    gender=gender,
                                    location=location
                                )

                        except (KeyError, ValueError, TypeError) as e:
                            logger.warning(f"配置 {gender}-{location} 的参数时出错: {e}")

    def calculate_cancer_incidence_probability(
        self,
        age: float,
        gender: str,
        anatomical_location: str
    ) -> float:
        """
        计算基于年龄的癌症发病概率（正态分布）

        Args:
            age: 年龄
            gender: 性别 ("male" 或 "female")
            anatomical_location: 解剖位置 ("proximal_colon", "distal_colon", "rectum")

        Returns:
            float: 年发病概率
        """
        if gender not in self.parameters:
            raise ValueError(f"未知的性别: {gender}")

        if anatomical_location not in self.parameters[gender]:
            raise ValueError(f"未知的解剖位置: {anatomical_location}")

        params = self.parameters[gender][anatomical_location]

        # 年龄相关的发病率（正态分布）
        age_factor = np.exp(
            -0.5 * ((age - params.peak_age) / params.age_spread) ** 2
        )

        # 基础发病率
        annual_probability = params.base_incidence_rate * age_factor

        # 确保概率在有效范围内
        return max(0.0, min(annual_probability, 1.0))

    def calculate_gender_adjusted_cancer_incidence_probability(
        self,
        age: float,
        gender: str,
        anatomical_location: str,
        risk_factors: Optional[Dict[str, float]] = None,
        progression_history: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        计算性别调整的癌症发病概率

        Args:
            age: 年龄
            gender: 性别
            anatomical_location: 解剖位置
            risk_factors: 风险因素字典
            progression_history: 疾病进展历史

        Returns:
            float: 性别调整的年发病概率
        """
        # 获取基础发病概率
        base_probability = self.calculate_cancer_incidence_probability(
            age, gender, anatomical_location
        )

        # 性别特异性调整因子
        gender_adjustment = self._get_gender_specific_adjustment(
            gender, anatomical_location, age
        )

        # 风险因素调整
        risk_adjustment = 1.0
        if risk_factors:
            risk_adjustment = self._calculate_risk_factor_adjustment(
                risk_factors, gender
            )

        # 疾病进展历史调整
        progression_adjustment = 1.0
        if progression_history:
            progression_adjustment = self._calculate_progression_history_adjustment(
                progression_history, gender
            )

        # 计算最终概率
        adjusted_probability = (
            base_probability *
            gender_adjustment *
            risk_adjustment *
            progression_adjustment
        )

        # 确保概率在有效范围内
        return max(0.0, min(adjusted_probability, 1.0))

    def _get_gender_specific_adjustment(
        self,
        gender: str,
        anatomical_location: str,
        age: float
    ) -> float:
        """
        获取性别特异性调整因子

        Args:
            gender: 性别
            anatomical_location: 解剖位置
            age: 年龄

        Returns:
            float: 性别特异性调整因子
        """
        # 基于医学文献的性别特异性调整因子
        gender_adjustments = {
            "male": {
                "proximal_colon": 1.1 + (age - 50) * 0.002,  # 男性近端结肠癌风险随年龄增加
                "distal_colon": 1.2 + (age - 50) * 0.001,   # 男性远端结肠癌风险较高
                "rectum": 1.3 + (age - 50) * 0.0015         # 男性直肠癌风险最高
            },
            "female": {
                "proximal_colon": 1.0,                       # 女性近端结肠癌风险基准
                "distal_colon": 0.9 - (age - 50) * 0.001,   # 女性远端结肠癌风险较低
                "rectum": 0.8 - (age - 50) * 0.0005         # 女性直肠癌风险最低
            }
        }

        adjustment = gender_adjustments.get(gender, {}).get(anatomical_location, 1.0)

        # 确保调整因子在合理范围内
        return max(0.5, min(adjustment, 2.0))

    def _calculate_risk_factor_adjustment(
        self,
        risk_factors: Dict[str, float],
        gender: str
    ) -> float:
        """
        计算风险因素调整倍数

        Args:
            risk_factors: 风险因素字典
            gender: 性别

        Returns:
            float: 风险因素调整倍数
        """
        # 性别特异性风险因素权重
        risk_weights = {
            "male": {
                "smoking": 1.4,      # 男性吸烟风险更高
                "alcohol": 1.3,      # 男性饮酒风险更高
                "obesity": 1.2,      # 男性肥胖风险较高
                "family_history": 1.0,  # 家族史风险无性别差异
                "inflammatory_bowel_disease": 1.0  # 炎症性肠病风险无性别差异
            },
            "female": {
                "smoking": 1.2,      # 女性吸烟风险较低
                "alcohol": 1.1,      # 女性饮酒风险较低
                "obesity": 1.3,      # 女性肥胖风险较高
                "family_history": 1.0,  # 家族史风险无性别差异
                "inflammatory_bowel_disease": 1.1   # 女性炎症性肠病风险稍高
            }
        }

        weights = risk_weights.get(gender, risk_weights["female"])
        adjustment = 1.0

        # 应用各种风险因素的性别特异性权重
        for factor, value in risk_factors.items():
            if factor in weights:
                # 风险因素值通常在0-1之间，转换为倍数
                factor_multiplier = 1.0 + (value * (weights[factor] - 1.0))
                adjustment *= factor_multiplier

        return adjustment

    def _calculate_progression_history_adjustment(
        self,
        progression_history: Dict[str, Any],
        gender: str
    ) -> float:
        """
        计算疾病进展历史调整倍数

        Args:
            progression_history: 疾病进展历史
            gender: 性别

        Returns:
            float: 进展历史调整倍数
        """
        adjustment = 1.0

        # 腺瘤历史对癌症发病的影响
        if "adenoma_history" in progression_history:
            adenoma_count = progression_history["adenoma_history"].get("count", 0)
            adenoma_size = progression_history["adenoma_history"].get("max_size", 0)

            # 性别特异性腺瘤历史影响
            if gender == "male":
                adjustment *= 1.0 + adenoma_count * 0.15 + adenoma_size * 0.02
            else:
                adjustment *= 1.0 + adenoma_count * 0.12 + adenoma_size * 0.015

        # 筛查历史对发病概率的影响
        if "screening_history" in progression_history:
            last_screening = progression_history["screening_history"].get("last_screening_years", 10)
            # 筛查间隔越长，发病概率越高
            screening_adjustment = 1.0 + (last_screening - 1) * 0.05
            adjustment *= screening_adjustment

        return adjustment

    def sample_cancer_incidence(
        self,
        age: float,
        gender: str,
        anatomical_location: str
    ) -> bool:
        """
        抽样癌症发病事件

        Args:
            age: 年龄
            gender: 性别
            anatomical_location: 解剖位置

        Returns:
            bool: 是否发生癌症
        """
        probability = self.calculate_cancer_incidence_probability(
            age, gender, anatomical_location
        )

        # 伯努利试验
        occurs = self.random_state.random() < probability

        # 如果发生，更新统计信息
        if occurs:
            self.statistics[gender][anatomical_location].update_incidence(age)

        return bool(occurs)

    def get_parameters(
        self, gender: str, anatomical_location: str
    ) -> Optional[CancerIncidenceParameters]:
        """获取指定性别和位置的发病参数"""
        return self.parameters.get(gender, {}).get(anatomical_location)

    def update_parameters(
        self,
        gender: str,
        anatomical_location: str,
        params: CancerIncidenceParameters
    ) -> None:
        """更新指定性别和位置的发病参数"""
        if gender not in self.parameters:
            self.parameters[gender] = {}
            self.statistics[gender] = {}

        self.parameters[gender][anatomical_location] = params

        # 如果统计对象不存在，则创建
        if anatomical_location not in self.statistics[gender]:
            self.statistics[gender][anatomical_location] = IncidenceStatistics(
                gender=gender,
                location=anatomical_location
            )

    def get_statistics(
        self, gender: str, anatomical_location: str
    ) -> Optional[IncidenceStatistics]:
        """获取指定性别和位置的统计信息"""
        return self.statistics.get(gender, {}).get(anatomical_location)

    def get_all_statistics(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """获取所有统计信息"""
        result = {}
        for gender, gender_stats in self.statistics.items():
            result[gender] = {}
            for location, location_stats in gender_stats.items():
                result[gender][location] = {
                    "total_incidences": location_stats.total_incidences,
                    "mean_age_at_incidence": location_stats.mean_age_at_incidence,
                    "age_distribution": location_stats.get_age_distribution()
                }
        return result

    def reset_statistics(
        self,
        gender: Optional[str] = None,
        anatomical_location: Optional[str] = None
    ) -> None:
        """重置统计信息"""
        if gender is None:
            # 重置所有统计
            for gender_stats in self.statistics.values():
                for location_stats in gender_stats.values():
                    location_stats.total_incidences = 0
                    location_stats.mean_age_at_incidence = 0.0
                    location_stats.age_group_counts = {
                        "30-39": 0, "40-49": 0, "50-59": 0, "60-69": 0,
                        "70-79": 0, "80-89": 0, "90+": 0
                    }
        elif anatomical_location is None:
            # 重置指定性别的所有统计
            if gender in self.statistics:
                for location_stats in self.statistics[gender].values():
                    location_stats.total_incidences = 0
                    location_stats.mean_age_at_incidence = 0.0
                    location_stats.age_group_counts = {
                        "30-39": 0, "40-49": 0, "50-59": 0, "60-69": 0,
                        "70-79": 0, "80-89": 0, "90+": 0
                    }
        else:
            # 重置指定性别和位置的统计
            if (gender in self.statistics
                    and anatomical_location in self.statistics[gender]):
                location_stats = self.statistics[gender][anatomical_location]
                location_stats.total_incidences = 0
                location_stats.mean_age_at_incidence = 0.0
                location_stats.age_group_counts = {
                    "30-39": 0, "40-49": 0, "50-59": 0, "60-69": 0,
                    "70-79": 0, "80-89": 0, "90+": 0
                }

    def validate_parameters(self) -> Dict[str, List[str]]:
        """验证所有参数"""
        validation_results = {}

        for gender, gender_params in self.parameters.items():
            for location, params in gender_params.items():
                key = f"{gender}-{location}"
                errors = []

                # 基本参数验证
                if params.base_incidence_rate <= 0:
                    errors.append("基础发病率必须大于0")
                if params.base_incidence_rate > 1.0:
                    errors.append("基础发病率不能超过1.0")
                if params.peak_age <= 0:
                    errors.append("发病高峰年龄必须大于0")
                if params.age_spread <= 0:
                    errors.append("年龄分布宽度必须大于0")
                if params.min_age < 0:
                    errors.append("最小发病年龄不能为负")
                if params.max_age <= params.min_age:
                    errors.append("最大发病年龄必须大于最小发病年龄")

                # 医学合理性验证
                if params.peak_age < 40 or params.peak_age > 90:
                    errors.append("发病高峰年龄应在40-90岁之间")
                if params.age_spread < 5 or params.age_spread > 30:
                    errors.append("年龄分布宽度应在5-30年之间")

                if errors:
                    validation_results[key] = errors

        return validation_results

    def export_configuration(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """导出当前配置"""
        config = {}
        for gender, gender_params in self.parameters.items():
            config[gender] = {}
            for location, params in gender_params.items():
                config[gender][location] = {
                    'base_incidence_rate': params.base_incidence_rate,
                    'peak_age': params.peak_age,
                    'age_spread': params.age_spread,
                    'min_age': params.min_age,
                    'max_age': params.max_age
                }
        return config

    def set_random_seed(self, seed: int) -> None:
        """设置随机数种子"""
        self.random_state = np.random.RandomState(seed)

    def get_supported_combinations(self) -> List[Tuple[str, str]]:
        """获取支持的性别-位置组合"""
        combinations = []
        for gender in self.parameters.keys():
            for location in self.parameters[gender].keys():
                combinations.append((gender, location))
        return combinations

    def calculate_age_specific_incidence_curve(
        self,
        gender: str,
        anatomical_location: str,
        age_range: Tuple[float, float] = (30, 100),
        age_step: float = 1.0
    ) -> Tuple[List[float], List[float]]:
        """
        计算年龄特异性发病率曲线

        Args:
            gender: 性别
            anatomical_location: 解剖位置
            age_range: 年龄范围 (最小年龄, 最大年龄)
            age_step: 年龄步长

        Returns:
            Tuple[List[float], List[float]]: (年龄列表, 发病率列表)
        """
        ages = []
        incidence_rates = []

        age = age_range[0]
        while age <= age_range[1]:
            ages.append(age)
            rate = self.calculate_cancer_incidence_probability(
                age, gender, anatomical_location
            )
            incidence_rates.append(rate)
            age += age_step

        return ages, incidence_rates

    def estimate_lifetime_risk(
        self,
        gender: str,
        anatomical_location: str,
        start_age: float = 30.0,
        end_age: float = 85.0
    ) -> float:
        """
        估算终生癌症风险

        Args:
            gender: 性别
            anatomical_location: 解剖位置
            start_age: 起始年龄
            end_age: 结束年龄

        Returns:
            float: 终生风险（概率）
        """
        # 使用简化的累积风险计算
        cumulative_risk = 0.0
        age = start_age

        while age < end_age:
            annual_risk = self.calculate_cancer_incidence_probability(
                age, gender, anatomical_location
            )
            # 假设每年的风险是独立的
            cumulative_risk += annual_risk * (1 - cumulative_risk)
            age += 1.0

        return cumulative_risk
