"""
筛查结果处理器

实现筛查结果判定逻辑，包括阳性/阴性/不确定结果的处理，
以及假阳性和假阴性结果的建模。
"""

import random
import logging
from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta
from enum import Enum

from src.core.individual import Individual
from src.core.enums import DiseaseState
from .enums import ScreeningToolType, ScreeningResult as BasicScreeningResult
from .screening_tool import ScreeningTool

logger = logging.getLogger(__name__)


class ScreeningResultType(Enum):
    """详细筛查结果类型枚举"""
    NEGATIVE = "negative"
    POSITIVE = "positive"
    INDETERMINATE = "indeterminate"
    INADEQUATE = "inadequate"


class FollowupAction(Enum):
    """后续行动枚举"""
    NONE = "none"
    REPEAT_SCREENING = "repeat_screening"
    DIAGNOSTIC_COLONOSCOPY = "diagnostic_colonoscopy"
    SPECIALIST_REFERRAL = "specialist_referral"
    IMMEDIATE_TREATMENT = "immediate_treatment"


class FalseResultType(Enum):
    """假结果类型枚举"""
    TRUE_RESULT = "true_result"
    FALSE_POSITIVE = "false_positive"
    FALSE_NEGATIVE = "false_negative"


@dataclass
class FalseResultImpact:
    """假结果影响数据结构"""
    result_type: FalseResultType
    psychological_impact_score: float  # 心理影响分数 (0-1)
    additional_cost: float  # 额外成本
    delay_in_detection_days: int = 0  # 检测延迟天数（仅假阴性）
    unnecessary_procedures: int = 0  # 不必要的检查次数（仅假阳性）
    anxiety_duration_days: int = 0  # 焦虑持续天数

    def __post_init__(self):
        """验证假结果影响数据"""
        if not 0 <= self.psychological_impact_score <= 1:
            raise ValueError("心理影响分数必须在0-1之间")
        if self.additional_cost < 0:
            raise ValueError("额外成本不能为负数")
        if self.delay_in_detection_days < 0:
            raise ValueError("检测延迟天数不能为负数")
        if self.unnecessary_procedures < 0:
            raise ValueError("不必要检查次数不能为负数")
        if self.anxiety_duration_days < 0:
            raise ValueError("焦虑持续天数不能为负数")


@dataclass
class ScreeningResult:
    """详细筛查结果数据结构"""
    individual_id: str
    tool_type: ScreeningToolType
    result_type: ScreeningResultType
    test_date: datetime
    confidence_score: float
    raw_value: Optional[float] = None
    is_true_positive: Optional[bool] = None
    is_true_negative: Optional[bool] = None
    followup_action: FollowupAction = FollowupAction.NONE
    processing_time: float = 0.0
    cost: float = 0.0
    quality_score: float = 1.0
    false_result_impact: Optional[FalseResultImpact] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """验证筛查结果数据"""
        if not 0 <= self.confidence_score <= 1:
            raise ValueError("置信度分数必须在0-1之间")
        if not 0 <= self.quality_score <= 1:
            raise ValueError("质量分数必须在0-1之间")
        if self.processing_time < 0:
            raise ValueError("处理时间不能为负数")
        if self.cost < 0:
            raise ValueError("成本不能为负数")


class ScreeningResultProcessor:
    """筛查结果处理器"""
    
    def __init__(self, tool_config: Dict[str, Any]):
        """
        初始化筛查结果处理器
        
        Args:
            tool_config: 筛查工具配置，包含敏感性和特异性参数
        """
        self.tool_config = tool_config
        self.sensitivity_by_state = tool_config.get('sensitivity_by_state', {})
        self.specificity = tool_config.get('specificity', 0.9)
        self.indeterminate_rate = tool_config.get('indeterminate_rate', 0.02)
        self.inadequate_rate = tool_config.get('inadequate_rate', 0.01)
        
        # 质量控制参数
        self.quality_threshold = tool_config.get('quality_threshold', 0.8)
        self.confidence_threshold = tool_config.get('confidence_threshold', 0.7)
        
        logger.info(f"初始化筛查结果处理器，特异性: {self.specificity}")

    def process_screening(
        self, 
        individual: Individual, 
        tool: ScreeningTool,
        simulation_time: Optional[datetime] = None
    ) -> ScreeningResult:
        """
        处理筛查并生成结果
        
        Args:
            individual: 被筛查的个体
            tool: 使用的筛查工具
            simulation_time: 模拟时间
            
        Returns:
            筛查结果对象
        """
        if simulation_time is None:
            simulation_time = datetime.now()
            
        # 获取个体真实疾病状态
        true_state = individual.current_disease_state
        
        # 计算检测概率
        detection_prob = self._calculate_detection_probability(individual, tool)
        
        # 生成筛查结果类型
        result_type = self._determine_result_type(true_state, detection_prob)
        
        # 计算置信度分数
        confidence_score = self._calculate_confidence_score(detection_prob, result_type)
        
        # 计算质量分数
        quality_score = self._calculate_quality_score(individual, tool)
        
        # 创建结果对象
        result = ScreeningResult(
            individual_id=individual.individual_id,
            tool_type=tool.tool_type,
            result_type=result_type,
            test_date=simulation_time,
            confidence_score=confidence_score,
            quality_score=quality_score,
            is_true_positive=self._is_true_positive(true_state, result_type),
            is_true_negative=self._is_true_negative(true_state, result_type),
            processing_time=self._calculate_processing_time(tool),
            cost=self._calculate_screening_cost(tool)
        )
        
        # 确定后续行动
        result.followup_action = self._determine_followup_action(result)

        # 处理假结果影响
        result.false_result_impact = self._calculate_false_result_impact(result, true_state)

        # 更新个体筛查历史
        self._update_individual_screening_history(individual, result)

        # 添加额外数据
        result.additional_data = {
            'true_disease_state': true_state.value,
            'detection_probability': detection_prob,
            'tool_sensitivity': self._get_sensitivity_for_state(true_state),
            'tool_specificity': self.specificity
        }
        
        logger.debug(f"处理筛查结果: {individual.individual_id}, "
                    f"工具: {tool.tool_type.value}, 结果: {result_type.value}")
        
        return result

    def _calculate_detection_probability(
        self, 
        individual: Individual, 
        tool: ScreeningTool
    ) -> float:
        """
        计算检测概率
        
        Args:
            individual: 个体
            tool: 筛查工具
            
        Returns:
            检测概率
        """
        true_state = individual.current_disease_state
        
        # 获取基础敏感性
        base_sensitivity = self._get_sensitivity_for_state(true_state)
        
        # 考虑个体特征的调整因子
        adjustment_factor = self._calculate_individual_adjustment(individual, tool)
        
        # 最终检测概率
        detection_prob = base_sensitivity * adjustment_factor
        
        # 确保概率在有效范围内
        return max(0.0, min(1.0, detection_prob))

    def _get_sensitivity_for_state(self, state: DiseaseState) -> float:
        """获取特定疾病状态的敏感性"""
        return self.sensitivity_by_state.get(state.value, 0.0)

    def _calculate_individual_adjustment(
        self, 
        individual: Individual, 
        tool: ScreeningTool
    ) -> float:
        """
        计算个体特征调整因子
        
        Args:
            individual: 个体
            tool: 筛查工具
            
        Returns:
            调整因子 (0.8-1.2)
        """
        adjustment = 1.0
        
        # 年龄调整
        age = individual.get_current_age()
        if age > 70:
            adjustment *= 0.95  # 高龄可能影响检测效果
        elif age < 50:
            adjustment *= 0.98  # 年轻人病变可能更小
            
        # 性别调整（某些工具可能有性别差异）
        if hasattr(individual, 'gender') and individual.gender:
            # 这里可以根据具体工具添加性别特异性调整
            pass
            
        # 确保调整因子在合理范围内
        return max(0.8, min(1.2, adjustment))

    def _determine_result_type(
        self, 
        true_state: DiseaseState, 
        detection_prob: float
    ) -> ScreeningResultType:
        """
        确定筛查结果类型
        
        Args:
            true_state: 真实疾病状态
            detection_prob: 检测概率
            
        Returns:
            筛查结果类型
        """
        # 首先检查是否为样本不足
        if random.random() < self.inadequate_rate:
            return ScreeningResultType.INADEQUATE
            
        # 检查是否为不确定结果
        if random.random() < self.indeterminate_rate:
            return ScreeningResultType.INDETERMINATE
            
        # 正常结果判定
        if true_state == DiseaseState.NORMAL:
            # 正常状态：基于特异性判定
            if random.random() > self.specificity:
                return ScreeningResultType.POSITIVE  # 假阳性
            else:
                return ScreeningResultType.NEGATIVE  # 真阴性
        else:
            # 疾病状态：基于敏感性判定
            if random.random() < detection_prob:
                return ScreeningResultType.POSITIVE  # 真阳性
            else:
                return ScreeningResultType.NEGATIVE  # 假阴性

    def _calculate_confidence_score(
        self, 
        detection_prob: float, 
        result_type: ScreeningResultType
    ) -> float:
        """计算结果置信度分数"""
        if result_type == ScreeningResultType.INADEQUATE:
            return 0.0
        elif result_type == ScreeningResultType.INDETERMINATE:
            return 0.5
        elif result_type == ScreeningResultType.POSITIVE:
            return detection_prob
        else:  # NEGATIVE
            return 1.0 - detection_prob

    def _calculate_quality_score(
        self, 
        individual: Individual, 
        tool: ScreeningTool
    ) -> float:
        """计算检查质量分数"""
        # 基础质量分数
        base_quality = 1.0
        
        # 根据个体特征调整
        age = individual.get_current_age()
        if age > 75:
            base_quality *= 0.95  # 高龄可能影响检查质量
            
        # 添加随机变异
        quality_variation = random.uniform(0.9, 1.0)
        
        return max(0.0, min(1.0, base_quality * quality_variation))

    def _is_true_positive(
        self, 
        true_state: DiseaseState, 
        result_type: ScreeningResultType
    ) -> Optional[bool]:
        """判断是否为真阳性"""
        if result_type != ScreeningResultType.POSITIVE:
            return None
        return true_state != DiseaseState.NORMAL

    def _is_true_negative(
        self, 
        true_state: DiseaseState, 
        result_type: ScreeningResultType
    ) -> Optional[bool]:
        """判断是否为真阴性"""
        if result_type != ScreeningResultType.NEGATIVE:
            return None
        return true_state == DiseaseState.NORMAL

    def _determine_followup_action(self, result: ScreeningResult) -> FollowupAction:
        """确定后续行动"""
        if result.result_type == ScreeningResultType.POSITIVE:
            if result.tool_type in [ScreeningToolType.COLONOSCOPY]:
                return FollowupAction.IMMEDIATE_TREATMENT
            else:
                return FollowupAction.DIAGNOSTIC_COLONOSCOPY
        elif result.result_type == ScreeningResultType.INDETERMINATE:
            return FollowupAction.REPEAT_SCREENING
        elif result.result_type == ScreeningResultType.INADEQUATE:
            return FollowupAction.REPEAT_SCREENING
        else:
            return FollowupAction.NONE

    def _calculate_processing_time(self, tool: ScreeningTool) -> float:
        """计算处理时间（分钟）"""
        base_times = {
            ScreeningToolType.FIT: 5.0,
            ScreeningToolType.COLONOSCOPY: 30.0,
            ScreeningToolType.SIGMOIDOSCOPY: 20.0,
            ScreeningToolType.CTCOLONOGRAPHY: 15.0,
        }
        base_time = base_times.get(tool.tool_type, 10.0)
        
        # 添加随机变异
        return base_time * random.uniform(0.8, 1.2)

    def _calculate_screening_cost(self, tool: ScreeningTool) -> float:
        """计算筛查成本"""
        if hasattr(tool, 'cost_model') and tool.cost_model:
            return tool.cost_model.calculate_total_cost()
        
        # 默认成本
        default_costs = {
            ScreeningToolType.FIT: 50.0,
            ScreeningToolType.COLONOSCOPY: 1200.0,
            ScreeningToolType.SIGMOIDOSCOPY: 600.0,
            ScreeningToolType.CTCOLONOGRAPHY: 800.0,
        }
        return default_costs.get(tool.tool_type, 100.0)

    def validate_result_quality(self, result: ScreeningResult) -> bool:
        """验证结果质量"""
        if result.quality_score < self.quality_threshold:
            logger.warning(f"筛查结果质量低于阈值: {result.quality_score}")
            return False

        if result.confidence_score < self.confidence_threshold:
            logger.warning(f"筛查结果置信度低于阈值: {result.confidence_score}")
            return False

        return True

    def _calculate_false_result_impact(
        self,
        result: ScreeningResult,
        true_state: DiseaseState
    ) -> Optional[FalseResultImpact]:
        """
        计算假结果的影响

        Args:
            result: 筛查结果
            true_state: 真实疾病状态

        Returns:
            假结果影响对象，如果是真结果则返回None
        """
        # 确定假结果类型
        false_result_type = self._determine_false_result_type(result, true_state)

        if false_result_type == FalseResultType.TRUE_RESULT:
            return None

        # 计算假结果影响
        if false_result_type == FalseResultType.FALSE_POSITIVE:
            return self._calculate_false_positive_impact(result)
        else:  # FALSE_NEGATIVE
            return self._calculate_false_negative_impact(result, true_state)

    def _determine_false_result_type(
        self,
        result: ScreeningResult,
        true_state: DiseaseState
    ) -> FalseResultType:
        """确定假结果类型"""
        if result.result_type == ScreeningResultType.POSITIVE:
            if true_state == DiseaseState.NORMAL:
                return FalseResultType.FALSE_POSITIVE
            else:
                return FalseResultType.TRUE_RESULT
        elif result.result_type == ScreeningResultType.NEGATIVE:
            if true_state != DiseaseState.NORMAL:
                return FalseResultType.FALSE_NEGATIVE
            else:
                return FalseResultType.TRUE_RESULT
        else:
            return FalseResultType.TRUE_RESULT

    def _calculate_false_positive_impact(self, result: ScreeningResult) -> FalseResultImpact:
        """计算假阳性影响"""
        # 心理影响分数（假阳性通常造成中等焦虑）
        psychological_impact = random.uniform(0.4, 0.7)

        # 额外成本（后续诊断检查）
        additional_cost = self._calculate_false_positive_cost(result.tool_type)

        # 不必要的检查次数
        unnecessary_procedures = 1  # 至少一次诊断性肠镜
        if random.random() < 0.2:  # 20%可能需要额外检查
            unnecessary_procedures += 1

        # 焦虑持续时间（直到确诊为假阳性）
        anxiety_duration = random.randint(7, 30)  # 7-30天

        return FalseResultImpact(
            result_type=FalseResultType.FALSE_POSITIVE,
            psychological_impact_score=psychological_impact,
            additional_cost=additional_cost,
            unnecessary_procedures=unnecessary_procedures,
            anxiety_duration_days=anxiety_duration
        )

    def _calculate_false_negative_impact(
        self,
        result: ScreeningResult,
        true_state: DiseaseState
    ) -> FalseResultImpact:
        """计算假阴性影响"""
        # 心理影响分数（假阴性可能造成后期发现时的严重心理冲击）
        psychological_impact = random.uniform(0.6, 0.9)

        # 检测延迟（根据疾病状态和筛查间隔估算）
        delay_days = self._calculate_detection_delay(true_state, result.tool_type)

        # 额外成本（延迟发现导致的治疗成本增加）
        additional_cost = self._calculate_false_negative_cost(true_state, delay_days)

        return FalseResultImpact(
            result_type=FalseResultType.FALSE_NEGATIVE,
            psychological_impact_score=psychological_impact,
            additional_cost=additional_cost,
            delay_in_detection_days=delay_days
        )

    def _calculate_false_positive_cost(self, tool_type: ScreeningToolType) -> float:
        """计算假阳性额外成本"""
        # 基础诊断成本（诊断性肠镜）
        base_cost = 1200.0

        # 根据初始筛查工具调整
        if tool_type == ScreeningToolType.FIT:
            # FIT假阳性通常需要肠镜确诊
            return base_cost
        elif tool_type == ScreeningToolType.CTCOLONOGRAPHY:
            # CT假阳性可能需要肠镜 + 额外影像
            return base_cost + 300.0
        else:
            return base_cost

    def _calculate_false_negative_cost(
        self,
        true_state: DiseaseState,
        delay_days: int
    ) -> float:
        """计算假阴性额外成本"""
        # 基础额外成本
        base_cost = 0.0

        # 根据疾病状态和延迟时间计算
        if true_state in [DiseaseState.LOW_RISK_ADENOMA, DiseaseState.HIGH_RISK_ADENOMA]:
            # 腺瘤延迟发现的成本相对较低
            base_cost = delay_days * 2.0  # 每天2元的机会成本
        elif true_state in [DiseaseState.SMALL_SERRATED, DiseaseState.LARGE_SERRATED]:
            # 锯齿状腺瘤延迟发现成本
            base_cost = delay_days * 3.0
        elif true_state == DiseaseState.PRECLINICAL_CANCER:
            # 临床前癌症延迟发现成本较高
            base_cost = delay_days * 10.0
        elif true_state.value.startswith('clinical_cancer'):
            # 临床癌症延迟发现成本很高
            base_cost = delay_days * 20.0

        return base_cost

    def _calculate_detection_delay(
        self,
        true_state: DiseaseState,
        tool_type: ScreeningToolType
    ) -> int:
        """计算检测延迟天数"""
        # 基础筛查间隔（天）
        screening_intervals = {
            ScreeningToolType.FIT: 365,  # 年度筛查
            ScreeningToolType.COLONOSCOPY: 3650,  # 10年筛查
            ScreeningToolType.SIGMOIDOSCOPY: 1825,  # 5年筛查
            ScreeningToolType.CTCOLONOGRAPHY: 1825,  # 5年筛查
        }

        base_interval = screening_intervals.get(tool_type, 365)

        # 根据疾病状态调整延迟
        if true_state in [DiseaseState.LOW_RISK_ADENOMA, DiseaseState.SMALL_SERRATED]:
            # 低风险病变，延迟相对较长
            return int(base_interval * random.uniform(0.8, 1.0))
        elif true_state in [DiseaseState.HIGH_RISK_ADENOMA, DiseaseState.LARGE_SERRATED]:
            # 高风险病变，延迟中等
            return int(base_interval * random.uniform(0.6, 0.8))
        elif true_state == DiseaseState.PRECLINICAL_CANCER:
            # 临床前癌症，延迟较短
            return int(base_interval * random.uniform(0.3, 0.6))
        else:
            # 临床癌症，延迟很短
            return int(base_interval * random.uniform(0.1, 0.3))

    def process_false_result_followup(
        self,
        result: ScreeningResult,
        individual: Individual
    ) -> Dict[str, Any]:
        """
        处理假结果的后续流程

        Args:
            result: 筛查结果
            individual: 个体

        Returns:
            后续处理信息
        """
        if not result.false_result_impact:
            return {"action": "none", "message": "真结果，无需特殊处理"}

        impact = result.false_result_impact

        if impact.result_type == FalseResultType.FALSE_POSITIVE:
            return self._handle_false_positive_followup(result, individual, impact)
        else:  # FALSE_NEGATIVE
            return self._handle_false_negative_followup(result, individual, impact)

    def _handle_false_positive_followup(
        self,
        result: ScreeningResult,
        individual: Individual,
        impact: FalseResultImpact
    ) -> Dict[str, Any]:
        """处理假阳性后续流程"""
        return {
            "action": "false_positive_management",
            "message": "假阳性结果，需要心理支持和医疗解释",
            "recommended_actions": [
                "提供详细的检查结果解释",
                "安排心理咨询支持",
                "制定后续筛查计划",
                "记录假阳性历史"
            ],
            "psychological_support_needed": impact.psychological_impact_score > 0.5,
            "anxiety_duration_days": impact.anxiety_duration_days,
            "additional_cost": impact.additional_cost
        }

    def _handle_false_negative_followup(
        self,
        result: ScreeningResult,
        individual: Individual,
        impact: FalseResultImpact
    ) -> Dict[str, Any]:
        """处理假阴性后续流程"""
        return {
            "action": "false_negative_management",
            "message": "假阴性结果，需要加强监测和治疗",
            "recommended_actions": [
                "立即安排诊断性检查",
                "评估疾病进展情况",
                "调整治疗方案",
                "提供心理支持"
            ],
            "urgent_followup_needed": True,
            "detection_delay_days": impact.delay_in_detection_days,
            "additional_cost": impact.additional_cost,
            "psychological_impact": impact.psychological_impact_score
        }

    def calculate_false_result_statistics(
        self,
        results: List[ScreeningResult]
    ) -> Dict[str, Any]:
        """
        计算假结果统计信息

        Args:
            results: 筛查结果列表

        Returns:
            假结果统计信息
        """
        total_results = len(results)
        if total_results == 0:
            return {}

        false_positives = [r for r in results
                          if r.false_result_impact and
                          r.false_result_impact.result_type == FalseResultType.FALSE_POSITIVE]
        false_negatives = [r for r in results
                          if r.false_result_impact and
                          r.false_result_impact.result_type == FalseResultType.FALSE_NEGATIVE]

        # 计算假阳性统计
        fp_count = len(false_positives)
        fp_rate = fp_count / total_results if total_results > 0 else 0
        fp_avg_cost = sum(r.false_result_impact.additional_cost for r in false_positives) / fp_count if fp_count > 0 else 0
        fp_avg_anxiety = sum(r.false_result_impact.anxiety_duration_days for r in false_positives) / fp_count if fp_count > 0 else 0

        # 计算假阴性统计
        fn_count = len(false_negatives)
        fn_rate = fn_count / total_results if total_results > 0 else 0
        fn_avg_cost = sum(r.false_result_impact.additional_cost for r in false_negatives) / fn_count if fn_count > 0 else 0
        fn_avg_delay = sum(r.false_result_impact.delay_in_detection_days for r in false_negatives) / fn_count if fn_count > 0 else 0

        return {
            "total_results": total_results,
            "false_positive": {
                "count": fp_count,
                "rate": fp_rate,
                "average_additional_cost": fp_avg_cost,
                "average_anxiety_duration_days": fp_avg_anxiety,
                "total_unnecessary_procedures": sum(r.false_result_impact.unnecessary_procedures for r in false_positives)
            },
            "false_negative": {
                "count": fn_count,
                "rate": fn_rate,
                "average_additional_cost": fn_avg_cost,
                "average_detection_delay_days": fn_avg_delay,
                "total_detection_delay_days": sum(r.false_result_impact.delay_in_detection_days for r in false_negatives)
            },
            "overall_false_result_rate": (fp_count + fn_count) / total_results if total_results > 0 else 0
        }

    def _update_individual_screening_history(
        self,
        individual: Individual,
        result: ScreeningResult
    ) -> None:
        """
        更新个体筛查历史记录

        Args:
            individual: 个体
            result: 筛查结果
        """
        screening_record = {
            'test_date': result.test_date,
            'tool_type': result.tool_type.value,
            'result_type': result.result_type.value,
            'confidence_score': result.confidence_score,
            'quality_score': result.quality_score,
            'is_true_positive': result.is_true_positive,
            'is_true_negative': result.is_true_negative,
            'followup_action': result.followup_action.value,
            'processing_time': result.processing_time,
            'cost': result.cost
        }

        # 添加假结果信息
        if result.false_result_impact:
            screening_record.update({
                'false_result_type': result.false_result_impact.result_type.value,
                'psychological_impact_score': result.false_result_impact.psychological_impact_score,
                'additional_cost': result.false_result_impact.additional_cost,
                'delay_in_detection_days': result.false_result_impact.delay_in_detection_days,
                'unnecessary_procedures': result.false_result_impact.unnecessary_procedures,
                'anxiety_duration_days': result.false_result_impact.anxiety_duration_days
            })

        # 添加到个体筛查历史
        individual.add_screening_record(screening_record)

        # 如果发现病变并进行了摘除，更新个体状态
        if (result.result_type == ScreeningResultType.POSITIVE and
            result.is_true_positive and
            individual.current_disease_state != DiseaseState.NORMAL):

            # 模拟筛查发现病变后的摘除
            self._simulate_screening_detected_lesion_removal(individual, result)

    def _simulate_screening_detected_lesion_removal(
        self,
        individual: Individual,
        result: ScreeningResult
    ) -> None:
        """
        模拟筛查发现病变后的摘除

        根据故事要求：筛查并被确诊为病灶的，则默认此处病灶被手术摘除

        Args:
            individual: 个体
            result: 筛查结果
        """
        original_state = individual.current_disease_state

        # 将个体状态重置为正常（病变摘除）
        success = individual.transition_to_state(DiseaseState.NORMAL)

        if success:
            # 记录摘除事件
            removal_record = {
                'removal_date': result.test_date,
                'original_state': original_state.value,
                'detection_method': 'screening',
                'tool_type': result.tool_type.value,
                'removal_type': 'endoscopic_removal'
            }

            # 添加到筛查记录的额外数据中
            if 'lesion_removal' not in result.additional_data:
                result.additional_data['lesion_removal'] = []
            result.additional_data['lesion_removal'].append(removal_record)

            logger.info(f"模拟筛查发现病变摘除: {individual.individual_id}, "
                       f"原状态: {original_state.value}")

    def calculate_screening_impact_on_survival(
        self,
        individual: Individual,
        results: List[ScreeningResult]
    ) -> Dict[str, float]:
        """
        计算筛查对生存预后的影响

        Args:
            individual: 个体
            results: 筛查结果列表

        Returns:
            生存影响指标
        """
        impact = {
            'life_years_gained': 0.0,
            'quality_adjusted_life_years': 0.0,
            'early_detection_benefit': 0.0,
            'false_result_harm': 0.0
        }

        # 计算早期发现的生存获益
        early_detections = [
            r for r in results
            if r.is_true_positive and r.result_type == ScreeningResultType.POSITIVE
        ]

        for detection in early_detections:
            # 根据检测到的疾病状态估算生存获益
            true_state = DiseaseState(detection.additional_data.get('true_disease_state'))

            if true_state in [DiseaseState.LOW_RISK_ADENOMA, DiseaseState.HIGH_RISK_ADENOMA]:
                # 腺瘤早期发现和摘除的生存获益
                impact['life_years_gained'] += 0.5  # 平均0.5年生存获益
                impact['quality_adjusted_life_years'] += 0.4
            elif true_state in [DiseaseState.SMALL_SERRATED, DiseaseState.LARGE_SERRATED]:
                # 锯齿状腺瘤早期发现的生存获益
                impact['life_years_gained'] += 0.7
                impact['quality_adjusted_life_years'] += 0.6
            elif true_state == DiseaseState.PRECLINICAL_CANCER:
                # 临床前癌症早期发现的显著生存获益
                impact['life_years_gained'] += 3.0
                impact['quality_adjusted_life_years'] += 2.5
            elif true_state.value.startswith('clinical_cancer'):
                # 临床癌症早期发现的生存获益
                impact['life_years_gained'] += 1.5
                impact['quality_adjusted_life_years'] += 1.2

        # 计算假结果的负面影响
        false_results = [
            r for r in results
            if r.false_result_impact and
            r.false_result_impact.result_type != FalseResultType.TRUE_RESULT
        ]

        for false_result in false_results:
            # 假结果造成的生活质量损失
            psychological_impact = false_result.false_result_impact.psychological_impact_score
            impact['false_result_harm'] += psychological_impact * 0.01  # 转换为QALY损失

        # 计算净早期发现获益
        impact['early_detection_benefit'] = (
            impact['life_years_gained'] - impact['false_result_harm']
        )

        return impact
