"""
筛查资格检查器

实现筛查资格判断逻辑，包括：
1. 初筛资格检查（年龄、诊断性肠镜间隔、筛查间隔、依从性）
2. 诊断性肠镜筛查资格检查（初筛阳性、依从性）
"""

import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Dict, Any, List

from src.core.individual import Individual
from src.modules.screening.enums import ScreeningToolType
from src.modules.screening.strategy import ScreeningInterval
from src.modules.screening.compliance_model import ComplianceModel

logger = logging.getLogger(__name__)


class EligibilityReason(Enum):
    """资格判断原因"""
    ELIGIBLE = "eligible"
    AGE_TOO_YOUNG = "age_too_young"
    AGE_TOO_OLD = "age_too_old"
    RECENT_COLONOSCOPY = "recent_colonoscopy"
    RECENT_SCREENING = "recent_screening"
    NON_COMPLIANT = "non_compliant"
    NO_POSITIVE_SCREENING = "no_positive_screening"
    DIAGNOSTIC_NON_COMPLIANT = "diagnostic_non_compliant"


@dataclass
class EligibilityResult:
    """筛查资格判断结果"""
    is_eligible: bool
    reason: EligibilityReason
    details: Dict[str, Any]
    next_eligible_date: Optional[datetime] = None


class ScreeningEligibilityChecker:
    """筛查资格检查器"""
    
    def __init__(self, compliance_model: Optional[ComplianceModel] = None):
        """
        初始化筛查资格检查器

        Args:
            compliance_model: 依从性模型，如果为None则创建默认模型
        """
        if compliance_model is None:
            # 创建默认依从性配置
            default_config = {
                'base_rates': {
                    'fit_screening': 0.7,
                    'colonoscopy_screening': 0.8,
                    'sigmoidoscopy_screening': 0.6,
                    'ctcolonography_screening': 0.5
                },
                'temporal_patterns': {
                    'first_time_multiplier': 0.8,
                    'repeat_multiplier': 1.1,
                    'time_decay_rate': 0.05
                },
                'followup_compliance': {
                    'base_rate': 0.85,
                    'waiting_time_impact': -0.02,
                    'multiple_positive_bonus': 0.1
                },
                'diagnostic_compliance': {
                    'base_rate': 0.85,
                    'waiting_time_impact': -0.02,
                    'multiple_positive_bonus': 0.1
                }
            }
            self.compliance_model = ComplianceModel(default_config)
        else:
            self.compliance_model = compliance_model
        logger.info("初始化筛查资格检查器")
    
    def check_primary_screening_eligibility(
        self,
        individual: Individual,
        screening_interval: ScreeningInterval,
        current_time: datetime,
        year_after_colonoscopy: float = 10.0
    ) -> EligibilityResult:
        """
        检查初筛资格
        
        根据要求：
        1. 个体年龄要大于等于起始年龄、小于等于终止年龄
        2. 满足1，且距离上次诊断性肠镜检查的年数要大于等于year_after_colonoscopy
        3. 满足2的基础上，本次与上次的筛查周期要大于等于筛查间隔期，且依从性符合要求
        
        Args:
            individual: 个体对象
            screening_interval: 筛查间隔配置
            current_time: 当前时间
            year_after_colonoscopy: 诊断性肠镜后的间隔年数
            
        Returns:
            资格判断结果
        """
        details = {
            "current_age": int(individual.get_current_age()),
            "start_age": screening_interval.start_age,
            "end_age": screening_interval.end_age,
            "tool_type": screening_interval.tool_type.value,
            "screening_interval_years": screening_interval.frequency_years
        }
        
        # 1. 检查年龄范围
        current_age = int(individual.get_current_age())
        if current_age < screening_interval.start_age:
            return EligibilityResult(
                is_eligible=False,
                reason=EligibilityReason.AGE_TOO_YOUNG,
                details=details,
                next_eligible_date=self._calculate_age_eligible_date(
                    individual, screening_interval.start_age
                )
            )
        
        if current_age > screening_interval.end_age:
            return EligibilityResult(
                is_eligible=False,
                reason=EligibilityReason.AGE_TOO_OLD,
                details=details
            )
        
        # 2. 检查距离上次诊断性肠镜的间隔
        last_colonoscopy = self._get_last_colonoscopy_date(individual)
        if last_colonoscopy:
            years_since_colonoscopy = (current_time - last_colonoscopy).days / 365.25
            details["last_colonoscopy_date"] = last_colonoscopy
            details["years_since_colonoscopy"] = years_since_colonoscopy
            
            if years_since_colonoscopy < year_after_colonoscopy:
                next_eligible_date = last_colonoscopy + timedelta(days=int(year_after_colonoscopy * 365.25))
                return EligibilityResult(
                    is_eligible=False,
                    reason=EligibilityReason.RECENT_COLONOSCOPY,
                    details=details,
                    next_eligible_date=next_eligible_date
                )
        
        # 3. 检查筛查间隔
        last_screening = self._get_last_screening_date(individual, screening_interval.tool_type)
        if last_screening:
            years_since_screening = (current_time - last_screening).days / 365.25
            details["last_screening_date"] = last_screening
            details["years_since_screening"] = years_since_screening
            
            if years_since_screening < screening_interval.frequency_years:
                next_eligible_date = last_screening + timedelta(
                    days=int(screening_interval.frequency_years * 365.25)
                )
                return EligibilityResult(
                    is_eligible=False,
                    reason=EligibilityReason.RECENT_SCREENING,
                    details=details,
                    next_eligible_date=next_eligible_date
                )
        
        # 4. 检查依从性
        screening_history = self._get_screening_history_events(individual)
        compliance_prob = self.compliance_model.calculate_compliance_probability(
            individual, screening_interval.tool_type, screening_history
        )
        
        details["compliance_probability"] = compliance_prob
        
        # 模拟依从性决策
        is_compliant = self.compliance_model.simulate_compliance_decision(
            individual, screening_interval.tool_type, screening_history
        )
        
        if not is_compliant:
            return EligibilityResult(
                is_eligible=False,
                reason=EligibilityReason.NON_COMPLIANT,
                details=details
            )
        
        # 所有条件都满足
        return EligibilityResult(
            is_eligible=True,
            reason=EligibilityReason.ELIGIBLE,
            details=details
        )
    
    def check_diagnostic_colonoscopy_eligibility(
        self,
        individual: Individual,
        current_time: datetime
    ) -> EligibilityResult:
        """
        检查诊断性肠镜筛查资格
        
        根据要求：
        1. 初筛阳性
        2. 接受诊断性肠镜筛查的率且符合诊断性肠镜依从性
        
        Args:
            individual: 个体对象
            current_time: 当前时间
            
        Returns:
            资格判断结果
        """
        details = {
            "current_age": int(individual.get_current_age()),
            "tool_type": "diagnostic_colonoscopy"
        }
        
        # 1. 检查是否有初筛阳性结果
        has_positive_screening = self._has_recent_positive_screening(individual, current_time)
        details["has_positive_screening"] = has_positive_screening

        if not has_positive_screening:
            return EligibilityResult(
                is_eligible=False,
                reason=EligibilityReason.NO_POSITIVE_SCREENING,
                details=details
            )

        # 1.5. 检查阳性筛查是否为肠镜筛查
        # 如果初筛工具已经是肠镜，则不需要诊断性肠镜
        has_positive_colonoscopy = self._has_recent_positive_colonoscopy_screening(individual, current_time)
        if has_positive_colonoscopy:
            return EligibilityResult(
                is_eligible=False,
                reason=EligibilityReason.NO_POSITIVE_SCREENING,  # 使用这个原因表示不需要
                details={**details, "note": "肠镜筛查本身就是诊断性的，无需额外诊断性肠镜"}
            )
        
        # 2. 检查诊断性肠镜依从性
        screening_history = self._get_screening_history_events(individual)

        # 创建一个模拟的阳性筛查事件
        positive_event = None
        if screening_history:
            for event in screening_history:
                # 检查事件是否有result属性（ScreeningEvent对象）或result_type键（字典）
                if hasattr(event, 'result'):
                    if event.result == 'positive':
                        positive_event = event
                        break
                elif hasattr(event, 'get') and event.get('result_type') == 'positive':
                    positive_event = event
                    break

        if positive_event:
            # 转换为ScreeningEvent格式
            from src.modules.screening.compliance_model import ScreeningEvent

            # 检查positive_event是否已经是ScreeningEvent对象
            if hasattr(positive_event, 'date'):
                screening_event = positive_event
            else:
                screening_event = ScreeningEvent(
                    date=positive_event.get('date', current_time),
                    tool_type=positive_event.get('tool_type', ScreeningToolType.FIT),
                    result=positive_event.get('result_type', 'positive'),
                    individual_id=individual.individual_id
                )

            diagnostic_compliance_prob = self.compliance_model.calculate_diagnostic_compliance(
                individual, screening_event, screening_history=screening_history
            )

            details["diagnostic_compliance_probability"] = diagnostic_compliance_prob

            # 模拟诊断依从性决策
            is_diagnostic_compliant = self.compliance_model.simulate_diagnostic_compliance_decision(
                individual, screening_event, screening_history=screening_history
            )
        else:
            # 没有阳性事件，使用默认值
            is_diagnostic_compliant = False
        
        if not is_diagnostic_compliant:
            return EligibilityResult(
                is_eligible=False,
                reason=EligibilityReason.DIAGNOSTIC_NON_COMPLIANT,
                details=details
            )
        
        # 满足诊断性肠镜条件
        return EligibilityResult(
            is_eligible=True,
            reason=EligibilityReason.ELIGIBLE,
            details=details
        )
    
    def _get_last_colonoscopy_date(self, individual: Individual) -> Optional[datetime]:
        """获取上次诊断性肠镜检查日期"""
        # 查找诊断历史中的肠镜检查
        if hasattr(individual, 'diagnostic_history') and individual.diagnostic_history:
            colonoscopy_records = [
                record for record in individual.diagnostic_history
                if record.get('procedure_type') == 'colonoscopy' or 
                   record.get('procedure_type') == 'diagnostic_colonoscopy'
            ]
            if colonoscopy_records:
                latest_record = max(colonoscopy_records, 
                                  key=lambda x: x.get('date', datetime.min))
                return latest_record.get('date')
        
        # 也检查筛查历史中的肠镜筛查
        if hasattr(individual, 'screening_history') and individual.screening_history:
            colonoscopy_screenings = [
                record for record in individual.screening_history
                if record.get('tool_type') == ScreeningToolType.COLONOSCOPY or
                   record.get('tool_type') == 'colonoscopy'
            ]
            if colonoscopy_screenings:
                latest_screening = max(colonoscopy_screenings,
                                     key=lambda x: x.get('test_date', datetime.min))
                return latest_screening.get('test_date')
        
        return None
    
    def _get_last_screening_date(
        self, 
        individual: Individual, 
        tool_type: ScreeningToolType
    ) -> Optional[datetime]:
        """获取上次指定工具类型的筛查日期"""
        if not hasattr(individual, 'screening_history') or not individual.screening_history:
            return None
        
        tool_screenings = [
            record for record in individual.screening_history
            if record.get('tool_type') == tool_type or
               record.get('tool_type') == tool_type.value
        ]
        
        if tool_screenings:
            latest_screening = max(tool_screenings,
                                 key=lambda x: x.get('test_date', datetime.min))
            return latest_screening.get('test_date')
        
        return None
    
    def _has_recent_positive_screening(
        self, 
        individual: Individual, 
        current_time: datetime,
        lookback_days: int = 365
    ) -> bool:
        """检查是否有近期的阳性筛查结果"""
        if not hasattr(individual, 'screening_history') or not individual.screening_history:
            return False
        
        cutoff_date = current_time - timedelta(days=lookback_days)
        
        recent_positive_screenings = [
            record for record in individual.screening_history
            if (record.get('result_type') == 'positive' and
                record.get('test_date', datetime.min) >= cutoff_date)
        ]
        
        return len(recent_positive_screenings) > 0

    def _has_recent_positive_colonoscopy_screening(
        self,
        individual: Individual,
        current_time: datetime,
        lookback_days: int = 365
    ) -> bool:
        """检查是否有近期的阳性肠镜筛查结果"""
        if not hasattr(individual, 'screening_history') or not individual.screening_history:
            return False

        cutoff_date = current_time - timedelta(days=lookback_days)

        recent_positive_colonoscopy = [
            record for record in individual.screening_history
            if (record.get('result_type') == 'positive' and
                record.get('test_date', datetime.min) >= cutoff_date and
                (record.get('tool_type') == ScreeningToolType.COLONOSCOPY or
                 record.get('tool_type') == 'colonoscopy'))
        ]

        return len(recent_positive_colonoscopy) > 0
    
    def _get_screening_history_events(self, individual: Individual) -> List[Any]:
        """获取筛查历史事件列表（转换为依从性模型需要的格式）"""
        if not hasattr(individual, 'screening_history') or not individual.screening_history:
            return []

        # 导入ScreeningEvent类
        from src.modules.screening.compliance_model import ScreeningEvent

        # 转换为依从性模型期望的格式
        events = []
        for record in individual.screening_history:
            event = ScreeningEvent(
                date=record.get('test_date', datetime.now()),
                tool_type=record.get('tool_type', ScreeningToolType.FIT),
                result=record.get('result_type', 'negative'),
                individual_id=individual.individual_id
            )
            events.append(event)

        return events
    
    def _calculate_age_eligible_date(
        self,
        individual: Individual,
        target_age: int
    ) -> datetime:
        """计算个体达到目标年龄的日期"""
        current_age = int(individual.get_current_age())
        years_to_wait = target_age - current_age

        # 简化计算：假设生日是年初
        birth_year = datetime.now().year - current_age
        target_year = int(birth_year + target_age)

        return datetime(target_year, 1, 1)
