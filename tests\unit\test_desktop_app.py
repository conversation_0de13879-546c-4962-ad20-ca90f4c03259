"""
桌面应用程序单元测试

测试PyQt6桌面应用程序的基本功能。
"""

import pytest
import sys
from unittest.mock import patch, MagicMock

# 确保PyQt6可用
pytest.importorskip("PyQt6")

from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt

from src.interfaces.desktop.main import Application, MainWindow


@pytest.fixture
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication(sys.argv)
    else:
        app = QApplication.instance()
    yield app
    # 清理不需要，因为QApplication是单例


class TestApplication:
    """测试Application类"""
    
    def test_application_creation(self, qapp):
        """测试应用程序创建"""
        app = Application(sys.argv)

        assert app.applicationName() == "结直肠癌筛查模拟器"
        assert app.applicationVersion() == "1.0.0"
        assert app.organizationName() == "深圳市南山区慢性病防治院"
        assert app.organizationDomain() == "www.sznsmby.cn"
    
    def test_create_main_window(self, qapp):
        """测试主窗口创建"""
        app = Application(sys.argv)
        
        # 第一次创建
        window1 = app.create_main_window()
        assert isinstance(window1, MainWindow)
        assert app.main_window is window1
        
        # 第二次调用应该返回同一个窗口
        window2 = app.create_main_window()
        assert window1 is window2
    
    def test_message_dialogs(self, qapp):
        """测试消息对话框方法"""
        app = Application(sys.argv)
        
        # 这些方法不会实际显示对话框，只是确保不会抛出异常
        with patch('PyQt6.QtWidgets.QMessageBox.critical'):
            app.show_error_message("测试错误", "这是一个测试错误消息")
        
        with patch('PyQt6.QtWidgets.QMessageBox.information'):
            app.show_info_message("测试信息", "这是一个测试信息消息")


class TestMainWindow:
    """测试MainWindow类"""
    
    def test_main_window_creation(self, qapp):
        """测试主窗口创建"""
        window = MainWindow()
        
        assert window.windowTitle() == "结直肠癌筛查模拟器"
        assert window.minimumSize().width() == 1200
        assert window.minimumSize().height() == 800
        
        # 检查初始数据
        assert window.population is None
        assert window.simulation_state is None
    
    def test_menu_bar_setup(self, qapp):
        """测试菜单栏设置"""
        window = MainWindow()
        
        menubar = window.menuBar()
        assert menubar is not None
        
        # 检查菜单项
        menus = [action.text() for action in menubar.actions()]
        expected_menus = ["文件(&F)", "编辑(&E)", "视图(&V)", "帮助(&H)"]
        
        for expected_menu in expected_menus:
            assert expected_menu in menus
    
    def test_tool_bar_setup(self, qapp):
        """测试工具栏设置"""
        window = MainWindow()
        
        toolbars = window.findChildren(QApplication.instance().toolBarClass())
        assert len(toolbars) > 0
        
        toolbar = toolbars[0]
        assert toolbar.windowTitle() == "主工具栏"
    
    def test_status_bar_setup(self, qapp):
        """测试状态栏设置"""
        window = MainWindow()
        
        status_bar = window.statusBar()
        assert status_bar is not None
        
        # 检查状态标签
        assert hasattr(window, 'status_label')
        assert window.status_label.text() == "就绪"
        
        # 检查进度条
        assert hasattr(window, 'progress_bar')
        assert not window.progress_bar.isVisible()
    
    def test_central_widget_setup(self, qapp):
        """测试中央区域设置"""
        window = MainWindow()
        
        central_widget = window.centralWidget()
        assert central_widget is not None
        
        # 检查标签页控件
        assert hasattr(window, 'tab_widget')
        assert window.tab_widget.count() == 3
        
        # 检查标签页标题
        tab_titles = [window.tab_widget.tabText(i) for i in range(window.tab_widget.count())]
        expected_titles = ["配置", "模拟", "结果"]
        assert tab_titles == expected_titles
    
    def test_progress_update(self, qapp):
        """测试进度更新"""
        window = MainWindow()
        
        # 测试进度更新
        window.update_progress(50)
        assert window.progress_bar.value() == 50
        
        # 测试完成时的行为
        window.update_progress(100)
        assert not window.progress_bar.isVisible()
        assert window.status_label.text() == "模拟完成"
    
    def test_simulation_signals(self, qapp):
        """测试模拟信号"""
        window = MainWindow()
        
        # 创建信号接收器
        started_received = []
        paused_received = []
        stopped_received = []
        
        window.simulation_started.connect(lambda: started_received.append(True))
        window.simulation_paused.connect(lambda: paused_received.append(True))
        window.simulation_stopped.connect(lambda: stopped_received.append(True))
        
        # 触发信号
        window._start_simulation()
        assert len(started_received) == 1
        
        window._pause_simulation()
        assert len(paused_received) == 1
        
        window._stop_simulation()
        assert len(stopped_received) == 1
    
    @patch('PyQt6.QtWidgets.QMessageBox.question')
    def test_close_event_accept(self, mock_question, qapp):
        """测试窗口关闭事件 - 接受"""
        from PyQt6.QtWidgets import QMessageBox
        from PyQt6.QtGui import QCloseEvent
        
        mock_question.return_value = QMessageBox.StandardButton.Yes
        
        window = MainWindow()
        event = QCloseEvent()
        
        window.closeEvent(event)
        
        assert event.isAccepted()
        mock_question.assert_called_once()
    
    @patch('PyQt6.QtWidgets.QMessageBox.question')
    def test_close_event_ignore(self, mock_question, qapp):
        """测试窗口关闭事件 - 忽略"""
        from PyQt6.QtWidgets import QMessageBox
        from PyQt6.QtGui import QCloseEvent
        
        mock_question.return_value = QMessageBox.StandardButton.No
        
        window = MainWindow()
        event = QCloseEvent()
        
        window.closeEvent(event)
        
        assert not event.isAccepted()
        mock_question.assert_called_once()


class TestMainFunction:
    """测试main函数"""
    
    @patch('src.interfaces.desktop.main.Application')
    def test_main_function_success(self, mock_app_class):
        """测试main函数成功执行"""
        from src.interfaces.desktop.main import main
        
        # 设置mock
        mock_app = MagicMock()
        mock_app.exec.return_value = 0
        mock_app_class.return_value = mock_app
        
        # 调用main函数
        result = main()
        
        assert result == 0
        mock_app_class.assert_called_once_with(sys.argv)
        mock_app.create_main_window.assert_called_once()
        mock_app.exec.assert_called_once()
    
    @patch('src.interfaces.desktop.main.Application')
    def test_main_function_exception(self, mock_app_class):
        """测试main函数异常处理"""
        from src.interfaces.desktop.main import main
        
        # 设置mock抛出异常
        mock_app = MagicMock()
        mock_app.create_main_window.side_effect = Exception("测试异常")
        mock_app_class.return_value = mock_app
        
        # 调用main函数
        result = main()
        
        assert result == 1
        mock_app.show_error_message.assert_called_once()
