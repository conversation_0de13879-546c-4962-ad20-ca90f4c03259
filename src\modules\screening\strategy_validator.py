"""
筛查策略配置验证系统

提供全面的策略配置验证功能，包括逻辑一致性检查、
成本效益分析和临床合理性验证。
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .strategy import ScreeningStrategy, ScreeningInterval, TargetPopulation
from .enums import ScreeningToolType
from src.utils.validators import (
    StrategyValidationError,
    validate_screening_strategy_name,
    validate_screening_interval_ages,
    validate_screening_frequency
)

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """验证严重程度"""
    ERROR = "error"         # 错误：必须修复
    WARNING = "warning"     # 警告：建议修复
    INFO = "info"          # 信息：可选优化


@dataclass
class ValidationResult:
    """验证结果"""
    severity: ValidationSeverity
    message: str
    field_name: str
    suggested_fix: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class StrategyValidator:
    """
    筛查策略验证器
    
    提供全面的策略配置验证功能。
    """
    
    def __init__(self):
        """初始化验证器"""
        self.validation_rules = {
            'basic_info': self._validate_basic_info,
            'intervals': self._validate_intervals,
            'target_population': self._validate_target_population,
            'cost_effectiveness': self._validate_cost_effectiveness,
            'clinical_consistency': self._validate_clinical_consistency,
            'implementation_feasibility': self._validate_implementation_feasibility
        }
        
        logger.info("策略验证器初始化完成")
    
    def validate_strategy(
        self, 
        strategy: ScreeningStrategy,
        validation_level: str = "comprehensive"
    ) -> List[ValidationResult]:
        """
        验证筛查策略
        
        Args:
            strategy: 筛查策略对象
            validation_level: 验证级别 ('basic', 'standard', 'comprehensive')
            
        Returns:
            验证结果列表
        """
        results = []
        
        # 根据验证级别选择验证规则
        if validation_level == "basic":
            rules_to_run = ['basic_info', 'intervals']
        elif validation_level == "standard":
            rules_to_run = ['basic_info', 'intervals', 'target_population', 'cost_effectiveness']
        else:  # comprehensive
            rules_to_run = list(self.validation_rules.keys())
        
        # 执行验证规则
        for rule_name in rules_to_run:
            try:
                rule_results = self.validation_rules[rule_name](strategy)
                results.extend(rule_results)
            except Exception as e:
                logger.error(f"验证规则 {rule_name} 执行失败: {e}")
                results.append(ValidationResult(
                    severity=ValidationSeverity.ERROR,
                    message=f"验证规则执行失败: {str(e)}",
                    field_name=rule_name
                ))
        
        # 按严重程度排序
        results.sort(key=lambda x: (x.severity.value, x.field_name))
        
        logger.info(f"策略验证完成，发现 {len(results)} 个问题")
        return results
    
    def _validate_basic_info(self, strategy: ScreeningStrategy) -> List[ValidationResult]:
        """验证基本信息"""
        results = []
        
        # 验证策略名称
        try:
            validate_screening_strategy_name(strategy.name)
        except StrategyValidationError as e:
            results.append(ValidationResult(
                severity=ValidationSeverity.ERROR,
                message=e.message,
                field_name="name",
                suggested_fix="请提供有效的策略名称（2-100个字符，不含特殊字符）"
            ))
        
        # 验证描述
        if not strategy.description or len(strategy.description.strip()) < 10:
            results.append(ValidationResult(
                severity=ValidationSeverity.WARNING,
                message="策略描述过短或为空",
                field_name="description",
                suggested_fix="建议提供详细的策略描述（至少10个字符）"
            ))
        
        # 验证版本号
        if not strategy.version or not strategy.version.strip():
            results.append(ValidationResult(
                severity=ValidationSeverity.WARNING,
                message="缺少版本号",
                field_name="version",
                suggested_fix="建议设置版本号（如：1.0）"
            ))
        
        # 验证作者
        if not strategy.author or not strategy.author.strip():
            results.append(ValidationResult(
                severity=ValidationSeverity.INFO,
                message="缺少作者信息",
                field_name="author",
                suggested_fix="建议填写作者信息"
            ))
        
        return results
    
    def _validate_intervals(self, strategy: ScreeningStrategy) -> List[ValidationResult]:
        """验证筛查间隔"""
        results = []
        
        # 检查是否有间隔
        if not strategy.intervals:
            results.append(ValidationResult(
                severity=ValidationSeverity.ERROR,
                message="策略必须包含至少一个筛查间隔",
                field_name="intervals",
                suggested_fix="请添加至少一个筛查间隔配置"
            ))
            return results
        
        # 验证每个间隔
        for i, interval in enumerate(strategy.intervals):
            interval_results = self._validate_single_interval(interval, f"intervals[{i}]")
            results.extend(interval_results)
        
        # 检查间隔重叠
        overlap_results = self._check_interval_overlaps(strategy.intervals)
        results.extend(overlap_results)
        
        # 检查工具组合合理性
        combination_results = self._check_tool_combinations(strategy.intervals)
        results.extend(combination_results)
        
        return results
    
    def _validate_single_interval(self, interval: ScreeningInterval, field_prefix: str) -> List[ValidationResult]:
        """验证单个筛查间隔"""
        results = []
        
        # 验证年龄范围
        try:
            validate_screening_interval_ages(interval.start_age, interval.end_age)
        except StrategyValidationError as e:
            results.append(ValidationResult(
                severity=ValidationSeverity.ERROR,
                message=e.message,
                field_name=f"{field_prefix}.age_range",
                suggested_fix="请设置有效的年龄范围（18-100岁，开始年龄小于结束年龄）"
            ))
        
        # 验证筛查频率
        try:
            validate_screening_frequency(interval.frequency_years)
        except StrategyValidationError as e:
            results.append(ValidationResult(
                severity=ValidationSeverity.ERROR,
                message=e.message,
                field_name=f"{field_prefix}.frequency",
                suggested_fix="请设置有效的筛查频率（0.5-20年）"
            ))
        
        # 检查工具特定的配置
        tool_results = self._validate_tool_specific_config(interval, field_prefix)
        results.extend(tool_results)
        
        return results
    
    def _validate_tool_specific_config(self, interval: ScreeningInterval, field_prefix: str) -> List[ValidationResult]:
        """验证工具特定配置"""
        results = []
        
        tool_type = interval.tool_type
        
        # FIT特定验证
        if tool_type == ScreeningToolType.FIT:
            if interval.frequency_years > 2:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message=f"FIT筛查频率({interval.frequency_years}年)过长，建议年度筛查",
                    field_name=f"{field_prefix}.frequency",
                    suggested_fix="建议将FIT筛查频率设置为1年"
                ))
        
        # 结肠镜特定验证
        elif tool_type == ScreeningToolType.COLONOSCOPY:
            if interval.frequency_years < 5:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message=f"结肠镜筛查频率({interval.frequency_years}年)过短，建议5-10年",
                    field_name=f"{field_prefix}.frequency",
                    suggested_fix="建议将结肠镜筛查频率设置为5-10年"
                ))
            
            if interval.start_age < 45:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message=f"结肠镜开始年龄({interval.start_age}岁)过早，建议45岁以上",
                    field_name=f"{field_prefix}.start_age",
                    suggested_fix="建议将结肠镜开始年龄设置为45岁以上"
                ))
        
        # 乙状结肠镜特定验证
        elif tool_type == ScreeningToolType.SIGMOIDOSCOPY:
            if interval.frequency_years > 10:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message=f"乙状结肠镜筛查频率({interval.frequency_years}年)过长，建议5年",
                    field_name=f"{field_prefix}.frequency",
                    suggested_fix="建议将乙状结肠镜筛查频率设置为5年"
                ))
        
        return results
    
    def _check_interval_overlaps(self, intervals: List[ScreeningInterval]) -> List[ValidationResult]:
        """检查间隔重叠"""
        results = []
        
        for i, interval1 in enumerate(intervals):
            for j, interval2 in enumerate(intervals[i+1:], i+1):
                if interval1.overlaps_with(interval2):
                    results.append(ValidationResult(
                        severity=ValidationSeverity.ERROR,
                        message=f"间隔{i+1}与间隔{j+1}存在重叠: {interval1.tool_type.display_name}",
                        field_name=f"intervals[{i}]",
                        suggested_fix="请调整年龄范围以避免重叠，或使用不同的筛查工具"
                    ))
        
        return results
    
    def _check_tool_combinations(self, intervals: List[ScreeningInterval]) -> List[ValidationResult]:
        """检查工具组合合理性"""
        results = []
        
        tool_types = [interval.tool_type for interval in intervals]
        
        # 检查是否有初筛工具
        primary_tools = [ScreeningToolType.FIT, ScreeningToolType.STOOL_DNA]
        has_primary = any(tool in tool_types for tool in primary_tools)
        
        # 检查是否有诊断工具
        diagnostic_tools = [ScreeningToolType.COLONOSCOPY]
        has_diagnostic = any(tool in tool_types for tool in diagnostic_tools)
        
        if has_diagnostic and not has_primary:
            results.append(ValidationResult(
                severity=ValidationSeverity.WARNING,
                message="建议在诊断性工具前添加初筛工具",
                field_name="intervals",
                suggested_fix="考虑添加FIT或粪便DNA检测作为初筛工具"
            ))
        
        # 检查高成本工具的使用
        high_cost_tools = [ScreeningToolType.CTCOLONOGRAPHY, ScreeningToolType.CAPSULE_ENDOSCOPY]
        has_high_cost = any(tool in tool_types for tool in high_cost_tools)
        
        if has_high_cost and len(intervals) == 1:
            results.append(ValidationResult(
                severity=ValidationSeverity.INFO,
                message="高成本筛查工具建议与其他工具组合使用",
                field_name="intervals",
                suggested_fix="考虑添加成本较低的初筛工具"
            ))
        
        return results
    
    def _validate_target_population(self, strategy: ScreeningStrategy) -> List[ValidationResult]:
        """验证目标人群"""
        results = []
        
        population = strategy.target_population
        
        # 验证年龄范围
        try:
            validate_screening_interval_ages(
                population.age_range[0], 
                population.age_range[1], 
                "target_population.age_range"
            )
        except StrategyValidationError as e:
            results.append(ValidationResult(
                severity=ValidationSeverity.ERROR,
                message=e.message,
                field_name="target_population.age_range",
                suggested_fix="请设置有效的目标人群年龄范围"
            ))
        
        # 检查年龄范围与间隔的一致性
        if strategy.intervals:
            pop_start, pop_end = population.age_range
            for i, interval in enumerate(strategy.intervals):
                if interval.start_age < pop_start or interval.end_age > pop_end:
                    results.append(ValidationResult(
                        severity=ValidationSeverity.WARNING,
                        message=f"间隔{i+1}的年龄范围超出目标人群范围",
                        field_name=f"intervals[{i}].age_range",
                        suggested_fix="建议调整间隔年龄范围与目标人群保持一致"
                    ))
        
        # 验证风险水平
        valid_risk_levels = ['low', 'average', 'high', 'very_high']
        if population.risk_level not in valid_risk_levels:
            results.append(ValidationResult(
                severity=ValidationSeverity.ERROR,
                message=f"无效的风险水平: {population.risk_level}",
                field_name="target_population.risk_level",
                suggested_fix=f"请选择有效的风险水平: {', '.join(valid_risk_levels)}"
            ))
        
        # 检查人群规模
        if population.population_size:
            if population.population_size < 100:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message="目标人群规模过小，可能影响统计效力",
                    field_name="target_population.population_size",
                    suggested_fix="建议目标人群规模至少100人"
                ))
            elif population.population_size > 10000000:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message="目标人群规模过大，可能影响实施可行性",
                    field_name="target_population.population_size",
                    suggested_fix="建议重新评估目标人群规模的合理性"
                ))
        
        return results
    
    def _validate_cost_effectiveness(self, strategy: ScreeningStrategy) -> List[ValidationResult]:
        """验证成本效益"""
        results = []
        
        # 检查预算约束
        if strategy.budget_constraint:
            if strategy.budget_constraint < 1000:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message="预算约束过低，可能无法实施有效筛查",
                    field_name="budget_constraint",
                    suggested_fix="建议增加预算约束或重新评估筛查方案"
                ))
        
        # 检查成本效益阈值
        if strategy.cost_effectiveness_threshold:
            if strategy.cost_effectiveness_threshold < 10000:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message="成本效益阈值过低，可能不符合国际标准",
                    field_name="cost_effectiveness_threshold",
                    suggested_fix="建议参考WHO推荐的成本效益阈值（通常为人均GDP的1-3倍）"
                ))
            elif strategy.cost_effectiveness_threshold > 500000:
                results.append(ValidationResult(
                    severity=ValidationSeverity.INFO,
                    message="成本效益阈值较高，请确认是否符合当地标准",
                    field_name="cost_effectiveness_threshold",
                    suggested_fix="建议参考当地卫生经济学评价标准"
                ))
        
        # 检查预算与阈值的一致性
        if (strategy.budget_constraint and strategy.cost_effectiveness_threshold and
            strategy.target_population.population_size):
            
            estimated_cost_per_person = strategy.budget_constraint / strategy.target_population.population_size
            if estimated_cost_per_person > strategy.cost_effectiveness_threshold:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message="人均预算可能超过成本效益阈值",
                    field_name="budget_constraint",
                    suggested_fix="建议调整预算约束或重新评估筛查方案的成本效益"
                ))
        
        return results
    
    def _validate_clinical_consistency(self, strategy: ScreeningStrategy) -> List[ValidationResult]:
        """验证临床一致性"""
        results = []
        
        # 检查筛查间隔的临床合理性
        for i, interval in enumerate(strategy.intervals):
            # 检查年龄范围的临床合理性
            if interval.tool_type == ScreeningToolType.COLONOSCOPY:
                if interval.start_age < 40:
                    results.append(ValidationResult(
                        severity=ValidationSeverity.WARNING,
                        message=f"结肠镜筛查开始年龄({interval.start_age}岁)过早",
                        field_name=f"intervals[{i}].start_age",
                        suggested_fix="除非有特殊适应症，建议结肠镜筛查从40-50岁开始"
                    ))
                
                if interval.end_age > 85:
                    results.append(ValidationResult(
                        severity=ValidationSeverity.INFO,
                        message=f"结肠镜筛查结束年龄({interval.end_age}岁)较晚",
                        field_name=f"intervals[{i}].end_age",
                        suggested_fix="建议评估高龄人群筛查的获益风险比"
                    ))
        
        # 检查风险水平与筛查强度的匹配
        risk_level = strategy.target_population.risk_level
        if risk_level == "high" or risk_level == "very_high":
            # 高风险人群应该有更频繁的筛查
            min_frequency = min(interval.frequency_years for interval in strategy.intervals)
            if min_frequency > 2:
                results.append(ValidationResult(
                    severity=ValidationSeverity.WARNING,
                    message="高风险人群的筛查频率可能过低",
                    field_name="intervals",
                    suggested_fix="建议为高风险人群设置更频繁的筛查间隔"
                ))
        
        return results
    
    def _validate_implementation_feasibility(self, strategy: ScreeningStrategy) -> List[ValidationResult]:
        """验证实施可行性"""
        results = []
        
        # 检查筛查工具的可及性
        complex_tools = [
            ScreeningToolType.CTCOLONOGRAPHY,
            ScreeningToolType.CAPSULE_ENDOSCOPY,
            ScreeningToolType.COLONOSCOPY
        ]
        
        complex_tool_count = sum(1 for interval in strategy.intervals 
                               if interval.tool_type in complex_tools)
        
        if complex_tool_count > 2:
            results.append(ValidationResult(
                severity=ValidationSeverity.INFO,
                message="策略包含多种复杂筛查工具，可能影响实施可行性",
                field_name="intervals",
                suggested_fix="建议评估当地医疗资源是否能支持多种复杂筛查工具"
            ))
        
        # 检查筛查频率的可行性
        high_frequency_count = sum(1 for interval in strategy.intervals 
                                 if interval.frequency_years < 1)
        
        if high_frequency_count > 1:
            results.append(ValidationResult(
                severity=ValidationSeverity.WARNING,
                message="多个高频率筛查间隔可能影响患者依从性",
                field_name="intervals",
                suggested_fix="建议平衡筛查效果与患者依从性"
            ))
        
        return results
    
    def get_validation_summary(self, results: List[ValidationResult]) -> Dict[str, Any]:
        """获取验证结果摘要"""
        summary = {
            "total_issues": len(results),
            "errors": len([r for r in results if r.severity == ValidationSeverity.ERROR]),
            "warnings": len([r for r in results if r.severity == ValidationSeverity.WARNING]),
            "info": len([r for r in results if r.severity == ValidationSeverity.INFO]),
            "is_valid": len([r for r in results if r.severity == ValidationSeverity.ERROR]) == 0,
            "issues_by_category": {}
        }
        
        # 按类别统计问题
        for result in results:
            category = result.field_name.split('.')[0] if '.' in result.field_name else result.field_name
            if category not in summary["issues_by_category"]:
                summary["issues_by_category"][category] = {"errors": 0, "warnings": 0, "info": 0}
            
            severity_key = result.severity.value
            if severity_key == "info":
                severity_key = "info"
            elif severity_key == "warning":
                severity_key = "warnings"
            elif severity_key == "error":
                severity_key = "errors"

            summary["issues_by_category"][category][severity_key] += 1
        
        return summary
