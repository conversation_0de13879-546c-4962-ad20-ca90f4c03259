"""
筛查工具系统集成测试

测试筛查工具系统各组件之间的集成和端到端功能。
"""

import pytest
import tempfile
from pathlib import Path

from src.core.enums import DiseaseState, AnatomicalLocation, Gender
from src.core.individual import Individual
from src.modules.screening import (
    ScreeningToolType, ScreeningResult, ScreeningToolFactory,
    ScreeningToolConfigManager, ScreeningToolConfigTemplates,
    ScreeningCostModel, CostItem, CostComponent, CostCategory
)


class TestScreeningSystemIntegration:
    """测试筛查系统集成"""
    
    @pytest.fixture
    def temp_config_dir(self):
        """创建临时配置目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def config_manager(self, temp_config_dir):
        """创建配置管理器"""
        return ScreeningToolConfigManager(temp_config_dir)
    
    @pytest.fixture
    def sample_individual(self):
        """创建样本个体"""
        individual = Individual(
            birth_year=1970,
            gender=Gender.MALE
        )
        # 设置疾病状态
        individual.current_disease_state = DiseaseState.HIGH_RISK_ADENOMA
        return individual
    
    def test_end_to_end_fit_workflow(self, config_manager, sample_individual):
        """测试FIT工具端到端工作流"""
        # 1. 创建配置模板
        fit_template = ScreeningToolConfigTemplates.create_fit_template()
        
        # 2. 保存配置
        config_path = config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            fit_template,
            "integration_fit_config.yaml"
        )
        assert Path(config_path).exists()
        
        # 3. 从配置创建工具
        fit_tool = config_manager.create_tool_from_config(
            ScreeningToolType.FIT,
            "integration_fit_config.yaml"
        )
        
        # 4. 验证工具属性
        assert fit_tool.tool_type == ScreeningToolType.FIT
        assert fit_tool.performance.specificity == 0.95
        assert fit_tool.characteristics.invasiveness.value == "non_invasive"
        
        # 5. 执行筛查
        result = fit_tool.perform_screening(sample_individual)
        assert result in [ScreeningResult.POSITIVE, ScreeningResult.NEGATIVE, ScreeningResult.INADEQUATE]
        
        # 6. 计算检测概率
        detection_prob = fit_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA,
            AnatomicalLocation.DISTAL_COLON
        )
        assert 0.0 <= detection_prob <= 1.0
        
        # 7. 获取性能指标
        metrics = fit_tool.get_performance_metrics()
        assert "tool_type" in metrics
        assert metrics["tool_type"] == "FIT"
    
    def test_end_to_end_colonoscopy_workflow(self, config_manager, sample_individual):
        """测试结肠镜工具端到端工作流"""
        # 1. 创建配置模板
        colonoscopy_template = ScreeningToolConfigTemplates.create_colonoscopy_template()
        
        # 2. 自定义配置
        custom_params = {
            "screening_tool": {
                "performance": {
                    "specificity": 0.99  # 提高特异性
                },
                "costs": {
                    "direct_cost": 1000.0  # 调整成本
                }
            }
        }
        
        custom_template = ScreeningToolConfigTemplates.create_custom_template(
            ScreeningToolType.COLONOSCOPY,
            custom_params
        )
        
        # 3. 保存自定义配置
        config_manager.save_tool_configuration(
            ScreeningToolType.COLONOSCOPY,
            custom_template,
            "custom_colonoscopy_config.yaml"
        )
        
        # 4. 从配置创建工具
        colonoscopy_tool = config_manager.create_tool_from_config(
            ScreeningToolType.COLONOSCOPY,
            "custom_colonoscopy_config.yaml"
        )
        
        # 5. 验证自定义配置生效
        assert colonoscopy_tool.performance.specificity == 0.99
        
        # 6. 测试高敏感性
        detection_prob = colonoscopy_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA
        )
        assert detection_prob > 0.8  # 结肠镜应该有很高的敏感性
        
        # 7. 测试质量指标
        quality_metrics = colonoscopy_tool.get_quality_metrics()
        assert "cecal_intubation_rate" in quality_metrics
        assert quality_metrics["cecal_intubation_rate"] >= 0.9
    
    def test_multi_tool_comparison(self, config_manager, sample_individual):
        """测试多工具比较"""
        # 创建多个工具
        tools = {}
        
        for tool_type in [ScreeningToolType.FIT, ScreeningToolType.COLONOSCOPY, ScreeningToolType.SIGMOIDOSCOPY]:
            # 创建模板
            if tool_type == ScreeningToolType.FIT:
                template = ScreeningToolConfigTemplates.create_fit_template()
            elif tool_type == ScreeningToolType.COLONOSCOPY:
                template = ScreeningToolConfigTemplates.create_colonoscopy_template()
            else:
                template = ScreeningToolConfigTemplates.create_sigmoidoscopy_template()
            
            # 保存配置
            config_file = f"comparison_{tool_type.value}_config.yaml"
            config_manager.save_tool_configuration(tool_type, template, config_file)
            
            # 创建工具
            tools[tool_type] = config_manager.create_tool_from_config(tool_type, config_file)
        
        # 比较检测概率
        detection_probs = {}
        for tool_type, tool in tools.items():
            prob = tool.calculate_detection_probability(
                sample_individual,
                DiseaseState.HIGH_RISK_ADENOMA,
                AnatomicalLocation.DISTAL_COLON
            )
            detection_probs[tool_type] = prob
        
        # 验证预期的敏感性顺序：结肠镜 > 乙状结肠镜 > FIT
        assert detection_probs[ScreeningToolType.COLONOSCOPY] > detection_probs[ScreeningToolType.SIGMOIDOSCOPY]
        assert detection_probs[ScreeningToolType.SIGMOIDOSCOPY] > detection_probs[ScreeningToolType.FIT]
        
        # 比较侵入性
        invasiveness_levels = {tool_type: tool.characteristics.invasiveness for tool_type, tool in tools.items()}
        assert invasiveness_levels[ScreeningToolType.FIT].value == "non_invasive"
        assert invasiveness_levels[ScreeningToolType.COLONOSCOPY].value == "invasive"
        assert invasiveness_levels[ScreeningToolType.SIGMOIDOSCOPY].value == "invasive"
    
    def test_location_specific_detection(self, config_manager):
        """测试位置特异性检测"""
        # 创建个体
        individual = Individual(birth_year=1965, gender=Gender.FEMALE)
        individual.current_disease_state = DiseaseState.HIGH_RISK_ADENOMA
        
        # 创建乙状结肠镜工具
        sigmoidoscopy_template = ScreeningToolConfigTemplates.create_sigmoidoscopy_template()
        config_manager.save_tool_configuration(
            ScreeningToolType.SIGMOIDOSCOPY,
            sigmoidoscopy_template,
            "location_test_sigmoidoscopy.yaml"
        )
        sigmoidoscopy_tool = config_manager.create_tool_from_config(
            ScreeningToolType.SIGMOIDOSCOPY,
            "location_test_sigmoidoscopy.yaml"
        )
        
        # 测试不同位置的检测能力
        locations = [
            AnatomicalLocation.PROXIMAL_COLON,
            AnatomicalLocation.DISTAL_COLON,
            AnatomicalLocation.RECTUM
        ]
        
        detection_probs = {}
        for location in locations:
            prob = sigmoidoscopy_tool.calculate_detection_probability(
                individual,
                DiseaseState.HIGH_RISK_ADENOMA,
                location
            )
            detection_probs[location] = prob
        
        # 乙状结肠镜不能检测近端结肠
        assert detection_probs[AnatomicalLocation.PROXIMAL_COLON] == 0.0
        
        # 能检测远端结肠和直肠
        assert detection_probs[AnatomicalLocation.DISTAL_COLON] > 0.0
        assert detection_probs[AnatomicalLocation.RECTUM] > 0.0
    
    def test_cost_integration(self, config_manager, sample_individual):
        """测试成本集成"""
        # 创建FIT工具配置
        fit_template = ScreeningToolConfigTemplates.create_fit_template()
        config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            fit_template,
            "cost_integration_fit.yaml"
        )
        
        # 创建成本模型
        cost_items = [
            CostItem(
                component=CostComponent.PROCEDURE_FEE,
                category=CostCategory.DIRECT_MEDICAL,
                base_cost=25.0
            ),
            CostItem(
                component=CostComponent.TIME_COST,
                category=CostCategory.INDIRECT,
                base_cost=50.0
            )
        ]
        
        cost_model = ScreeningCostModel(
            tool_type=ScreeningToolType.FIT,
            cost_items=cost_items
        )
        
        # 计算成本
        cost_breakdown = cost_model.calculate_total_cost(sample_individual)
        
        # 验证成本计算
        assert cost_breakdown["total_cost"] == 75.0
        assert cost_breakdown["procedure_fee"] == 25.0
        assert cost_breakdown["time_cost"] == 50.0
        
        # 验证类别汇总
        assert cost_breakdown["direct_medical_total"] == 25.0
        assert cost_breakdown["indirect_total"] == 50.0
    
    def test_configuration_management_workflow(self, config_manager):
        """测试配置管理工作流"""
        # 1. 列出初始配置（应该为空）
        initial_configs = config_manager.list_available_configurations()
        initial_count = len(initial_configs)
        
        # 2. 创建并保存多个配置
        tools_to_create = [
            (ScreeningToolType.FIT, "workflow_fit.yaml"),
            (ScreeningToolType.COLONOSCOPY, "workflow_colonoscopy.yaml"),
            (ScreeningToolType.SIGMOIDOSCOPY, "workflow_sigmoidoscopy.yaml")
        ]
        
        for tool_type, config_file in tools_to_create:
            if tool_type == ScreeningToolType.FIT:
                template = ScreeningToolConfigTemplates.create_fit_template()
            elif tool_type == ScreeningToolType.COLONOSCOPY:
                template = ScreeningToolConfigTemplates.create_colonoscopy_template()
            else:
                template = ScreeningToolConfigTemplates.create_sigmoidoscopy_template()
            
            config_manager.save_tool_configuration(tool_type, template, config_file)
        
        # 3. 验证配置已保存
        updated_configs = config_manager.list_available_configurations()
        assert len(updated_configs) == initial_count + 3
        
        # 4. 验证配置信息
        config_names = [config["file_name"] for config in updated_configs]
        for _, config_file in tools_to_create:
            assert config_file in config_names
        
        # 5. 测试缓存功能
        # 先清空缓存确保测试的准确性
        config_manager.clear_cache()
        cache_info_before = config_manager.get_cache_info()

        # 加载配置（应该缓存）
        for tool_type, config_file in tools_to_create:
            config_manager.load_tool_configuration(tool_type, config_file)

        cache_info_after = config_manager.get_cache_info()
        assert cache_info_after["cached_configs"] >= len(tools_to_create)
        
        # 6. 清空缓存
        config_manager.clear_cache()
        cache_info_cleared = config_manager.get_cache_info()
        assert cache_info_cleared["cached_configs"] == 0
    
    def test_error_handling_integration(self, config_manager):
        """测试错误处理集成"""
        # 1. 测试加载不存在的配置
        with pytest.raises(FileNotFoundError):
            config_manager.load_tool_configuration(
                ScreeningToolType.FIT,
                "nonexistent_config.yaml"
            )
        
        # 2. 测试无效配置
        invalid_config = {
            "invalid_structure": {
                "name": "无效配置"
            }
        }
        
        with pytest.raises(Exception):  # 应该抛出验证错误
            config_manager.save_tool_configuration(
                ScreeningToolType.FIT,
                invalid_config,
                "invalid_config.yaml"
            )
        
        # 3. 测试未注册工具类型（创建一个临时的未注册类型）
        # 由于所有枚举类型都已注册，我们测试工厂方法的错误处理
        try:
            # 测试传入None会如何处理
            ScreeningToolFactory.create_tool(None)
            assert False, "应该抛出异常"
        except (ValueError, TypeError, AttributeError):
            pass  # 预期的异常
    
    def test_performance_consistency(self, config_manager, sample_individual):
        """测试性能一致性"""
        # 创建相同配置的工具
        fit_template = ScreeningToolConfigTemplates.create_fit_template()
        
        config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            fit_template,
            "consistency_test_1.yaml"
        )
        
        config_manager.save_tool_configuration(
            ScreeningToolType.FIT,
            fit_template,
            "consistency_test_2.yaml"
        )
        
        # 创建两个工具实例
        tool1 = config_manager.create_tool_from_config(
            ScreeningToolType.FIT,
            "consistency_test_1.yaml"
        )
        
        tool2 = config_manager.create_tool_from_config(
            ScreeningToolType.FIT,
            "consistency_test_2.yaml"
        )
        
        # 验证性能一致性
        prob1 = tool1.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA
        )
        
        prob2 = tool2.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA
        )
        
        # 相同配置应该产生相同的检测概率
        assert abs(prob1 - prob2) < 0.001  # 允许微小的浮点误差
