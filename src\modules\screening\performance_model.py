"""
筛查工具性能参数建模

实现疾病阶段特异性敏感性、解剖位置调整、年龄性别影响等高级性能参数建模。
"""

import math
import random
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import logging

from src.core.enums import DiseaseState, AnatomicalLocation, Gender
from src.core.individual import Individual
from .enums import ScreeningToolType


logger = logging.getLogger(__name__)


class UncertaintyModel(Enum):
    """不确定性建模类型"""
    NONE = "none"                    # 无不确定性
    NORMAL = "normal"                # 正态分布
    BETA = "beta"                    # Beta分布
    LOGNORMAL = "lognormal"          # 对数正态分布


@dataclass
class PerformanceUncertainty:
    """性能参数不确定性配置"""
    
    model_type: UncertaintyModel = UncertaintyModel.NONE
    
    # 正态分布参数
    mean: float = 0.0
    std_dev: float = 0.0
    
    # Beta分布参数
    alpha: float = 1.0
    beta: float = 1.0
    
    # 对数正态分布参数
    mu: float = 0.0
    sigma: float = 1.0
    
    # 边界限制
    min_value: float = 0.0
    max_value: float = 1.0

    def sample(self) -> float:
        """从不确定性分布中采样"""
        if self.model_type == UncertaintyModel.NONE:
            return self.mean
        
        elif self.model_type == UncertaintyModel.NORMAL:
            value = random.normalvariate(self.mean, self.std_dev)
        
        elif self.model_type == UncertaintyModel.BETA:
            value = random.betavariate(self.alpha, self.beta)
        
        elif self.model_type == UncertaintyModel.LOGNORMAL:
            value = random.lognormvariate(self.mu, self.sigma)
        
        else:
            value = self.mean
        
        # 应用边界限制
        return max(self.min_value, min(self.max_value, value))


@dataclass
class DiseaseStatePerformance:
    """疾病状态特异性性能参数"""
    
    # 基础敏感性
    base_sensitivity: float
    
    # 不确定性模型
    uncertainty: Optional[PerformanceUncertainty] = None
    
    # 疾病严重程度调整
    severity_adjustment: float = 1.0
    
    # 病变大小影响（如适用）
    size_sensitivity_curve: Optional[Dict[str, float]] = None
    
    # 组织学类型影响
    histology_modifiers: Optional[Dict[str, float]] = None

    def calculate_effective_sensitivity(
        self,
        lesion_size_mm: Optional[float] = None,
        histology_type: Optional[str] = None,
        **kwargs
    ) -> float:
        """
        计算有效敏感性
        
        Args:
            lesion_size_mm: 病变大小（毫米）
            histology_type: 组织学类型
            **kwargs: 其他参数
            
        Returns:
            float: 有效敏感性
        """
        # 基础敏感性
        sensitivity = self.base_sensitivity
        
        # 应用不确定性
        if self.uncertainty:
            uncertainty_factor = self.uncertainty.sample()
            sensitivity *= uncertainty_factor
        
        # 应用严重程度调整
        sensitivity *= self.severity_adjustment
        
        # 应用病变大小影响
        if lesion_size_mm and self.size_sensitivity_curve:
            size_factor = self._calculate_size_factor(lesion_size_mm)
            sensitivity *= size_factor
        
        # 应用组织学类型影响
        if histology_type and self.histology_modifiers:
            histology_factor = self.histology_modifiers.get(histology_type, 1.0)
            sensitivity *= histology_factor
        
        return max(0.0, min(1.0, sensitivity))
    
    def _calculate_size_factor(self, size_mm: float) -> float:
        """根据病变大小计算敏感性调整因子"""
        if not self.size_sensitivity_curve:
            return 1.0
        
        # 简化的大小-敏感性曲线
        # 实际实现中可以使用更复杂的插值方法
        size_thresholds = sorted(self.size_sensitivity_curve.keys())
        
        for i, threshold_str in enumerate(size_thresholds):
            threshold = float(threshold_str)
            if size_mm <= threshold:
                return self.size_sensitivity_curve[threshold_str]
        
        # 如果超过最大阈值，返回最大值
        return self.size_sensitivity_curve[size_thresholds[-1]]


@dataclass
class LocationSpecificPerformance:
    """解剖位置特异性性能参数"""
    
    # 位置特异性敏感性基础值
    base_sensitivities: Dict[AnatomicalLocation, float] = field(default_factory=dict)
    
    # 位置特异性检测难度
    detection_difficulty: Dict[AnatomicalLocation, float] = field(default_factory=dict)
    
    # 位置可达性（对于内镜检查）
    accessibility: Dict[AnatomicalLocation, float] = field(default_factory=dict)
    
    # 视野质量影响
    visualization_quality: Dict[AnatomicalLocation, float] = field(default_factory=dict)

    def calculate_location_adjustment(
        self,
        location: AnatomicalLocation,
        procedure_quality: float = 1.0
    ) -> float:
        """
        计算解剖位置调整因子
        
        Args:
            location: 解剖位置
            procedure_quality: 检查质量因子
            
        Returns:
            float: 位置调整因子
        """
        # 基础敏感性
        base_factor = self.base_sensitivities.get(location, 1.0)
        
        # 检测难度调整
        difficulty_factor = 1.0 / self.detection_difficulty.get(location, 1.0)
        
        # 可达性调整
        accessibility_factor = self.accessibility.get(location, 1.0)
        
        # 视野质量调整
        visualization_factor = self.visualization_quality.get(location, 1.0)
        
        # 综合调整
        total_factor = (
            base_factor * 
            difficulty_factor * 
            accessibility_factor * 
            visualization_factor * 
            procedure_quality
        )
        
        return max(0.0, min(2.0, total_factor))


@dataclass
class DemographicAdjustments:
    """人口统计学调整参数"""
    
    # 年龄相关调整
    age_sensitivity_model: str = "linear"  # linear, exponential, sigmoid
    age_coefficients: Dict[str, float] = field(default_factory=dict)
    age_reference: float = 50.0
    
    # 性别相关调整
    gender_modifiers: Dict[Gender, float] = field(default_factory=dict)
    
    # BMI相关调整（如适用）
    bmi_sensitivity_curve: Optional[Dict[str, float]] = None
    
    # 合并症影响
    comorbidity_modifiers: Optional[Dict[str, float]] = None

    # 暂时注销年龄调整功能
    # def calculate_age_adjustment(self, age: float) -> float:
    #     """计算年龄调整因子"""
    #     age_diff = age - self.age_reference
    #
    #     if self.age_sensitivity_model == "linear":
    #         slope = self.age_coefficients.get("slope", 0.0)
    #         return 1.0 + slope * age_diff
    #
    #     elif self.age_sensitivity_model == "exponential":
    #         rate = self.age_coefficients.get("rate", 0.0)
    #         return math.exp(rate * age_diff)
    #
    #     elif self.age_sensitivity_model == "sigmoid":
    #         # Sigmoid函数：1 / (1 + exp(-k*(x-x0)))
    #         k = self.age_coefficients.get("steepness", 0.1)
    #         x0 = self.age_coefficients.get("midpoint", 65.0)
    #         return 1.0 / (1.0 + math.exp(-k * (age - x0)))
    #
    #     else:
    #         return 1.0

    def calculate_age_adjustment(self, age: float) -> float:
        """计算年龄调整因子（暂时返回1.0，不进行调整）"""
        return 1.0
    
    def calculate_gender_adjustment(self, gender: Gender) -> float:
        """计算性别调整因子"""
        return self.gender_modifiers.get(gender, 1.0)
    
    def calculate_bmi_adjustment(self, bmi: Optional[float]) -> float:
        """计算BMI调整因子"""
        if bmi is None or not self.bmi_sensitivity_curve:
            return 1.0
        
        # 简化的BMI-敏感性曲线
        bmi_ranges = sorted([float(k) for k in self.bmi_sensitivity_curve.keys()])
        
        for bmi_threshold in bmi_ranges:
            if bmi <= bmi_threshold:
                return self.bmi_sensitivity_curve[str(bmi_threshold)]
        
        # 如果超过最大阈值
        return self.bmi_sensitivity_curve[str(bmi_ranges[-1])]


class AdvancedPerformanceModel:
    """高级性能参数模型"""
    
    def __init__(
        self,
        tool_type: ScreeningToolType,
        disease_performance: Dict[DiseaseState, DiseaseStatePerformance],
        location_performance: LocationSpecificPerformance,
        demographic_adjustments: DemographicAdjustments,
        base_specificity: float = 0.95
    ):
        self.tool_type = tool_type
        self.disease_performance = disease_performance
        self.location_performance = location_performance
        self.demographic_adjustments = demographic_adjustments
        self.base_specificity = base_specificity
    
    def calculate_detection_probability(
        self,
        individual: Individual,
        disease_state: DiseaseState,
        anatomical_location: Optional[AnatomicalLocation] = None,
        **kwargs
    ) -> float:
        """
        计算综合检测概率
        
        Args:
            individual: 个体对象
            disease_state: 疾病状态
            anatomical_location: 解剖位置
            **kwargs: 其他参数
            
        Returns:
            float: 检测概率
        """
        # 获取疾病状态特异性性能
        if disease_state not in self.disease_performance:
            logger.warning(f"未配置疾病状态 {disease_state} 的性能参数")
            return 0.0
        
        disease_perf = self.disease_performance[disease_state]
        
        # 计算基础敏感性
        base_sensitivity = disease_perf.calculate_effective_sensitivity(
            lesion_size_mm=kwargs.get("lesion_size_mm"),
            histology_type=kwargs.get("histology_type")
        )
        
        # 应用解剖位置调整
        location_factor = 1.0
        if anatomical_location:
            procedure_quality = kwargs.get("procedure_quality", 1.0)
            location_factor = self.location_performance.calculate_location_adjustment(
                anatomical_location, procedure_quality
            )
        
        # 应用人口统计学调整（暂时注销年龄调整）
        # age_factor = self.demographic_adjustments.calculate_age_adjustment(
        #     individual.get_current_age()
        # )
        age_factor = 1.0  # 暂时不进行年龄调整
        gender_factor = self.demographic_adjustments.calculate_gender_adjustment(
            individual.gender
        )
        bmi_factor = self.demographic_adjustments.calculate_bmi_adjustment(
            kwargs.get("bmi")
        )
        
        # 计算最终检测概率
        final_probability = (
            base_sensitivity * 
            location_factor * 
            age_factor * 
            gender_factor * 
            bmi_factor
        )
        
        return max(0.0, min(1.0, final_probability))
    
    def calculate_specificity(
        self,
        individual: Individual,
        **kwargs
    ) -> float:
        """
        计算特异性（可根据个体特征调整）
        
        Args:
            individual: 个体对象
            **kwargs: 其他参数
            
        Returns:
            float: 特异性
        """
        # 基础特异性
        specificity = self.base_specificity
        
        # 可以根据年龄、性别等因素进行微调
        # 目前返回基础特异性
        return specificity
